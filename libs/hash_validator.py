import os
import re
from typing import List, Dict, Any, Optional, Tu<PERSON>

def is_valid_hash(hash_string: str) -> bool:
    """Kiểm tra xem chuỗi có phải là hash hợp lệ không"""
    if not hash_string or len(hash_string) < 10:
        return False
    
    # Chỉ cho phép các ký tự hex và độ dài phù hợp
    hash_pattern = re.compile(r'^[a-fA-F0-9]{32,128}$')
    return bool(hash_pattern.match(hash_string))

def sanitize_hash(hash_string: str) -> str:
    """Làm sạch giá trị hash"""
    # Loại bỏ tất cả các ký tự không phải hex
    return re.sub(r'[^a-fA-F0-9]', '', hash_string)

def is_valid_mask(mask: str) -> bool:
    """Kiểm tra xem mask có đúng định dạng không"""
    if not mask:
        return False
    
    # Mask phải chứa ít nhất một ký tự đặc biệt như ?l, ?u, ?d, ?s, ?a, ?b, hoặc ?1-?4
    mask_pattern = re.compile(r'\?[ludsab1-4]')
    return bool(mask_pattern.search(mask))

def validate_hashcat_input(data: Dict[str, Any]) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """Kiểm tra tính hợp lệ của dữ liệu đầu vào cho hashcat"""
    if not data:
        return False, "No input data provided", None
    
    # Validate required fields
    required_fields = ["hash_input", "hash_id", "attack_mode"]
    missing_fields = [field for field in required_fields if field not in data or not data[field]]
    if missing_fields:
        return False, f"Missing required fields: {', '.join(missing_fields)}", None
    
    # Validate hash_input
    hash_input = data.get("hash_input", "")
    if not hash_input:
        return False, "hash_input cannot be empty", None
    
    # Sanitize hash input to prevent command injection
    if isinstance(hash_input, list):
        sanitized_hashes = [sanitize_hash(h) for h in hash_input]
        valid_hashes = [h for h in sanitized_hashes if is_valid_hash(h)]
        
        if not valid_hashes:
            return False, "No valid hash formats found in input", None
            
        hash_input = valid_hashes
        print(f"hash_input {hash_input}")
    else:
        hash_input = sanitize_hash(hash_input)
        if not is_valid_hash(hash_input):
            return False, "Invalid hash format", None
    
    
    hash_id = data.get("hash_id")
    if not isinstance(hash_id, (str)):
        return False, "hash_id must be a string", None

    # Validate attack_mode if provided
    attack_mode = data.get("attack_mode")
    if attack_mode is not None:
        if attack_mode not in ["0", "1", "3", "6", "7", "9", 0, 1, 3, 6, 7, 9]:
            return False, "Invalid attack_mode. Supported modes: 0, 1, 3, 6, 7, 9", None
    else:
        attack_mode = "0"  # Default to dictionary attack if not specified
    
    # Convert attack_mode to int for easier comparison
    attack_mode_int = int(attack_mode)
    
    # Validate dictionary/wordlist/mask based on attack mode
    if attack_mode_int == 0:  # Dictionary attack (Straight)
        if not any(k in data for k in ["wordlist", "dictionary", "directory"]):
            return False, "Dictionary attack requires wordlist, dictionary, or directory", None
        
        # Check rule constraints: only one of rules_file or generate_rules can be used
        has_rules_file = "rules_file" in data and data["rules_file"]
        has_generate_rules = "generate_rules" in data and data["generate_rules"]
        
        if has_rules_file and has_generate_rules:
            return False, "Cannot use both rules_file and generate_rules at the same time", None
        
        # If using generate_rules, validate related parameters
        if has_generate_rules:
            # These are optional but if provided, they must be valid
            pass  # Already validated above
            
    elif attack_mode_int == 1:  # Combination attack
        # Requires two wordlists
        if not ("wordlist" in data and isinstance(data["wordlist"], list) and len(data["wordlist"]) >= 2):
            return False, "Combination attack requires two wordlists", None
        
        # rule_left and rule-right should be single rules, not files
        # (This is hard to validate completely, but we can check they're strings)
        
    elif attack_mode_int == 3:  # Brute force attack
        if "mask" not in data or not data["mask"]:
            return False, "Brute force attack requires a mask", None
        
        # Validate mask format (basic check)
        if not is_valid_mask(data["mask"]):
            return False, "Invalid mask format", None
        
        # Validate increment parameters
        if "increment_min" in data or "increment_max" in data:
            if "increment" not in data or data["increment"] is not True:
                return False, "increment_min/max requires increment mode to be enabled", None
        
    elif attack_mode_int == 6:  # Hybrid wordlist + mask
        if not any(k in data for k in ["wordlist", "dictionary"]):
            return False, "Hybrid attack requires wordlist or dictionary", None
        if "mask" not in data or not data["mask"]:
            return False, "Hybrid attack requires a mask", None
        
        # Validate mask format
        if not is_valid_mask(data["mask"]):
            return False, "Invalid mask format", None
        
        # Validate increment parameters
        if "increment_min" in data or "increment_max" in data:
            if "increment" not in data or data["increment"] is not True:
                return False, "increment_min/max requires increment mode to be enabled", None
        
    elif attack_mode_int == 7:  # Hybrid mask + wordlist
        if "mask" not in data or not data["mask"]:
            return False, "Hybrid attack requires a mask", None
        if not any(k in data for k in ["wordlist", "dictionary"]):
            return False, "Hybrid attack requires wordlist or dictionary", None
        
        # Validate mask format
        if not is_valid_mask(data["mask"]):
            return False, "Invalid mask format", None
        
        # Validate increment parameters
        if "increment_min" in data or "increment_max" in data:
            if "increment" not in data or data["increment"] is not True:
                return False, "increment_min/max requires increment mode to be enabled", None
    
    elif attack_mode_int == 9:  # Association attack
        if not any(k in data for k in ["wordlist", "dictionary"]):
            return False, "Association attack requires wordlist or dictionary", None
        if "hashlist" not in data or not data["hashlist"]:
            return False, "Association attack requires a hashlist", None
        
        # Ideally we would validate that wordlist and hashlist have the same number of lines
        # This would require reading the files, which might be expensive
        # For now, we'll skip this validation and let hashcat handle it
    
    # Validate rules_file if provided
    rules_file = data.get("rules_file")
    if rules_file:
        if not isinstance(rules_file, str):
            return False, "rules_file must be a string", None
    
    # Validate rule generation parameters
    if "generate_rules" in data:
        if not isinstance(data["generate_rules"], (int, str)):
            return False, "generate_rules must be an integer", None
        try:
            int(data["generate_rules"])
        except ValueError:
            return False, "generate_rules must be a valid integer", None
    
    if "generate_rules_func_min" in data:
        if not isinstance(data["generate_rules_func_min"], (int, str)):
            return False, "generate_rules_func_min must be a non-negative integer", None
        try:
            value = int(data["generate_rules_func_min"])
            if value < 0:
                return False, "generate_rules_func_min must be a non-negative integer", None
        except ValueError:
            return False, "generate_rules_func_min must be a valid integer", None
    
    if "generate_rules_func_max" in data:
        if not isinstance(data["generate_rules_func_max"], (int, str)):
            return False, "generate_rules_func_max must be a non-negative integer", None
        try:
            value = int(data["generate_rules_func_max"])
            if value < 0:
                return False, "generate_rules_func_max must be a non-negative integer", None
        except ValueError:
            return False, "generate_rules_func_max must be a valid integer", None
    
    if "generate_rules_func_sel" in data:
        if not isinstance(data["generate_rules_func_sel"], str):
            return False, "generate_rules_func_sel must be a string", None
    
    if "generate_rules_seed" in data:
        if not isinstance(data["generate_rules_seed"], (int, str)):
            return False, "generate_rules_seed must be an integer", None
        try:
            int(data["generate_rules_seed"])
        except ValueError:
            return False, "generate_rules_seed must be a valid integer", None
    
    # Validate rule_left and rule-right if provided
    if "rule_left" in data:
        if not isinstance(data["rule_left"], str):
            return False, "rule_left must be a string", None
    
    if "rule_right" in data:
        if not isinstance(data["rule_right"], str):
            return False, "rule_right must be a string", None
    
    # Validate custom charsets
    for i in range(1, 5):
        charset_key = f"custom_charset{i}"
        if charset_key in data:
            if not isinstance(data[charset_key], str):
                return False, f"{charset_key} must be a string", None
    
    # Validate increment parameters
    if "increment" in data and data["increment"] is True:
        # Increment mode is enabled, validate min and max if provided
        if "increment_min" in data:
            if not isinstance(data["increment_min"], (int, str)):
                return False, "increment_min must be a non-negative integer", None
            try:
                value = int(data["increment_min"])
                if value < 0:
                    return False, "increment_min must be a non-negative integer", None
            except ValueError:
                return False, "increment_min must be a valid integer", None
        
        if "increment_max" in data:
            if not isinstance(data["increment_max"], (int, str)):
                return False, "increment_max must be a non-negative integer", None
            try:
                value = int(data["increment_max"])
                if value < 0:
                    return False, "increment_max must be a non-negative integer", None
            except ValueError:
                return False, "increment_max must be a valid integer", None
            
            # If both min and max are provided, ensure max >= min
            if "increment_min" in data:
                min_val = int(data["increment_min"])
                max_val = int(data["increment_max"])
                if max_val < min_val:
                    return False, "increment_max must be greater than or equal to increment_min", None
    
    # Validate wordlist path if provided
    # if "wordlist" in data and data["wordlist"]:
    #     wordlist_path = data["wordlist"]
    #     if not os.path.exists(wordlist_path):
    #         return False, f"Wordlist file not found: {wordlist_path}", None
    
    # # Validate directory path if provided
    # if "directory" in data and data["directory"]:
    #     directory_path = data["directory"]
    #     if not os.path.isdir(directory_path):
    #         return False, f"Directory not found: {directory_path}", None
    
    # Nếu tất cả validation đều pass, trả về dữ liệu đã được sanitize
    validated_data = data.copy()
    validated_data["hash_input"] = hash_input
    
    return True, None, validated_data
