import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import datetime


@dataclass
class WordlistInfo:
    filename: str
    passwords_count: int
    bytes_count: int
    keyspace: int


@dataclass
class DeviceInfo:
    device_id: str
    device_name: str
    memory: str
    cores: str


@dataclass
class CrackedHash:
    hash_value: str
    password: str


@dataclass
class StatusUpdate:
    status: str  # Running, Exhausted, etc.
    hashcat_mode_id: str  # Renamed and split
    hashcat_mode_name: str  # Renamed and split
    hash_target: str
    time_started: str
    time_started_timestamp: Optional[float]  # Added
    time_estimated: str
    time_estimated_timestamp: Optional[float]  # Added
    kernel_feature: str
    guess_base: str
    guess_queue_status: str  # Renamed and split
    guess_queue_percent: str  # Renamed and split
    speed_value: str  # Renamed and split
    speed_unit: str  # Renamed and split
    recovered_total: int  # Added
    recovered_total_percent: float  # Added
    recovered_total_count: int
    recovered_new: int  # Added
    recovered_new_percent: float  # Added
    progress_status: str  # Changed name
    progress_percent: str  # Added field
    rejected: str
    restore_point: str
    candidate_engine: str
    candidates: str
    hardware_utilization: str


@dataclass
class HashcatResult:
    version: str
    devices: List[DeviceInfo]
    wordlists: List[WordlistInfo]
    cracked_hashes: List[CrackedHash]
    latest_status: Optional[StatusUpdate] = None
    command: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None

def parse_hashcat_output(output: str) -> HashcatResult:
    """
    Parse the output of hashcat to extract information.

    Args:
        output: String output from hashcat command

    Returns:
        HashcatResult object containing parsed information
    """

    result = HashcatResult(
        version="",
        devices=[],
        wordlists=[],
        cracked_hashes=[],
    )

    # Extract hashcat version
    version_match = re.search(r'hashcat \(v([\d.]+)\)', output)
    if version_match:
        result.version = version_match.group(1)

    # Extract command (if present)
    command_match = re.search(r'└─\$ (hashcat .+?)\n', output)
    if command_match:
        result.command = command_match.group(1)

    # Extract device information
    device_pattern = r'\* Device #(\d+): (.+?), (\d+/\d+ MB)(?: \([\d]+ MB allocatable\))?, (\d+MCU)'
    for device_match in re.finditer(device_pattern, output):
        device_id = device_match.group(1)
        device_name = device_match.group(2)
        memory = device_match.group(3)
        cores = device_match.group(4)
        result.devices.append(DeviceInfo(device_id, device_name, memory, cores))

    # Extract wordlist information
    wordlist_blocks = re.finditer(
        r'Dictionary cache (?:hit|built):\n'
        r'\* Filename\.\.: (.+?)\n'
        r'\* Passwords\.: (\d+)\n'
        r'\* Bytes\.....: (\d+)\n'
        r'\* Keyspace\.\.: (\d+)',
        output
    )

    for wordlist_match in wordlist_blocks:
        filename = wordlist_match.group(1)
        passwords_count = int(wordlist_match.group(2))
        bytes_count = int(wordlist_match.group(3))
        keyspace = int(wordlist_match.group(4))
        result.wordlists.append(WordlistInfo(filename, passwords_count, bytes_count, keyspace))

    # Extract cracked hashes
    # Strategy:
    # 1. Split output into lines
    # 2. Look for lines that contain hash:password format
    # 3. Use context and format to determine if it's a real cracked result
    lines = output.split('\n')
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        # Skip lines that are clearly status messages
        if any(line.startswith(prefix) for prefix in [
            'Session.', 'Status.', 'Hash.', 'Time.', 'Kernel.',
            'Guess.', 'Speed.', 'Progress.', 'Rejected.',
            'Restore.', 'Candidate.', 'Candidates.', 'Hardware.',
            'Dictionary cache', 'Watchdog:', 'Started:', 'Stopped:',
            '*', '[s]', 'Approaching', 'The wordlist'
        ]):
            continue
            
        # Check if line matches hash:password format
        if ':' in line:
            parts = line.split(':')
            # Must have exactly one colon for hash:password format
            if len(parts) == 2:
                hash_value = parts[0].strip()
                password = parts[1].strip()
                
                # Basic validation of hash and password
                if hash_value and password:
                    # Additional check: hash should be longer than password in most cases
                    # and shouldn't contain spaces
                    if len(hash_value) > len(password) and ' ' not in hash_value:
                        result.cracked_hashes.append(CrackedHash(hash_value, password))

    # Extract status updates - we'll focus on finding the last one
    # Completely revised regex pattern to match the actual output format
    status_pattern = (
        r'Session\.+: (.+?)\s*\n'
        r'Status\.+: (.+?)\s*\n'
        r'Hash\.Mode\.+: (\d+) \((.+?)\)\s*\n'
        r'Hash\.Target\.+: (.+?)\s*\n'
        r'Time\.Started\.+: (.+?)\s*\n'
        r'Time\.Estimated\.+: (.+?)\s*\n'
        r'Kernel\.Feature\.+: (.+?)\s*\n'
        r'Guess\.Base\.+: (.+?)\s*\n'
        r'Guess\.(?:Mod|Queue)\.+: (.+?) \((.+?)(?:%|\%)?\)\s*\n'
        r'Speed\.#\d+\.+: (.+?) (.+?) \(.+?\)(?: @ .+?)?\s*\n'
        r'Recovered\.+: (\d+)/(\d+) \((.+?)(?:%|\%)?\)(?: Digests.+?)?\s*\n'
        r'Progress\.+: (.+?)/(.+?) \((.+?)(?:%|\%)?\)\s*\n'
        r'Rejected\.+: (.+?)/(.+?) \((.+?)(?:%|\%)?\)\s*\n'
        r'Restore\.Point\.+: (.+?)/(.+?) \((.+?)(?:%|\%)?\)\s*\n'
        r'(?:Restore\.Sub\.#\d+\.+: .+?\s*\n)?'
        r'Candidate\.Engine\.+: (.+?)\s*\n'
        r'Candidates\.#\d+\.+: (.+?)\s*\n'
        r'Hardware\.Mon\.#\d+\.+: (.+?)(?:\s*\n|$)'
    )
    
    status_blocks = []
    # Try to find status blocks with the comprehensive pattern
    for match in re.finditer(status_pattern, output):
        status_blocks.append(match)
    
    # If no matches found, try a more flexible approach
    if not status_blocks:
        # Debug pattern: Find all lines that start with known hashcat status fields
        debug_pattern = r'(?:Session|Status|Hash\.Mode|Hash\.Target|Time\.Started|Time\.Estimated|Kernel\.Feature|Guess\.Base|Guess\.(?:Mod|Queue)|Speed\.#\d+|Recovered|Progress|Rejected|Restore\.Point|Candidate\.Engine|Candidates\.#\d+|Hardware\.Mon\.#\d+)\.+: (.+?)(?:\n|$)'
        
        # Simple debug output
        # print("No status blocks found with comprehensive pattern. Detailed fields found:")
        # for dm in re.finditer(debug_pattern, output):
        #     print(f"Found field value: {dm.group(1)[:50]}...")
        
        # Try a more forgiving pattern
        simplified_pattern = (
            r'Session\.+: (.+?)\n'
            r'Status\.+: (.+?)\n'
            r'Hash\.Mode\.+: (.+?) \((.+?)\)\n'
            r'Hash\.Target\.+: (.+?)\n'
        )
        
        # Just look for the beginning of a status block
        # for match in re.finditer(simplified_pattern, output):
        #     print(f"Found partial status block starting with session: {match.group(1)}")
    
    # Print total number of status blocks found

    # Find the last status update by direct regex matches
    latest_status = None
    
    # We'll find the most recent status block by looking for sections delineated by the status prompt line
    status_sections = re.split(r'\[s\]tatus \[p\]ause \[b\]ypass \[c\]heckpoint \[f\]inish \[q\]uit => ', output)
    
    # If we have multiple sections, use the last one (most recent)
    if len(status_sections) > 1:
        # Use the last section that's not empty
        for section in reversed(status_sections):
            if section.strip():
                output_section = section
                break
    else:
        output_section = output
    
    # Now extract fields from the selected section
    session_match = re.search(r'Session\.+: (.+?)(?:\r?\n)', output_section)
    status_match = re.search(r'Status\.+: (.+?)(?:\r?\n)', output_section)
    hashmode_match = re.search(r'Hash\.Mode\.+: (\d+) \((.+?)\)(?:\r?\n)', output_section)
    target_match = re.search(r'Hash\.Target\.+: (.+?)(?:\r?\n)', output_section)
    time_started_match = re.search(r'Time\.Started\.+: (.+?)(?:\r?\n)', output_section)
    time_estimated_match = re.search(r'Time\.Estimated\.+: (.+?)(?:\r?\n)', output_section)
    kernel_feature_match = re.search(r'Kernel\.Feature\.+: (.+?)(?:\r?\n)', output_section)
    guess_base_match = re.search(r'Guess\.Base\.+: (.+?)(?:\r?\n)', output_section)
    guess_queue_match = re.search(r'Guess\.(?:Mod|Queue)\.+: (.+?) \((.+?)%?\)(?:\r?\n)', output_section)
    speed_match = re.search(r'Speed\.#\d+\.+: (.+?) (.+?) \(', output_section)
    recovered_match = re.search(r'Recovered\.+: (\d+)/(\d+) \((.+?)%?\)', output_section)
    progress_match = re.search(r'Progress\.+: (.+?)/(.+?) \((.+?)%?\)', output_section)
    rejected_match = re.search(r'Rejected\.+: (.+?)/(.+?) \((.+?)%?\)', output_section)
    restore_point_match = re.search(r'Restore\.Point\.+: (.+?)/(.+?) \((.+?)%?\)', output_section)
    candidate_engine_match = re.search(r'Candidate\.Engine\.+: (.+?)(?:\r?\n)', output_section)
    candidates_match = re.search(r'Candidates\.#\d+\.+: (.+?)(?:\r?\n)', output_section)
    hardware_mon_match = re.search(r'Hardware\.Mon\.#\d+\.+: (.+?)(?:\r?\n|$)', output_section)
    
    # Check if we found the essential fields
    if (status_match and hashmode_match and target_match and time_started_match and 
            progress_match):
        
        # Get values or set defaults
        session = session_match.group(1) if session_match else "Unknown"
        status = status_match.group(1) if status_match else "Unknown"
        hashcat_mode_id = hashmode_match.group(1) if hashmode_match else "0"
        hashcat_mode_name = hashmode_match.group(2) if hashmode_match else "Unknown"
        hash_target = target_match.group(1) if target_match else "Unknown"
        time_started = time_started_match.group(1) if time_started_match else ""
        time_started_timestamp = _parse_time_to_timestamp(time_started) if time_started else None
        time_estimated = time_estimated_match.group(1) if time_estimated_match else ""
        time_estimated_timestamp = _parse_time_to_timestamp(time_estimated) if time_estimated else None
        kernel_feature = kernel_feature_match.group(1) if kernel_feature_match else "Unknown"
        guess_base = guess_base_match.group(1) if guess_base_match else "Unknown"
        
        guess_queue_status = guess_queue_match.group(1) if guess_queue_match else "Unknown"
        guess_queue_percent = guess_queue_match.group(2) if guess_queue_match else "0"
        
        speed_value = speed_match.group(1) if speed_match else "0"
        speed_unit = speed_match.group(2) if speed_match else "H/s"
        
        # Recovered information
        recovered_total = int(recovered_match.group(1)) if recovered_match else 0
        recovered_total_count = int(recovered_match.group(2)) if recovered_match else 0
        recovered_total_percent_str = recovered_match.group(3) if recovered_match else "0"
        recovered_total_percent = float(recovered_total_percent_str.replace("%", "")) if recovered_total_percent_str else 0.0
        
        # Progress information
        progress_current = progress_match.group(1) if progress_match else "0"
        progress_total = progress_match.group(2) if progress_match else "0"
        progress_percent_str = progress_match.group(3) if progress_match else "0"
        progress_percent = progress_percent_str.replace("%", "") if progress_percent_str else "0"
        
        # Other fields - handle different formats
        rejected = "0/0 (0.00%)"
        if rejected_match:
            # Handle different formats of rejected info
            if len(rejected_match.groups()) >= 3:
                rejected_current = rejected_match.group(1)
                rejected_total = rejected_match.group(2)
                rejected_percent = rejected_match.group(3)
                rejected = f"{rejected_current}/{rejected_total} ({rejected_percent}%)"
            else:
                # Direct format from output
                rejected = rejected_match.group(1)
        
        restore_point = "0/0 (0.00%)"
        if restore_point_match:
            # Handle different formats of restore point info
            if len(restore_point_match.groups()) >= 3:
                restore_current = restore_point_match.group(1)
                restore_total = restore_point_match.group(2)
                restore_percent = restore_point_match.group(3)
                restore_point = f"{restore_current}/{restore_total} ({restore_percent}%)"
            else:
                # Direct format from output
                restore_point = restore_point_match.group(1)
        
        candidate_engine = candidate_engine_match.group(1) if candidate_engine_match else "Unknown"
        candidates = candidates_match.group(1) if candidates_match else "Unknown"
        hardware_util = hardware_mon_match.group(1) if hardware_mon_match else "Unknown"
        
        # Print debug information for verification
        print(f"Found status: {status}")
        print(f"Hash Mode: {hashcat_mode_id} ({hashcat_mode_name})")
        print(f"Target: {hash_target}")
        print(f"Progress: {progress_current}/{progress_total} ({progress_percent}%)")
        
        # Create a new status update
        latest_status = StatusUpdate(
            status=status,
            hashcat_mode_id=hashcat_mode_id,
            hashcat_mode_name=hashcat_mode_name,
            hash_target=hash_target,
            time_started=time_started,
            time_started_timestamp=time_started_timestamp,
            time_estimated=time_estimated,
            time_estimated_timestamp=time_estimated_timestamp,
            kernel_feature=kernel_feature,
            guess_base=guess_base,
            guess_queue_status=guess_queue_status,
            guess_queue_percent=guess_queue_percent,
            speed_value=speed_value,
            speed_unit=speed_unit,
            recovered_total=recovered_total,
            recovered_total_count=recovered_total_count,
            recovered_total_percent=recovered_total_percent,
            recovered_new=0,  # Default value when not in the output
            recovered_new_percent=0.0,  # Default value when not in the output
            progress_status=f"{progress_current}/{progress_total}",
            progress_percent=progress_percent,
            rejected=rejected,
            restore_point=restore_point,
            candidate_engine=candidate_engine,
            candidates=candidates,
            hardware_utilization=hardware_util
        )

    result.latest_status = latest_status

    # Extract start/end time
    started_match = re.search(r'Started: (.+?)(?:\n|$)', output)
    stopped_match = re.search(r'Stopped: (.+?)(?:\n|$)', output)

    if started_match:
        result.start_time = started_match.group(1)
    if stopped_match:
        result.end_time = stopped_match.group(1)

    return result


def format_hashcat_result(result: HashcatResult) -> Dict[str, Any]:
    """
    Convert HashcatResult to a dictionary format for reporting.

    Args:
        result: The parsed HashcatResult object

    Returns:
        Dictionary with formatted results
    """

    formatted = {
        "version": result.version,
        "command": result.command,
        "devices": [
            {
                "id": device.device_id,
                "name": device.device_name,
                "memory": device.memory,
                "cores": device.cores
            }
            for device in result.devices
        ],
        "wordlists": [
            {
                "filename": wl.filename,
                "passwords_count": wl.passwords_count,
                "bytes_count": wl.bytes_count,
                "keyspace": wl.keyspace
            }
            for wl in result.wordlists
        ],
        "cracked_hashes": [
            {
                "hash": ch.hash_value,
                "password": ch.password
            }
            for ch in result.cracked_hashes
        ],
        "start_time": result.start_time,
        "end_time": result.end_time,
    }

    if result.latest_status:
        status = result.latest_status
        formatted["status"] = {
            "state": status.status,
            "hashcat_mode_id": status.hashcat_mode_id,  # Use split values
            "hashcat_mode_name": status.hashcat_mode_name,  # Use split values
            "hash_target": status.hash_target,
            "time_started": status.time_started,
            "time_started_timestamp": status.time_started_timestamp,
            "time_estimated": status.time_estimated,
            "time_estimated_timestamp": status.time_estimated_timestamp,
            "kernel_feature": status.kernel_feature,
            "guess_base": status.guess_base,
            "guess_queue_status": status.guess_queue_status,  # Use split values
            "guess_queue_percent": status.guess_queue_percent,  # Use split values
            "speed_value": status.speed_value,  # Use split values
            "speed_unit": status.speed_unit,  # Use split values
            "recovered_total": status.recovered_total,
            "recovered_total_percent": status.recovered_total_percent,
            "recovered_total_count": status.recovered_total_count,
            "recovered_new": status.recovered_new,
            "recovered_new_percent": status.recovered_new_percent,
            "progress_status": status.progress_status,  # Use split values
            "progress_percent": status.progress_percent,  # Use split values
            "rejected": status.rejected,
            "restore_point": status.restore_point,
            "candidate_engine": status.candidate_engine,
            "candidates": status.candidates,
            "hardware_utilization": status.hardware_utilization
        }

    return formatted


def parse_hashcat_output_file(output_file_path: str) -> Dict[str, Any]:
    """
    Parse hashcat output file to extract cracked hashes

    Args:
        output_file_path: Path to the hashcat output file

    Returns:
        Dictionary with cracked hashes
    """
    import os

    if not os.path.exists(output_file_path):
        return {"cracked_hashes": []}

    cracked_hashes = []

    try:
        with open(output_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                # Hashcat output file format is typically hash:password
                if ':' in line:
                    parts = line.split(':', 1)  # Split only on first colon
                    if len(parts) == 2:
                        hash_value = parts[0].strip()
                        password = parts[1].strip()

                        # Basic validation
                        if hash_value and password:
                            cracked_hashes.append({
                                "hash": hash_value,
                                "password": password
                            })

    except Exception as e:
        print(f"Error reading output file {output_file_path}: {e}")
        return {"cracked_hashes": []}

    return {"cracked_hashes": cracked_hashes}


def process_hashcat_output(output_text: str) -> Dict[str, Any]:
    """
    Process hashcat output text and return results in dictionary format

    Args:
        output_text: Raw output from hashcat command

    Returns:
        Dictionary with parsed information
    """

    parsed_result = parse_hashcat_output(output_text)
    print(parsed_result)
    return format_hashcat_result(parsed_result)


def _parse_time_to_timestamp(time_str: str) -> Optional[float]:
    """
    Attempt to parse a time string from hashcat output into a Unix timestamp.
    Handles variations in time string formats.

    Args:
        time_str: The time string from hashcat output (e.g., "Thu May  1 05:30:58 2025 (1 sec)", "Thu May  1 05:30:58 2025")

    Returns:
        Unix timestamp (float) if parsing is successful, None otherwise.
    """
    # Remove any trailing time information in parentheses
    time_str = re.sub(r'\s*\(.+?\)$', '', time_str).strip()

    formats_to_try = [
        "%a %b  %d %H:%M:%S %Y",  # e.g., "Thu May  1 05:30:58 2025"
        "%a %b %d %H:%M:%S %Y"   # e.g., "Thu May 01 05:30:58 2025" (single digit day)
    ]

    for fmt in formats_to_try:
        try:
            dt_object = datetime.datetime.strptime(time_str, fmt)
            return dt_object.timestamp()
        except ValueError:
            continue  # Try the next format

    return None  # Return None if no format matches