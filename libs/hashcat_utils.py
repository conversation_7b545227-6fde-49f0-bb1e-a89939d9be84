import os
import re
import fcntl
import logging
import subprocess
import threading
import time
from typing import List, Dict, Any, Optional, Tuple
from libs.hashcat_parser import process_hashcat_output, parse_hashcat_output
import traceback
import select
from libs.mongo_client import MongoDBClient

# Biến toàn cục để lưu trữ các phiên hashcat
sessions = {}

def build_hashcat_command(session_id: str, data: dict) -> List[str]:
    """Xây dựng lệnh hashcat từ dữ liệu JSON"""
    cmd = ["hashcat"]
    base_dir = "./hashcat_tmp"
    os.makedirs(base_dir, exist_ok=True)

    # C<PERSON><PERSON> tùy chọn yêu cầu giá trị
    options_with_value = {
        "hash_type": "-m",
        "attack_mode": "-a",
        "status_timer": "--status-timer",
        "stdin_timeout_abort": "--stdin-timeout-abort",
        "markov_hcstat2": "--markov-hcstat2",
        "markov_threshold": "-t",
        "runtime": "--runtime",
        "session": "--session",
        "restore_file_path": "--restore-file-path",
        "outfile": "-o",
        "outfile_format": "--outfile-format",
        "outfile_check_timer": "--outfile-check-timer",
        "separator": "-p",
        "remove_timer": "--remove-timer",
        "potfile_path": "--potfile-path",
        "encoding_from": "--encoding-from",
        "encoding_to": "--encoding-to",
        "debug_mode": "--debug-mode",
        "debug_file": "--debug-file",
        "induction_dir": "--induction-dir",
        "outfile_check_dir": "--outfile-check-dir",
        "hccapx_message_pair": "--hccapx-message-pair",
        "nonce_error_corrections": "--nonce-error-corrections",
        "keyboard_layout_mapping": "--keyboard-layout-mapping",
        "truecrypt_keyfiles": "--truecrypt-keyfiles",
        "veracrypt_keyfiles": "--veracrypt-keyfiles",
        "veracrypt_pim_start": "--veracrypt-pim-start",
        "veracrypt_pim_stop": "--veracrypt-pim-stop",
        "segment_size": "-c",
        "bitmap_min": "--bitmap-min",
        "bitmap_max": "--bitmap-max",
        "cpu_affinity": "--cpu-affinity",
        "hook_threads": "--hook-threads",
        "backend_devices": "-d",
        "opencl_device_types": "-D",
        "workload_profile": "-w",
        "kernel_accel": "-n",
        "kernel_loops": "-u",
        "kernel_threads": "-T",
        "backend_vector_width": "--backend-vector-width",
        "spin_damp": "--spin-damp",
        "hwmon_temp_abort": "--hwmon-temp-abort",
        "rules_file": "-r",  # Thêm tùy chọn rules_file
        # Thêm các tùy chọn generate rules
        "generate_rules": "-g",
        "generate_rules_func_min": "--generate-rules-func-min",
        "generate_rules_func_max": "--generate-rules-func-max",
        "generate_rules_func_sel": "--generate-rules-func-sel",
        "generate_rules_seed": "--generate-rules-seed",
        "rule_left": "-j",
        "rule_right": "-k",
        "custom_charset1": "-1",
        "custom_charset2": "-2",
        "custom_charset3": "-3",
        "custom_charset4": "-4",
        "increment_min": "--increment_min",
        "increment_max": "--increment_max",
    }

    # Các tùy chọn không cần giá trị (boolean)
    boolean_options = {
        "quiet": "--quiet",
        "hex_charset": "--hex-charset",
        "hex_salt": "--hex-salt",
        "hex_wordlist": "--hex-wordlist",
        "force": "--force",
        "deprecated_check_disable": "--deprecated-check-disable",
        "status": "--status",
        "status_json": "--status-json",
        "machine_readable": "--machine-readable",
        "keep_guessing": "--keep-guessing",
        "self_test_disable": "--self-test-disable",
        "loopback": "--loopback",
        "markov_disable": "--markov-disable",
        "markov_classic": "--markov-classic",
        "markov_inverse": "--markov-inverse",
        "restore": "--restore",
        "restore_disable": "--restore-disable",
        "outfile_autohex_disable": "--outfile-autohex-disable",
        "wordlist_autohex_disable": "--wordlist-autohex-disable",
        "stdout": "--stdout",
        "show": "--show",
        "left": "--left",
        "username": "--username",
        "remove": "--remove",
        "potfile_disable": "--potfile-disable",
        "logfile_disable": "--logfile-disable",
        "benchmark": "-b",
        "benchmark_all": "--benchmark-all",
        "speed_only": "--speed-only",
        "progress_only": "--progress-only",
        "backend_ignore_cuda": "--backend-ignore-cuda",
        "backend_ignore_hip": "--backend-ignore-hip",
        "backend_ignore_metal": "--backend-ignore-metal",
        "backend_ignore_opencl": "--backend-ignore-opencl",
        "backend_info": "-I",
        "optimized_kernel_enable": "-O",
        "multiply_accel_disable": "-M",
        "hwmon_disable": "--hwmon-disable",
        "increment": "-i",
        "slow_candidates": "-S",
        "brain_server": "--brain-server",
        "brain_client": "-z",
        "identify": "--identify",
    }

    # Xử lý các tùy chọn có giá trị
    for key, flag in options_with_value.items():
        if key in data and data[key] is not None and data[key] != '':
            cmd.extend([flag, str(data[key])])

    # Xử lý các tùy chọn boolean
    for key, flag in boolean_options.items():
        if key in data and (data[key] is True or "true" in data[key].lower()):
            cmd.append(flag)

    # Xử lý hash hoặc hashfile
    hash_input = data.get("hash_input")
    if not hash_input:
        raise ValueError("hash_input (hash, hashfile, or hccapxfile) is required")
    
    hash_file_path = os.path.join(base_dir, f"hash_{session_id}.txt")
    with open(hash_file_path, "w") as f:
        if isinstance(hash_input, list):
            # Write each hash on a separate line
            f.write('\n'.join(hash_input))
        else:
            # Handle single hash as a string
            f.write(hash_input)
    cmd.append(hash_file_path)

    # Lấy attack mode từ data hoặc sử dụng default là dictionary attack (0)
    attack_mode = data.get("attack_mode", "0")
    
    # Xử lý wordlist (dictionary), mask, hoặc directory tùy theo attack mode
    if attack_mode == "0" or attack_mode == 0:  # Dictionary attack
        if "wordlist" in data and data["wordlist"]:
            cmd.extend(data["wordlist"])
        elif "dictionary" in data and data["dictionary"]:
            dict_path = os.path.join(base_dir, f"dict_{session_id}.txt")
            with open(dict_path, "w") as f:
                f.write(data["dictionary"])
            cmd.append(dict_path)
        elif "directory" in data and data["directory"]:
            cmd.append(data["directory"])
        else:
            raise ValueError("Dictionary attack requires a wordlist, dictionary, or directory input")
    
    elif attack_mode == "3" or attack_mode == 3:  # Brute force attack (mask)
        if "mask" in data and data["mask"]:
            cmd.append(data["mask"])
        else:
            raise ValueError("Brute force attack requires a mask input")
    
    elif attack_mode == "6" or attack_mode == 6:  # Hybrid wordlist + mask
        if ("wordlist" in data and data["wordlist"]) or ("dictionary" in data and data["dictionary"]):
            if "wordlist" in data and data["wordlist"]:
                wordlist_path = data["wordlist"]
                cmd.extend(wordlist_path)
            else:
                dict_path = os.path.join(base_dir, f"dict_{session_id}.txt")
                with open(dict_path, "w") as f:
                    f.write(data["dictionary"])
                cmd.append(dict_path)
            
            if "mask" in data and data["mask"]:
                cmd.append(data["mask"])
            else:
                raise ValueError("Hybrid attack requires both a wordlist/dictionary and a mask")
        else:
            raise ValueError("Hybrid attack requires both a wordlist/dictionary and a mask")
    
    elif attack_mode == "7" or attack_mode == 7:  # Hybrid mask + wordlist
        if "mask" in data and data["mask"]:
            cmd.append(data["mask"])
        else:
            raise ValueError("Hybrid attack requires a mask")
        
        if "wordlist" in data and data["wordlist"]:
            wordlist_path = data["wordlist"]
            cmd.extend(wordlist_path)
        elif "dictionary" in data and data["dictionary"]:
            dict_path = os.path.join(base_dir, f"dict_{session_id}.txt")
            with open(dict_path, "w") as f:
                f.write(data["dictionary"])
            cmd.append(dict_path)
        else:
            raise ValueError("Hybrid attack requires both a mask and a wordlist/dictionary")
    cmd.append("--status")
    cmd.append("--status-timer=3")
    
    return cmd

def run_hashcat(task_id: str, session_id: str, cmd: list, hash_input, hash_type):
    """Chạy hashcat và cập nhật kết quả trực tiếp vào database"""
    from libs.mongo_client import MongoDBClient
    from libs.hashcat_parser import process_hashcat_output
    
    db_client = MongoDBClient()
    
    try:
        # Start hashcat process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,  # Trả về str thay vì bytes
            bufsize=1   # Line buffering
        )
        
        # Cập nhật task với PID
        db_client.update_hashcat_task(
            task_id=task_id,
            update_data={"pid": process.pid}
        )
        
        # Set up non-blocking reads
        fd = process.stdout.fileno()
        fl = fcntl.fcntl(fd, fcntl.F_GETFL)
        fcntl.fcntl(fd, fcntl.F_SETFL, fl | os.O_NONBLOCK)
        
        # Buffer for live output
        live_output = ""
        last_update_time = time.time()
        
        # Poll process output
        while process.poll() is None:
            try:
                output = process.stdout.readline()
                if output:
                    live_output += output
                    
                    # Update database periodically (every 3 seconds) to avoid too many writes
                    current_time = time.time()
                    if current_time - last_update_time > 3:
                        # Parse output to get progress
                        parsed_result = process_hashcat_output( live_output)
                        
                        # Extract newly cracked hashes
                        newly_cracked = []
                        if parsed_result and "cracked_hashes" in parsed_result:
                            newly_cracked = [
                                {"hash": h["hash"], "password": h["password"], "source": "newly_cracked"}
                                for h in parsed_result.get("cracked_hashes", [])
                            ]
                        
                        # Get current task to access already_cracked hashes
                        current_task = db_client.get_hashcat_task(task_id)
                        already_cracked = current_task.get("results", {}).get("already_cracked", [])
                        
                        # Combine all cracked hashes
                        all_cracked = already_cracked + newly_cracked
                        
                        # Update remaining hashes
                        remaining_hashes = []
                        if isinstance(hash_input, list):
                            # Create a set of all cracked hash values for faster lookup
                            cracked_hash_values = {h["hash"] for h in all_cracked}
                            
                            # Filter out hashes that have been cracked
                            remaining_hashes = [h for h in hash_input if h not in cracked_hash_values]
                        
                        # Update task with latest output and details
                        remaining_results = [{"hash": h, "password": "", "source": "remaining_hashes"} for h in remaining_hashes]
                        db_client.update_hashcat_task(
                            task_id=task_id,
                            update_data={
                                "raw_output": live_output,
                                "details": parsed_result,
                                "results": {
                                    "all": all_cracked + remaining_results,
                                    "already_cracked": already_cracked,
                                    "newly_cracked": newly_cracked,
                                    "remaining_hashes": remaining_hashes,
                                    "remaining_to_crack": len(remaining_hashes)
                                }
                            }
                        )
                        last_update_time = current_time
                
                # Don't hog the CPU
                time.sleep(0.1)
                
            except (IOError, OSError) as e:
                # Resource temporarily unavailable, just continue
                time.sleep(0.1)
                continue
        
        # Process has finished, get any remaining output
        remaining_output = process.stdout.read()
        if remaining_output:
            live_output += remaining_output
        
        # Get stderr output if there was an error
        if process.returncode != 0:
            error_output = process.stderr.read()
            if error_output:
                live_output += "\nERROR: " + error_output
        
        # Parse final output
        parsed_result = process_hashcat_output(live_output)
        
        # Update status based on return code
        final_status = "Completed" if process.returncode == 0 else "Failed"
        
        # Extract newly cracked hashes
        newly_cracked = []
        if parsed_result and "cracked_hashes" in parsed_result:
            newly_cracked = [
                {"hash": h["hash"], "password": h["password"], "source": "newly_cracked"}
                for h in parsed_result.get("cracked_hashes", [])
            ]
        
        # Get current task to access already_cracked hashes
        current_task = db_client.get_hashcat_task(task_id)
        already_cracked = current_task.get("results", {}).get("already_cracked", [])
        
        # Combine all cracked hashes
        all_cracked = already_cracked + newly_cracked
        
        # Update remaining hashes
        remaining_hashes = []
        if isinstance(hash_input, list):
            # Create a set of all cracked hash values for faster lookup
            cracked_hash_values = {h["hash"] for h in all_cracked}
            
            # Filter out hashes that have been cracked
            remaining_hashes = [h for h in hash_input if h not in cracked_hash_values]
        remaining_results = [{"hash": h, "password": "", "source": "remaining_hashes"} for h in remaining_hashes]
        # Update task with final results
        db_client.update_hashcat_task(
            task_id=task_id,
            update_data={
                "status": final_status,
                "end_time": time.time(),
                "raw_output": live_output,
                "details": parsed_result,
                "results": {
                    "all": all_cracked + remaining_results,
                    "already_cracked": already_cracked,
                    "newly_cracked": newly_cracked,
                    "remaining_hashes": remaining_hashes,
                    "remaining_to_crack": len(remaining_hashes)
                }
            }
        )
            
    except Exception as e:
        logging.error(f"Error in run_hashcat_db: {str(e)}")
        
        # Update task with error
        db_client.update_hashcat_task(
            task_id=task_id,
            update_data={
                "status": "Failed",
                "end_time": time.time(),
                "raw_output": f"Error: {str(e)}\n{traceback.format_exc()}"
            }
        )

# Dictionary để lưu trạng thái các phiên hashcat
sessions = {}
