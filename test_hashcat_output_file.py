#!/usr/bin/env python3
"""
Test script để kiểm tra việc sử dụng output file trong hashcat
"""

import os
import tempfile
import uuid
from libs.hashcat_utils import build_hashcat_command
from libs.hashcat_parser import parse_hashcat_output_file

def test_build_hashcat_command_with_output_file():
    """Test xem build_hashcat_command có tạo output file đúng không"""
    print("Testing build_hashcat_command with output file...")
    
    session_id = str(uuid.uuid4())
    test_data = {
        "hash_input": "5d41402abc4b2a76b9719d911017c592",  # MD5 hash của "hello"
        "hash_type": "0",  # MD5
        "attack_mode": "0",  # Dictionary attack
        "dictionary": "hello\nworld\npassword\n123456"
    }
    
    try:
        cmd, output_file = build_hashcat_command(session_id, test_data)
        
        print(f"Command: {' '.join(cmd)}")
        print(f"Output file: {output_file}")
        
        # Ki<PERSON>m tra xem -o có trong command không
        assert "-o" in cmd, "Command should contain -o option"
        
        # Kiểm tra xem output file có đúng format không
        expected_output_file = f"./hashcat_tmp/output_{session_id}.txt"
        assert output_file == expected_output_file, f"Expected {expected_output_file}, got {output_file}"
        
        # Kiểm tra xem output file có trong command không
        output_index = cmd.index("-o")
        assert cmd[output_index + 1] == output_file, "Output file should be after -o option"
        
        print("✓ build_hashcat_command test passed!")
        return True
        
    except Exception as e:
        print(f"✗ build_hashcat_command test failed: {e}")
        return False

def test_parse_hashcat_output_file():
    """Test xem parse_hashcat_output_file có hoạt động đúng không"""
    print("\nTesting parse_hashcat_output_file...")
    
    # Tạo file tạm với nội dung giả lập
    test_content = """5d41402abc4b2a76b9719d911017c592:hello
098f6bcd4621d373cade4e832627b4f6:test
5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8:password
"""
    
    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write(test_content)
            temp_file_path = f.name
        
        # Parse file
        result = parse_hashcat_output_file(temp_file_path)
        
        print(f"Parsed result: {result}")
        
        # Kiểm tra kết quả
        assert "cracked_hashes" in result, "Result should contain cracked_hashes"
        cracked_hashes = result["cracked_hashes"]
        assert len(cracked_hashes) == 3, f"Expected 3 cracked hashes, got {len(cracked_hashes)}"
        
        # Kiểm tra từng hash
        expected_hashes = [
            {"hash": "5d41402abc4b2a76b9719d911017c592", "password": "hello"},
            {"hash": "098f6bcd4621d373cade4e832627b4f6", "password": "test"},
            {"hash": "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8", "password": "password"}
        ]
        
        for i, expected in enumerate(expected_hashes):
            assert cracked_hashes[i]["hash"] == expected["hash"], f"Hash mismatch at index {i}"
            assert cracked_hashes[i]["password"] == expected["password"], f"Password mismatch at index {i}"
        
        print("✓ parse_hashcat_output_file test passed!")
        return True
        
    except Exception as e:
        print(f"✗ parse_hashcat_output_file test failed: {e}")
        return False
    finally:
        # Cleanup
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass

def test_parse_empty_output_file():
    """Test xem parse_hashcat_output_file có xử lý file rỗng đúng không"""
    print("\nTesting parse_hashcat_output_file with empty file...")
    
    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("")  # Empty file
            temp_file_path = f.name
        
        result = parse_hashcat_output_file(temp_file_path)
        
        assert "cracked_hashes" in result, "Result should contain cracked_hashes"
        assert len(result["cracked_hashes"]) == 0, "Empty file should return empty cracked_hashes"
        
        print("✓ Empty file test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Empty file test failed: {e}")
        return False
    finally:
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass

def test_parse_nonexistent_file():
    """Test xem parse_hashcat_output_file có xử lý file không tồn tại đúng không"""
    print("\nTesting parse_hashcat_output_file with non-existent file...")
    
    try:
        result = parse_hashcat_output_file("/path/that/does/not/exist.txt")
        
        assert "cracked_hashes" in result, "Result should contain cracked_hashes"
        assert len(result["cracked_hashes"]) == 0, "Non-existent file should return empty cracked_hashes"
        
        print("✓ Non-existent file test passed!")
        return True
        
    except Exception as e:
        print(f"✗ Non-existent file test failed: {e}")
        return False

if __name__ == "__main__":
    print("Running hashcat output file tests...\n")
    
    tests = [
        test_build_hashcat_command_with_output_file,
        test_parse_hashcat_output_file,
        test_parse_empty_output_file,
        test_parse_nonexistent_file
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
        exit(1)
