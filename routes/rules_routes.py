from flask import Blueprint, request, jsonify, send_file
from libs.mongo_client import MongoDBClient
from werkzeug.utils import secure_filename
import os
from config import Config
import hashlib
from bson import ObjectId
from flasgger import swag_from
from datetime import datetime

rules_bp = Blueprint('rules', __name__)
db_client = MongoDBClient()

# Đ<PERSON><PERSON> bảo thư mục rules tồn tại
RULES_FOLDER = os.path.join(Config.UPLOAD_FOLDER, "rules")
if not os.path.exists(RULES_FOLDER):
    os.makedirs(RULES_FOLDER)

def calculate_file_hash(file_path):
    """Tính hash SHA-256 của file"""
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

@rules_bp.route("/rules", methods=["GET"])
@swag_from('../docs/rules/get_rules.yml')
def get_rules():
    """API để lấy tất cả hashcat rules"""
    try:
        rules = db_client.get_rules()
        
        formatted_rules = [{
            'id': str(rule['_id']),
            'name': rule['name'],
            'filename': rule['filename'],
            'description': rule.get('description', ''),
            'rule_count': rule.get('rule_count', 0),
            'created_at': rule.get('created_at', '')
        } for rule in rules]
        
        return jsonify(formatted_rules)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@rules_bp.route("/rules/<rule_id>", methods=["GET"])
@swag_from('../docs/rules/get_rule.yml')
def get_rule(rule_id):
    """API để lấy thông tin chi tiết về một rule cụ thể"""
    try:
        rule = db_client.get_rule_by_id(ObjectId(rule_id))
        if not rule:
            return jsonify({"error": "Rule not found"}), 404
            
        return jsonify({
            'id': str(rule['_id']),
            'name': rule['name'],
            'filename': rule['filename'],
            'description': rule.get('description', ''),
            'rule_count': rule.get('rule_count', 0),
            'created_at': rule.get('created_at', '')
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@rules_bp.route("/rules/download/<rule_id>", methods=["GET"])
@swag_from('../docs/rules/download_rule.yml')
def download_rule(rule_id):
    """API để tải xuống file rule"""
    try:
        rule = db_client.get_rule_by_id(ObjectId(rule_id))
        if not rule:
            return jsonify({"error": "Rule not found"}), 404
            
        if not os.path.exists(rule["filepath"]):
            return jsonify({"error": "Rule file not found"}), 404
            
        return send_file(rule["filepath"], as_attachment=True, 
                         download_name=rule["filename"])
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@rules_bp.route("/rules/upload", methods=["POST"])
@swag_from('../docs/rules/upload_rule.yml')
def upload_rule():
    """API để upload file rule của hashcat"""
    if "rule_file" not in request.files:
        return jsonify({"error": "No rule file uploaded"}), 400

    file = request.files["rule_file"]
    name = request.form.get("name", "")
    description = request.form.get("description", "")

    if not name:
        return jsonify({"error": "Rule name is required"}), 400

    if file.filename == '':
        return jsonify({"error": "No file selected"}), 400

    temp_filepath = os.path.join(RULES_FOLDER, "temp_" + secure_filename(file.filename))
    try:
        file.save(temp_filepath)
        
        # Kiểm tra xem file có phải là file rule hợp lệ không
        try:
            with open(temp_filepath, "r", encoding="utf-8", errors="ignore") as f:
                rule_lines = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]
                rule_count = len(rule_lines)
        except Exception as e:
            os.remove(temp_filepath)
            return jsonify({"error": f"Invalid rule file: {str(e)}"}), 400
        
        file_hash = calculate_file_hash(temp_filepath)
        
        # Kiểm tra trùng lặp
        existing_rule = db_client.get_rule_by_hash(file_hash)
        if existing_rule:
            os.remove(temp_filepath)
            return jsonify({
                "error": "Duplicate rule file",
                "existing_rule": {
                    "id": str(existing_rule["_id"]),
                    "name": existing_rule["name"],
                    "filename": existing_rule["filename"]
                }
            }), 409
        
        filename = secure_filename(file.filename)
        filepath = os.path.join(RULES_FOLDER, filename)
        
        # Nếu tên file đã tồn tại, thêm timestamp vào tên file
        if os.path.exists(filepath):
            name_parts = os.path.splitext(filename)
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"{name_parts[0]}_{timestamp}{name_parts[1]}"
            filepath = os.path.join(RULES_FOLDER, filename)
        
        os.rename(temp_filepath, filepath)
        
        rule_id = db_client.add_rule(
            name=name,
            filename=filename,
            filepath=filepath,
            description=description,
            rule_count=rule_count,
            file_hash=file_hash
        )
        
        return jsonify({
            "id": str(rule_id),
            "name": name,
            "filename": filename,
            "description": description,
            "rule_count": rule_count
        })
    except Exception as e:
        if os.path.exists(temp_filepath):
            os.remove(temp_filepath)
        return jsonify({"error": str(e)}), 500

@rules_bp.route("/rules/<rule_id>", methods=["PUT"])
@swag_from('../docs/rules/update_rule.yml')
def update_rule(rule_id):
    """API để cập nhật thông tin rule"""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
            
        name = data.get("name")
        description = data.get("description")
        
        if not name and not description:
            return jsonify({"error": "Nothing to update"}), 400
            
        update_data = {}
        if name:
            update_data["name"] = name
        if description:
            update_data["description"] = description
            
        success = db_client.update_rule(ObjectId(rule_id), update_data)
        
        if success:
            return jsonify({"message": "Rule updated successfully"})
        return jsonify({"error": "Rule not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@rules_bp.route("/rules/<rule_id>", methods=["DELETE"])
@swag_from('../docs/rules/delete_rule.yml')
def delete_rule(rule_id):
    """API để xóa một rule"""
    try:
        rule = db_client.get_rule_by_id(ObjectId(rule_id))
        if not rule:
            return jsonify({"error": "Rule not found"}), 404

        # Xóa file vật lý
        if os.path.exists(rule["filepath"]):
            os.remove(rule["filepath"])

        # Xóa bản ghi trong database
        success = db_client.delete_rule(ObjectId(rule_id))
        
        if success:
            return jsonify({"message": "Rule deleted successfully"})
        return jsonify({"error": "Rule not found"}), 404
    except Exception as e:
        return jsonify({"error": str(e)}), 500