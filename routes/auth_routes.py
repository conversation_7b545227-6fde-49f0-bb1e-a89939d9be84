from flask import Blueprint, request, jsonify, session, redirect, url_for
from werkzeug.security import generate_password_hash, check_password_hash
from libs.mongo_client import MongoDBClient
from functools import wraps
from bson import ObjectId
import jwt
import datetime
from config import Config
from flasgger import swag_from

auth_bp = Blueprint('auth', __name__)
db_client = MongoDBClient()

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            token = request.headers['Authorization'].split(" ")[1]
        
        if not token:
            return jsonify({'error': 'Token is missing!'}), 401
        
        try:
            data = jwt.decode(token, Config.SECRET_KEY, algorithms=["HS256"])
            current_user = db_client.get_user_by_id(ObjectId(data['user_id']))
            if not current_user:
                return jsonify({'error': 'Invalid token!'}), 401
        except:
            return jsonify({'error': 'Invalid token!'}), 401
        
        return f(current_user, *args, **kwargs)
    
    return decorated

def admin_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            token = request.headers['Authorization'].split(" ")[1]
        
        if not token:
            return jsonify({'error': 'Token is missing!'}), 401
        
        try:
            data = jwt.decode(token, Config.SECRET_KEY, algorithms=["HS256"])
            current_user = db_client.get_user_by_id(ObjectId(data['user_id']))
            if not current_user or current_user['role'] != 'admin':
                return jsonify({'error': 'Admin privileges required!'}), 403
        except:
            return jsonify({'error': 'Invalid token!'}), 401
        
        return f(current_user, *args, **kwargs)
    
    return decorated

@auth_bp.route('/login', methods=['POST'])
@swag_from('../docs/auth/login.yml')
def login():
    """API đăng nhập người dùng"""
    data = request.json
    
    if not data or not data.get('username') or not data.get('password'):
        return jsonify({'error': 'Username and password are required!'}), 400
    
    user = db_client.get_user_by_username(data.get('username'))
    
    if not user:
        return jsonify({'error': 'Invalid username or password!'}), 401
    
    if user.get('status') == 'disabled':
        return jsonify({'error': 'Account is disabled!'}), 403
    
    if check_password_hash(user['password'], data.get('password')):
        # Update last_connect timestamp
        db_client.update_user(user['_id'], {'last_connect': datetime.datetime.utcnow()})
        
        token = jwt.encode({
            'user_id': str(user['_id']),
            'username': user['username'],
            'role': user['role'],
            'permissions': user.get('permissions', []),
            'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
        }, Config.SECRET_KEY, algorithm="HS256")
        
        return jsonify({
            'token': token,
            'user': {
                'id': str(user['_id']),
                'username': user['username'],
                'role': user['role'],
                'permissions': user.get('permissions', [])
            }
        })
    
    return jsonify({'error': 'Invalid username or password!'}), 401

@auth_bp.route('/user/add', methods=['POST'])
@admin_required
@swag_from('../docs/auth/register.yml')
def register(current_user):
    """API đăng ký người dùng mới (chỉ admin)"""
    data = request.json
    print(data)
    
    if not data or not data.get('username') or not data.get('password'):
        return jsonify({'error': 'Username and password are required!'}), 400
    
    existing_user = db_client.get_user_by_username(data.get('username'))
    if existing_user:
        return jsonify({'error': 'Username already exists!'}), 409
    
    role = data.get('role', 'user')
    if role not in ['admin', 'user']:
        return jsonify({'error': 'Invalid role!'}), 400
    
    permissions = data.get('permissions', [])
    valid_permissions = ['wordlist', 'hydra', 'hash', 'rules']
    for perm in permissions:
        if perm not in valid_permissions:
            return jsonify({'error': f'Invalid permission: {perm}'}), 400
    
    hashed_password = generate_password_hash(data.get('password'))
    
    user_id = db_client.add_user({
        'username': data.get('username'),
        'password': hashed_password,
        'role': role,
        'permissions': permissions,
        'description': data.get('description', ''),
        'status': data.get('status', 'active'),
        'last_connect': datetime.datetime.utcnow(),
        'created_at': datetime.datetime.utcnow()
    })
    
    return jsonify({
        'message': 'User registered successfully!',
        'user_id': str(user_id)
    }), 201

@auth_bp.route('/users', methods=['GET'])
@admin_required
@swag_from('../docs/auth/get_users.yml')
def get_users(current_user):
    """API lấy danh sách người dùng (chỉ admin)"""
    # Get pagination parameters from request
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))
    
    # Get users with pagination
    users, total = db_client.get_users_paginated(page=page, per_page=per_page)
    
    return jsonify({
        'users': [{
            'id': str(user['_id']),
            'username': user['username'],
            'role': user['role'],
            'permissions': user.get('permissions', []),
            'description': user.get('description', ''),
            'status': user.get('status', 'active'),
            'last_connect': user.get('last_connect', ''),
            'created_at': user.get('created_at', '')
        } for user in users],
        'total': total,
        'page': page,
        'per_page': per_page,
        'total_pages': (total + per_page - 1) // per_page
    })

@auth_bp.route('/users/<user_id>', methods=['GET'])
@admin_required
@swag_from('../docs/auth/get_user.yml')
def get_user(current_user, user_id):
    """API lấy thông tin người dùng theo ID (chỉ admin)"""
    user = db_client.get_user_by_id(ObjectId(user_id))
    
    if not user:
        return jsonify({'error': 'User not found!'}), 404
    
    return jsonify({
        'id': str(user['_id']),
        'username': user['username'],
        'role': user['role'],
        'permissions': user.get('permissions', []),
        'description': user.get('description', ''),
        'created_at': user.get('created_at', '')
    })

@auth_bp.route('/users/<user_id>', methods=['PUT'])
@admin_required
@swag_from('../docs/auth/update_user.yml')
def update_user(current_user, user_id):
    """API cập nhật thông tin người dùng (chỉ admin)"""
    data = request.json
    
    if not data:
        return jsonify({'error': 'No data provided!'}), 400
    
    user = db_client.get_user_by_id(ObjectId(user_id))
    if not user:
        return jsonify({'error': 'User not found!'}), 404
    
    update_data = {}
    
    if 'role' in data:
        if data['role'] not in ['admin', 'user']:
            return jsonify({'error': 'Invalid role!'}), 400
        update_data['role'] = data['role']
    
    if 'permissions' in data:
        valid_permissions = ['wordlist', 'hydra', 'hash', 'rules']
        for perm in data['permissions']:
            if perm not in valid_permissions:
                return jsonify({'error': f'Invalid permission: {perm}'}), 400
        update_data['permissions'] = data['permissions']
    
    if 'password' in data:
        update_data['password'] = generate_password_hash(data['password'])
    
    if 'description' in data:
        update_data['description'] = data['description']
    
    if 'status' in data:
        if data['status'] not in ['active', 'disabled']:
            return jsonify({'error': 'Invalid status!'}), 400
        update_data['status'] = data['status']
    
    success = db_client.update_user(ObjectId(user_id), update_data)
    
    if success:
        return jsonify({'message': 'User updated successfully!'})
    
    return jsonify({'error': 'Failed to update user!'}), 500

@auth_bp.route('/users/<user_id>', methods=['DELETE'])
@admin_required
@swag_from('../docs/auth/delete_user.yml')
def delete_user(current_user, user_id):
    """API xóa người dùng (chỉ admin)"""
    if str(current_user['_id']) == user_id:
        return jsonify({'error': 'Cannot delete your own account!'}), 400
    
    success = db_client.delete_user(ObjectId(user_id))
    
    if success:
        return jsonify({'message': 'User deleted successfully!'})
    
    return jsonify({'error': 'User not found!'}), 404

@auth_bp.route('/profile', methods=['GET'])
@token_required
@swag_from('../docs/auth/get_profile.yml')
def get_profile(current_user):
    """API lấy thông tin cá nhân của người dùng đang đăng nhập"""
    return jsonify({
        'id': str(current_user['_id']),
        'username': current_user['username'],
        'role': current_user['role'],
        'permissions': current_user.get('permissions', []),
        'description': current_user.get('description', ''),
        'status': current_user.get('status', 'active'),
        'last_connect': current_user.get('last_connect', '')
    })

@auth_bp.route('/change-password', methods=['POST'])
@token_required
@swag_from('../docs/auth/change_password.yml')
def change_password(current_user):
    """API thay đổi mật khẩu của người dùng đang đăng nhập"""
    data = request.json
    
    if not data or not data.get('current_password') or not data.get('new_password'):
        return jsonify({'error': 'Current password and new password are required!'}), 400
    
    if not check_password_hash(current_user['password'], data.get('current_password')):
        return jsonify({'error': 'Current password is incorrect!'}), 401
    
    hashed_password = generate_password_hash(data.get('new_password'))
    
    success = db_client.update_user(current_user['_id'], {'password': hashed_password})
    
    if success:
        return jsonify({'message': 'Password changed successfully!'})
    
    return jsonify({'error': 'Failed to change password!'}), 500
