from flask import Blueprint, request, jsonify
from libs.mongo_client import MongoD<PERSON>lient
from libs.hash_validator import validate_hashcat_input, sanitize_hash, is_valid_hash
from libs.hashcat_utils import build_hashcat_command, run_hashcat, sessions
from libs.hashcat_parser import process_hashcat_output
import requests
from urllib.parse import quote
from flasgger import swag_from
import subprocess
import os
import uuid
import threading
import time
import logging
import fcntl
from bson import ObjectId
import traceback

hash_bp = Blueprint('hash', __name__)
db_client = MongoDBClient()

# Thư mục lưu trữ tạm thời
BASE_DIR = "./hashcat_tmp"
os.makedirs(BASE_DIR, exist_ok=True)

@hash_bp.route('/hash/identify', methods=['POST'])
@swag_from('../docs/hash/identify_hash.yml')
def identify_hash():
    try:
        hash_value = request.json.get('hash')
        
        if not hash_value:
            return jsonify({
                'success': False,
                'error': 'No hash provided'
            }), 400
            
        hash_value = sanitize_hash(hash_value)

        api_url = 'https://hashes.com/en/api/identifier'
        headers = {
            'User-Agent': 'HashIdentifier/1.0',
            'Accept': 'application/json'
        }
        
        timeout = 10
        
        response = requests.get(
            api_url,
            params={'hash': hash_value},
            headers=headers,
            timeout=timeout,
            verify=True
        )
        
        if response.status_code == 200:
            hash_types = db_client.get_hash_types()
            data = response.json()
            api_algorithms = data.get('algorithms', [])[0]
            hash_id = ""
            # Tạo danh sách kết quả đã được mapping
            mapped_algorithms = []
            for hash_type in hash_types:
                if api_algorithms.lower() == hash_type['name'].lower():
                    hash_id = str(hash_type['_id'])
                
            return jsonify({
                'success': True,
                'hash': hash_value,
                'algorithms': api_algorithms,
                'id': hash_id,
                'source': 'hashes.com'
            })

    except requests.exceptions.RequestException as e:
        return jsonify({
            'success': False,
            'error': 'Connection error',
            'details': str(e)
        }), 503
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'Internal server error',
            'details': str(e)
        }), 500

@hash_bp.route('/hash/types', methods=['GET'])
@swag_from('../docs/hash/get_hash_types.yml')
def get_hash_types():
    """API để lấy danh sách các loại hash được hỗ trợ"""
    try:
        hash_types = db_client.get_hash_types()
        
        return jsonify({
            'success': True,
            'hash_types': [{
                'id': str(ht['_id']),
                'name': ht['name'],
                'hashcat_id': ht['hashcat_id'],
                'example': ht.get('example', ''),
                'is_precomputed': ht.get('is_precomputed', False),
                'is_popular': ht.get('is_popular', False)
            } for ht in hash_types]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@hash_bp.route("/hash/types/<hash_id>", methods=["GET"])
@swag_from('../docs/hash/get_hash_type_details.yml')
def get_hash_type_details(hash_id):
    """API lấy chi tiết của một loại hash dựa trên ID"""
    try:
        # Chuyển đổi hash_id thành ObjectId
        hash_type = db_client.get_hash_type_by_id(ObjectId(hash_id))
        
        if not hash_type:
            return jsonify({
                'success': False,
                'error': 'Hash type not found'
            }), 404
        
        # Chuyển đổi ObjectId thành string để có thể serialize thành JSON
        hash_type['_id'] = str(hash_type['_id'])
        
        # Tạo response với thông tin chi tiết
        response = {
            'success': True,
            'hash_type': {
                'id': hash_type['_id'],
                'name': hash_type['name'],
                'hashcat_id': hash_type['hashcat_id'],
                'example': hash_type.get('example', ''),
                'description': hash_type.get('description', ''),
                'is_precomputed': hash_type.get('is_precomputed', False),
                'is_popular': hash_type.get('is_popular', False),
                'complexity': hash_type.get('complexity', 'medium'),
                'created_at': hash_type.get('created_at', None)
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@hash_bp.route('/hash/lookup', methods=['POST'])
@swag_from('../docs/hash/lookup.yml')
def lookup_hash():
    """API để tìm kiếm hash trong database bên ngoài sử dụng POST"""
    try:
        data = request.json
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'Missing request body'
            }), 400
            
        # Get hash input - support both single hash and array of hashes
        hash_input = data.get('hash')
        if not hash_input:
            return jsonify({
                'success': False,
                'error': 'Missing hash parameter'
            }), 400
            
        # Convert single hash to array for consistent processing
        hash_values = hash_input if isinstance(hash_input, list) else [hash_input]
        
        # Sanitize all hashes
        hash_values = [sanitize_hash(h) for h in hash_values]
        
        # Get algorithm ID
        alg_id = data.get('algorithm')
        if not alg_id:
            return jsonify({
                'success': False,
                'error': 'Missing algorithm parameter'
            }), 400
            
        # Get hash type from database
        hash_type = db_client.get_hash_type_by_id(alg_id)
        if not hash_type:
            return jsonify({
                'success': False,
                'error': 'Invalid hash type ID'
            }), 400
            
        # Get algorithm name and format it for API
        alg = hash_type['name'].lower().replace('sha2-', 'sha')
        
        # Create a task in the database to track this lookup
        task_id = db_client.add_hashcat_task({
            "created_by": request.headers.get("X-User-ID", "anonymous"),
            "command": f"API lookup for {len(hash_values)} hashes of type {alg}",
            "configuration": {
                "hash_input": hash_values,
                "hash_id": alg_id,
                "algorithm": alg
            },
            "hash_input": hash_values,
            "method": "quicksearch",
            "status": "Running"
        })
        
        # Results to collect
        all_results = []
        cracked_hashes = []
        remaining_hashes = hash_values.copy()
        print(hash_values)
        # Process each hash
        for hash_value in hash_values:
            try:
                # Call the external API
                api_url = 'https://demo.cybertrust.icu/api/hashpass/search'
                
                response = requests.get(
                    api_url,
                    params={'alg': alg, 'hash': hash_value},
                    timeout=10,
                    verify=True
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'Ok' in data:
                        # Hash was found
                        password = data['Ok']
                        cracked_hashes.append({
                            "hash": hash_value,
                            "password": password,
                            "source": "quicksearch"
                        })
                        # Remove from remaining hashes
                        if hash_value in remaining_hashes:
                            remaining_hashes.remove(hash_value)
                        
                        all_results.append({
                            'hash': hash_value,
                            'found': True,
                            'password': password
                        })
                    else:
                        # Hash was not found
                        all_results.append({
                            'hash': hash_value,
                            'found': False,
                            'message': 'Hash not found in database'
                        })
                else:
                    # API error
                    all_results.append({
                        'hash': hash_value,
                        'found': False,
                        'message': f"API error: {response.status_code}"
                    })
                    
            except Exception as e:
                # Request error
                all_results.append({
                    'hash': hash_value,
                    'found': False,
                    'message': f"Error: {str(e)}"
                })
        
        remaining_results = [{"hash": h, "password": "", "source": "remaining_hashes"} for h in remaining_hashes]
        # Update the task with results
        db_client.update_hashcat_task(
            task_id=task_id,
            update_data={
                "status": "Completed",
                "end_time": time.time(),
                "results": {
                    "all": cracked_hashes + remaining_results,
                    "newly_cracked": cracked_hashes,
                    "already_cracked": [],
                    "remaining_hashes": remaining_hashes,
                    "remaining_to_crack": len(remaining_hashes)
                }
            }
        )
        
        # Return the results
        return jsonify({
            'success': True,
            'results': all_results,
            'task_id': task_id,
            'summary': {
                'total': len(hash_values),
                'found': len(cracked_hashes),
                'not_found': len(hash_values) - len(cracked_hashes)
            }
        })
                
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@hash_bp.route('/crack/tasks', methods=['GET'])
@swag_from('../docs/hash/get_tasks.yml')
def get_crack_tasks():
    """API để lấy danh sách các task crack password"""
    try:
        # Pagination parameters
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        
        # Filter parameters
        status_param = request.args.get('status', '')
        method = request.args.get('method', None)
        
        # Parse status parameter (comma-separated values)
        statuses = [s.strip() for s in status_param.split(',')] if status_param else None
        
        # Get tasks with pagination
        tasks, total = db_client.get_hashcat_tasks_paginated(
            page=page,
            per_page=per_page,
            status=statuses,
            method=method
        )
        
        # Format tasks for response
        formatted_tasks = []
        for task in tasks:
            # Calculate success rate if available
            success_rate = 0
            if task.get('results'):
                total_hashes = len(task.get('results', {}).get('all', [])) + len(task.get('results', {}).get('remaining_hashes', []))
                if total_hashes > 0:
                    success_rate = (len(task.get('results', {}).get('all', [])) / total_hashes) * 100
            
            # Calculate execution time
            execution_time = ""
            if task.get('end_time') and task.get('start_time'):
                time_diff = task.get('end_time') - task.get('start_time')
                execution_time = f"{time_diff:.2f}s"
            
            formatted_task = {
                'id': task.get('_id'),
                'method': task.get('method', 'hashcat'),
                'command': task.get('command', ''),
                'status': task.get('status', ''),
                'success_rate': f"{success_rate:.1f}%",
                'execution_time': execution_time,
                'created_at': task.get('created_at', ''),
                'created_by': task.get('created_by', 'anonymous'),
                'configuration': task.get('configuration', {}),
                'results_summary': {
                    'total': len(task.get('results', {}).get('all', [])) + len(task.get('results', {}).get('remaining_hashes', [])),
                    'cracked': len(task.get('results', {}).get('all', [])),
                    'remaining': len(task.get('results', {}).get('remaining_hashes', []))
                }
            }
            formatted_tasks.append(formatted_task)
        
        # Return paginated response
        return jsonify({
            'success': True,
            'tasks': formatted_tasks,
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@hash_bp.route('/crack/tasks/<task_id>', methods=['DELETE'])
@swag_from('../docs/hash/delete_task.yml')
def delete_crack_task(task_id):
    """API để xóa một task crack password"""
    try:
        result = db_client.delete_crack_task(task_id)
        
        if result:
            return jsonify({
                'success': True,
                'message': f'Task {task_id} deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Task not found'
            }), 404
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@hash_bp.route("/hashcat/start", methods=["POST"])
def start_hashcat():
    """Khởi động một phiên hashcat mới"""
    data = request.json
    
    # Validate input data
    is_valid, error_message, validated_data = validate_hashcat_input(data)
    if not is_valid:
        return jsonify({"error": error_message}), 400

    session_id = str(uuid.uuid4())
    
    # Handle hash_id input
    if 'hash_id' in validated_data:
        hash_type = db_client.get_hash_type_by_id(validated_data['hash_id'])
        if hash_type:
            validated_data['hash_type'] = hash_type['hashcat_id']
        else:
            return jsonify({"error": "Invalid hashcat_id ID"}), 400
    
    # Handle wordlist ID input
    wordlist_id_list = validated_data['wordlist']
    if 'wordlist' in validated_data and validated_data['wordlist']:
        # If wordlist is an array of wordlist IDs
        if isinstance(validated_data['wordlist'], list):
            wordlist_paths = []
            for wordlist_id in validated_data['wordlist']:
                wordlist_obj = db_client.get_wordlist_by_id(ObjectId(wordlist_id))
                if wordlist_obj:
                    wordlist_paths.append(wordlist_obj['filepath'])
                else:
                    return jsonify({"error": f"Invalid wordlist ID: {wordlist_id}"}), 400
            
            validated_data['wordlist'] = wordlist_paths
        
        # If wordlist is a single ID
        elif isinstance(validated_data['wordlist'], str):
            wordlist_obj = db_client.get_wordlist_by_id(ObjectId(validated_data['wordlist']))
            if wordlist_obj:
                validated_data['wordlist'] = wordlist_obj['filepath']
            else:
                return jsonify({"error": "Invalid wordlist ID"}), 400
    
    # Handle rules_file ID input
    if 'rules_file' in validated_data and validated_data['rules_file']:
        rule_obj = db_client.get_rule_by_id(ObjectId(validated_data['rules_file']))
        if rule_obj:
            validated_data['rules_file'] = rule_obj['filepath']
        else:
            return jsonify({"error": "Invalid rules file ID"}), 400

    try:
        cmd, output_file = build_hashcat_command(session_id, validated_data)

        # Lấy hash value và hash type từ input data
        hash_input = validated_data.get("hash_input")
        hash_type = validated_data.get("hash_type")
        validated_data['wordlist'] = wordlist_id_list,
        
        # Create a new hashcat task in the database
        task_data = {
            "created_by": request.headers.get("X-User-ID", "anonymous"),
            "command": " ".join(cmd),
            "configuration": validated_data,
            "hash_input": hash_input
        }
        
        task_id = db_client.add_hashcat_task(task_data)
        
        # Variables to track potfile results
        potfile_results = remaining_hashes = []
        all_hashes_cracked = False
        
        # Kiểm tra potfile trước nếu potfile_disable không được bật
        if not validated_data.get("potfile_disable", False) and hash_input:
            # Create a show command to check if hash is already in potfile
            show_cmd = cmd.copy()
            if "--show" not in show_cmd:
                show_cmd.append("--show")
            
            try:
                show_process = subprocess.run(
                    show_cmd,
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                potfile_output = show_process.stdout.strip()
                
                if potfile_output:
                    # Extract all hash:password pairs from potfile output
                    potfile_hash_dict = {}
                    for line in potfile_output.splitlines():
                        line = line.strip()
                        if ':' in line:
                            parts = line.split(':', 1)
                            if len(parts) >= 2:
                                hash_val = parts[0].strip()
                                password = parts[1].strip()
                                potfile_hash_dict[hash_val] = password
                                potfile_results.append({
                                    "hash": hash_val,
                                    "password": password,
                                    "source": "already_cracked"
                                })
                    
                    # Check which hashes still need to be cracked
                    remaining_hashes = []
                    for hash_val in hash_input:
                        if hash_val not in potfile_hash_dict:
                            remaining_hashes.append(hash_val)
                    remaining_results = [{"hash": h, "password": "", "source": "remaining_hashes"} for h in remaining_hashes]
                    # Update the task with potfile results
                    db_client.update_hashcat_task(
                        task_id=task_id,
                        update_data={
                            "results": {
                                "all": potfile_results + remaining_results,
                                "already_cracked": potfile_results,
                                "remaining_hashes": remaining_hashes,
                                "remaining_to_crack": len(remaining_hashes)
                            }
                        }
                    )
                    
                    # Check if all hashes were found in potfile
                    if not remaining_hashes:
                        all_hashes_cracked = True
                        db_client.update_hashcat_task(
                            task_id=task_id,
                            update_data={
                                "status": "Completed",
                                "end_time": time.time(),
                                "raw_output": "All target hashes have been successfully cracked."
                            }
                        )
                        return jsonify({
                            "task_id": task_id,
                            "status": "Completed",
                            "message": "All target hashes have been successfully cracked.",
                            "results": {
                                "all": potfile_results,
                                "already_cracked": potfile_results,
                                "newly_cracked": [],
                                "remaining_hashes": [],
                                "remaining_to_crack": 0
                            }
                        })
                
                # If there are remaining hashes, create a new hashfile with only those
                if remaining_hashes and potfile_results:
                    # Create a new hash file with only the remaining hashes
                    hash_file_path = os.path.join(BASE_DIR, f"hash_{session_id}.txt")
                    with open(hash_file_path, "w") as f:
                        f.write('\n'.join(remaining_hashes))
                    
                    # Update the command to use the new hash file
                    hash_file_index = None
                    for i, arg in enumerate(cmd):
                        if arg.startswith(f"{BASE_DIR}/hash_") and arg.endswith(".txt"):
                            hash_file_index = i
                            break
                    
                    if hash_file_index is not None:
                        cmd[hash_file_index] = hash_file_path
                        # Update command in database
                        db_client.update_hashcat_task(
                            task_id=task_id,
                            update_data={"command": " ".join(cmd)}
                        )
            
            except subprocess.TimeoutExpired:
                logging.error("Timeout while running hashcat --show")
                db_client.update_hashcat_task(
                    task_id=task_id,
                    update_data={"raw_output": "Timeout while checking potfile"}
                )
            except Exception as e:
                logging.error(f"Error running hashcat --show: {str(e)}")
                logging.error("Error running hashcat --show:\n%s", traceback.format_exc())
                db_client.update_hashcat_task(
                    task_id=task_id,
                    update_data={"raw_output": f"Error checking potfile: {str(e)}"}
                )
                # If there was an error checking the potfile, attempt to crack all hashes
                remaining_hashes = hash_input

        # Nếu không có kết quả trong potfile hoặc còn hash chưa crack, khởi tạo session
        if not all_hashes_cracked:
            # Update task status to running
            db_client.update_hashcat_task(
                task_id=task_id,
                update_data={"status": "Running"}
            )

            # Khởi chạy thread cho hashcat
            thread = threading.Thread(
                target=run_hashcat,
                args=(task_id, session_id, cmd, hash_input, hash_type, output_file)
            )
            thread.daemon = True
            thread.start()

            # Get the current task state to return
            task = db_client.get_hashcat_task(task_id)
            
            return jsonify({
                "task_id": task_id,
                "status": "Started",
                "message": "Hashcat session started" + (f" (Some hashes already cracked: {len(potfile_results)})" if potfile_results else ""),
                "results": task.get("results", {})
            })

    except ValueError as e:
        return jsonify({
            "error": str(e)
        }), 400
    except Exception as e:
        logging.error(f"Error starting hashcat: {str(e)}")
        logging.error("Error starting hashcat:\n" + traceback.format_exc())
        return jsonify({
            "error": f"Internal server error: {str(e)}"
        }), 500

@hash_bp.route("/hashcat/status/<task_id>", methods=["GET"])
def get_status(task_id: str):
    """Lấy trạng thái chi tiết của một phiên hashcat từ database"""
    task = db_client.get_hashcat_task(task_id)
    if not task:
        return jsonify({"error": "Task not found"}), 404

    # Return the task data directly
    return jsonify(task)

@hash_bp.route("/hashcat/stop/<task_id>", methods=["POST"])
def stop_hashcat(task_id: str):
    """Dừng một phiên hashcat"""
    task = db_client.get_hashcat_task(task_id)
    if not task:
        return jsonify({"error": "Task not found"}), 404

    if task.get("status") not in ["Running", "Starting"]:
        return jsonify({"error": "Task is not running"}), 400

    # Get process ID
    pid = task.get("pid")
    
    if pid:
        try:
            # Try to terminate the process
            os.kill(pid, signal.SIGTERM)
            
            # Give it a moment to terminate
            time.sleep(1)
            
            # Check if it's still running
            try:
                os.kill(pid, 0)  # This will raise an exception if process is gone
                # Process still exists, force kill
                os.kill(pid, signal.SIGKILL)
            except OSError:
                # Process is already gone
                pass
                
            # Update task status
            db_client.update_hashcat_task(
                task_id=task_id,
                update_data={
                    "status": "Stopped",
                    "end_time": time.time()
                }
            )
            
            return jsonify({
                "success": True,
                "message": "Task stopped",
                "task_id": task_id
            })
        except Exception as e:
            logging.error(f"Error stopping hashcat task {task_id}: {str(e)}")
            return jsonify({"error": f"Error stopping hashcat task: {str(e)}"}), 500
    else:
        return jsonify({"error": "No process ID found for this task"}), 400

