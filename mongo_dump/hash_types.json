[{"name": "MD4", "hashcat_id": 900, "description": "Raw Hash", "category": "Raw Hash", "example": "afe04867ec7a3845145579a95f72eca7", "is_precomputed": false, "is_popular": true}, {"name": "MD5", "hashcat_id": 0, "description": "Raw Hash", "category": "Raw Hash", "example": "8743b52063cd84097a65d1633f5c74f5", "is_precomputed": true, "is_popular": true}, {"name": "SHA1", "hashcat_id": 100, "description": "Raw Hash", "category": "Raw Hash", "example": "b89eaac7e61417341b710b727768294d0e6a277b", "is_precomputed": true, "is_popular": true}, {"name": "SHA224", "hashcat_id": 1300, "description": "Raw Hash", "category": "Raw Hash", "example": "e4fa1555ad877bf0ec455483371867200eee89550a93eff2f95a6198", "is_precomputed": false, "is_popular": true}, {"name": "SHA256", "hashcat_id": 1400, "description": "Raw Hash", "category": "Raw Hash", "example": "127e6fbfe24a750e72930c220a8e138275656b8e5d8f48a98c3c92df2caba935", "is_precomputed": true, "is_popular": true}, {"name": "SHA384", "hashcat_id": 10800, "description": "Raw Hash", "category": "Raw Hash", "example": "07371af1ca1fca7c6941d2399f3610f1e392c56c6d73fddffe38f18c430a2817028dae1ef09ac683b62148a2c8757f42", "is_precomputed": false, "is_popular": false}, {"name": "SHA512", "hashcat_id": 1700, "description": "Raw Hash", "category": "Raw Hash", "example": "82a9dda829eb7f8ffe9fbe49e45d47d2dad9664fbb7adf72492e3c81ebd3e29134d9bc12212bf83c6840f10e8246b9db54a4859b7ccd0123d86e5872c1e5082f", "is_precomputed": false, "is_popular": true}, {"name": "SHA224", "hashcat_id": 17300, "description": "Raw Hash", "category": "Raw Hash", "example": "412ef78534ba6ab0e9b1607d3e9767a25c1ea9d5e83176b4c2817a6c", "is_precomputed": false, "is_popular": false}, {"name": "SHA256", "hashcat_id": 17400, "description": "Raw Hash", "category": "Raw Hash", "example": "d60fcf6585da4e17224f58858970f0ed5ab042c3916b76b0b828e62eaf636cbd", "is_precomputed": false, "is_popular": false}, {"name": "SHA384", "hashcat_id": 17500, "description": "Raw Hash", "category": "Raw Hash", "example": "983ba28532cc6320d04f20fa485bcedb38bddb666eca5f1e5aa279ff1c6244fe5f83cf4bbf05b95ff378dd2353617221", "is_precomputed": false, "is_popular": false}, {"name": "SHA512", "hashcat_id": 17600, "description": "Raw Hash", "category": "Raw Hash", "example": "7c2dc1d743735d4e069f3bda85b1b7e9172033dfdd8cd599ca094ef8570f3930c3f2c0b7afc8d6152ce4eaad6057a2ff22e71934b3a3dd0fb55a7fc84a53144e", "is_precomputed": false, "is_popular": false}, {"name": "RIPEMD-160", "hashcat_id": 6000, "description": "Raw Hash", "category": "Raw Hash", "example": "012cb9b334ec1aeb71a9c8ce85586082467f7eb6", "is_precomputed": false, "is_popular": true}, {"name": "BLAKE2b-512", "hashcat_id": 600, "description": "Raw Hash", "category": "Raw Hash", "example": "$BLAKE2$296c269e70ac5f0095e6fb47693480f0f7b97ccd0307f5c3bfa4df8f5ca5c9308a0e7108e80a0a9c0ebb715e8b7109b072046c6cd5e155b4cfd2f27216283b1e", "is_precomputed": false, "is_popular": false}, {"name": "GOST R 34.11-2012 (Streebog) 256-bit, big-endian", "hashcat_id": 11700, "description": "Raw Hash", "category": "Raw Hash", "example": "57e9e50caec93d72e9498c211d6dc4f4d328248b48ecf46ba7abfa874f666e36", "is_precomputed": false, "is_popular": false}, {"name": "GOST R 34.11-2012 (Streebog) 512-bit, big-endian", "hashcat_id": 11800, "description": "Raw Hash", "category": "Raw Hash", "example": "5d5bdba48c8f89ee6c0a0e11023540424283e84902de08013aeeb626e819950bb32842903593a1d2e8f71897ff7fe72e17ac9ba8ce1d1d2f7e9c4359ea63bdc3", "is_precomputed": false, "is_popular": false}, {"name": "GOST R 34.11-94", "hashcat_id": 6900, "description": "Raw Hash", "category": "Raw Hash", "example": "df226c2c6dcb1d995c0299a33a084b201544293c31fc3d279530121d36bbcea9", "is_precomputed": false, "is_popular": false}, {"name": "GPG (AES-128/AES-256 (SHA-1($pass)))", "hashcat_id": 17010, "description": "Raw Hash", "category": "Raw Hash", "example": "$gpg$*1*348*1024*8833fa3812b5500aa9eb7e46febfa31a0584b7e4a5b13c198f5c9b0814243895cce45ac3714e79692fb5a130a1c943b9130315ce303cb7e6831be68ce427892858f313fc29f533434dbe0ef26573f2071bbcc1499dc49bda90648221ef3823757e2fba6099a18c0c83386b21d8c9b522ec935ecd540210dbf0f21c859429fd4d35fa056415d8087f27b3e66b16081ea18c544d8b2ea414484f17097bc83b773d92743f76eb2ccb4df8ba5f5ff84a5474a5e8a8e5179a5b0908503c55e428de04b40628325739874e1b4aa004c4cbdf09b0b620990a8479f1c9b4187e33e63fe48a565bc1264bbf4062559631bef9e346a7217f1cabe101a38ac4be9fa94f6dafe6b0301e67792ed51bca04140cddd5cb6e80ac6e95e9a09378c9651588fe360954b622c258a3897f11246c944a588822cc6daf1cb81ccc95098c3bea8432f1ee0c663b193a7c7f1cdfeb91eee0195296bf4783025655cbebd7c70236*3*254*2*7*16*a47ef38987beab0a0b9bfe74b72822e8*65536*1f5c90d9820997db", "is_precomputed": false, "is_popular": false}, {"name": "Half MD5", "hashcat_id": 5100, "description": "Raw Hash", "category": "Raw Hash", "example": "8743b52063cd8409", "is_precomputed": false, "is_popular": true}, {"name": "Keccak-224", "hashcat_id": 17700, "description": "Raw Hash", "category": "Raw Hash", "example": "e1dfad9bafeae6ef15f5bbb16cf4c26f09f5f1e7870581962fc84636", "is_precomputed": false, "is_popular": false}, {"name": "Keccak-256", "hashcat_id": 17800, "description": "Raw Hash", "category": "Raw Hash", "example": "203f88777f18bb4ee1226627b547808f38d90d3e106262b5de9ca943b57137b6", "is_precomputed": false, "is_popular": false}, {"name": "Keccak-384", "hashcat_id": 17900, "description": "Raw Hash", "category": "Raw Hash", "example": "5804b7ada5806ba79540100e9a7ef493654ff2a21d94d4f2ce4bf69abda5d94bf03701fe9525a15dfdc625bfbd769701", "is_precomputed": false, "is_popular": false}, {"name": "Keccak-512", "hashcat_id": 18000, "description": "Raw Hash", "category": "Raw Hash", "example": "2fbf5c9080f0a704de2e915ba8fdae6ab00bbc026b2c1c8fa07da1239381c6b7f4dfd399bf9652500da723694a4c719587dd0219cb30eabe61210a8ae4dc0b03", "is_precomputed": false, "is_popular": false}, {"name": "Whirlpool", "hashcat_id": 6100, "description": "Raw Hash", "category": "Raw Hash", "example": "7ca8eaaaa15eaa4c038b4c47b9313e92da827c06940e69947f85bc0fbef3eb8fd254da220ad9e208b6b28f6bb9be31dd760f1fdb26112d83f87d96b416a4d258", "is_precomputed": false, "is_popular": true}, {"name": "SipHash", "hashcat_id": 10100, "description": "Raw Hash", "category": "Raw Hash", "example": "ad61d78c06037cd9:2:4:81533218127174468417660201434054", "is_precomputed": false, "is_popular": false}, {"name": "md5(utf16le($pass))", "hashcat_id": 70, "description": "Raw Hash", "category": "Raw Hash", "example": "2303b15bfa48c74a74758135a0df1201", "is_precomputed": false, "is_popular": false}, {"name": "sha1(utf16le($pass))", "hashcat_id": 170, "description": "Raw Hash", "category": "Raw Hash", "example": "b9798556b741befdbddcbf640d1dd59d19b1e193", "is_precomputed": false, "is_popular": false}, {"name": "sha256(utf16le($pass))", "hashcat_id": 1470, "description": "Raw Hash", "category": "Raw Hash", "example": "9e9283e633f4a7a42d3abc93701155be8afe5660da24c8758e7d3533e2f2dc82", "is_precomputed": false, "is_popular": false}, {"name": "sha384(utf16le($pass))", "hashcat_id": 10870, "description": "Raw Hash", "category": "Raw Hash", "example": "48e61d68e93027fae35d405ed16cd01b6f1ae66267833b4a7aa1759e45bab9bba652da2e4c07c155a3d8cf1d81f3a7e8", "is_precomputed": false, "is_popular": false}, {"name": "sha512(utf16le($pass))", "hashcat_id": 1770, "description": "Raw Hash", "category": "Raw Hash", "example": "79bba09eb9354412d0f2c037c22a777b8bf549ab12d49b77d5b25faa839e4378d8f6fa11aceb6d9413977ae5ad5d011568bad2de4f998d75fd4ce916eda83697", "is_precomputed": false, "is_popular": false}, {"name": "BLAKE2b-512($pass.$salt)", "hashcat_id": 610, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "$BLAKE2$41fcd44c789c735c08b43a871b81c8f617ca43918d38aee6cf8291c58a0b00a03115857425e5ff6f044be7a5bec8536b52d6c9992e21cd43cdca8a55bbf1f5c1:1033", "is_precomputed": false, "is_popular": false}, {"name": "BLAKE2b-512($salt.$pass)", "hashcat_id": 620, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "$BLAKE2$f0325fdfc3f82a014935442f7adbc069d4636d67276a85b09f8de368f122cf5195a0b780d7fee709fbf1dcd02ddcb581df84508cf1fb0f3393af1be0565491c6:3301", "is_precomputed": false, "is_popular": false}, {"name": "md5($pass.$salt)", "hashcat_id": 10, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "01dfae6e5d4d90d9892622325959afbe:7050461", "is_precomputed": false, "is_popular": true}, {"name": "md5($salt.$pass)", "hashcat_id": 20, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "f0fda58630310a6dd91a7d8f0a4ceda2:4225637426", "is_precomputed": false, "is_popular": true}, {"name": "md5($salt.$pass.$salt)", "hashcat_id": 3800, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "2e45c4b99396c6cb2db8bda0d3df669f:1234", "is_precomputed": false, "is_popular": true}, {"name": "md5($salt.md5($pass))", "hashcat_id": 3710, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "95248989ec91f6d0439dbde2bd0140be:1234", "is_precomputed": false, "is_popular": true}, {"name": "md5($salt.md5($pass.$salt))", "hashcat_id": 4110, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "b4cb5c551a30f6c25d648560408df68a:1234", "is_precomputed": false, "is_popular": false}, {"name": "md5($salt.md5($salt.$pass))", "hashcat_id": 4010, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "30d0cf4a5d7ed831084c5b8b0ba75b46:1234", "is_precomputed": false, "is_popular": false}, {"name": "md5($salt.sha1($salt.$pass))", "hashcat_id": 21300, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "799dc7d9aa4d3f404cc21a4936dbdcde:68617368636174", "is_precomputed": false, "is_popular": false}, {"name": "md5($salt.utf16le($pass))", "hashcat_id": 40, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "d63d0e21fdc05f618d55ef306c54af82:13288442151473", "is_precomputed": false, "is_popular": false}, {"name": "md5(md5($pass))", "hashcat_id": 2600, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "a936af92b0ae20b1ff6c3347a72e5fbe", "is_precomputed": false, "is_popular": true}, {"name": "md5(md5($pass).md5($salt))", "hashcat_id": 3910, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "250920b3a5e31318806a032a4674df7e:1234", "is_precomputed": false, "is_popular": false}, {"name": "md5(md5(md5($pass)))", "hashcat_id": 3500, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "9882d0778518b095917eb589f6998441", "is_precomputed": false, "is_popular": false}, {"name": "md5(sha1($pass))", "hashcat_id": 4400, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "288496df99b33f8f75a7ce4837d1b480", "is_precomputed": false, "is_popular": true}, {"name": "md5(sha1($pass).$salt)", "hashcat_id": 4410, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "bc8319c0220bff8a0d7f5d703114a725:34659348756345251", "is_precomputed": false, "is_popular": false}, {"name": "md5(sha1($pass).md5($pass).sha1($pass))", "hashcat_id": 20900, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "100b3a4fc1dc8d60d9bf40688d8b740a", "is_precomputed": false, "is_popular": false}, {"name": "md5(sha1($salt).md5($pass))", "hashcat_id": 21200, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "e69b7a7fe1bf2ad9ef116f79551ee919:baa038987e582431a6d", "is_precomputed": false, "is_popular": false}, {"name": "md5(strtoupper(md5($pass)))", "hashcat_id": 4300, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "b8c385461bb9f9d733d3af832cf60b27", "is_precomputed": false, "is_popular": true}, {"name": "md5(utf16le($pass).$salt)", "hashcat_id": 30, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "b31d032cfdcf47a399990a71e43c5d2a:144816", "is_precomputed": false, "is_popular": false}, {"name": "sha1($pass.$salt)", "hashcat_id": 110, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "2fc5a684737ce1bf7b3b239df432416e0dd07357:2014", "is_precomputed": false, "is_popular": true}, {"name": "sha1($salt.$pass)", "hashcat_id": 120, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "cac35ec206d868b7d7cb0b55f31d9425b075082b:5363620024", "is_precomputed": false, "is_popular": true}, {"name": "sha1($salt.$pass.$salt)", "hashcat_id": 4900, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "85087a691a55cbb41ae335d459a9121d54080b80:488387841", "is_precomputed": false, "is_popular": false}, {"name": "sha1($salt.sha1($pass))", "hashcat_id": 4520, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "a0f835fdf57d36ebd8d0399cc44e6c2b86a1072b:511358214352751667201107073531735211566650747315", "is_precomputed": false, "is_popular": false}, {"name": "sha1($salt.sha1($pass.$salt))", "hashcat_id": 24300, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "94520b02c04e79e08a75a84c2a6e3ed4e3874fe8:ThisIsATestSalt", "is_precomputed": false, "is_popular": false}, {"name": "sha1($salt.utf16le($pass))", "hashcat_id": 140, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "5db61e4cd8776c7969cfd62456da639a4c87683a:8763434884872", "is_precomputed": false, "is_popular": false}, {"name": "sha1($salt1.$pass.$salt2)", "hashcat_id": 19300, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "630d2e918ab98e5fad9c61c0e4697654c4c16d73:18463812876898603420835420139870031762867:4449516425193605979760642927684590668549584534278112685644182848763890902699756869283142014018311837025441092624864168514500447147373198033271040848851687108629922695275682773136540885737874252666804716579965812709728589952868736177317883550827482248620334", "is_precomputed": false, "is_popular": false}, {"name": "sha1(CX)", "hashcat_id": 14400, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "fd9149fb3ae37085dc6ed1314449f449fbf77aba:87740665218240877702", "is_precomputed": false, "is_popular": false}, {"name": "sha1(md5($pass))", "hashcat_id": 4700, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "92d85978d884eb1d99a51652b1139c8279fa8663", "is_precomputed": false, "is_popular": true}, {"name": "sha1(md5($pass).$salt)", "hashcat_id": 4710, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "53c724b7f34f09787ed3f1b316215fc35c789504:hashcat1", "is_precomputed": false, "is_popular": false}, {"name": "sha1(md5($pass.$salt))", "hashcat_id": 21100, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "aade80a61c6e3cd3cac614f47c1991e0a87dd028:6", "is_precomputed": false, "is_popular": false}, {"name": "sha1(md5(md5($pass)))", "hashcat_id": 18500, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "888a2ffcb3854fba0321110c5d0d434ad1aa2880", "is_precomputed": false, "is_popular": false}, {"name": "sha1(sha1($pass))", "hashcat_id": 4500, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "3db9184f5da4e463832b086211af8d2314919951", "is_precomputed": false, "is_popular": true}, {"name": "sha1(sha1($pass).$salt)", "hashcat_id": 4510, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "9138d472fce6fe50e2a32da4eec4ecdc8860f4d5:hashcat1", "is_precomputed": false, "is_popular": false}, {"name": "sha1(sha1($salt.$pass.$salt))", "hashcat_id": 5000, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "203f88777f18bb4ee1226627b547808f38d90d3e106262b5de9ca943b57137b6 replaced by specific Keccak types in hashcat 5.0", "is_precomputed": false, "is_popular": true}, {"name": "sha1(utf16le($pass).$salt)", "hashcat_id": 130, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "c57f6ac1b71f45a07dbd91a59fa47c23abcd87c2:631225", "is_precomputed": false, "is_popular": false}, {"name": "sha256($pass.$salt)", "hashcat_id": 1410, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "c73d08de890479518ed60cf670d17faa26a4a71f995c1dcc978165399401a6c4:53743528", "is_precomputed": false, "is_popular": true}, {"name": "sha256($salt.$pass)", "hashcat_id": 1420, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "eb368a2dfd38b405f014118c7d9747fcc97f4f0ee75c05963cd9da6ee65ef498:560407001617", "is_precomputed": false, "is_popular": true}, {"name": "sha256($salt.$pass.$salt)", "hashcat_id": 22300, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "755a8ce4e0cf0baee41d714aa35c9fca803106608f718f973eab006578285007:11265", "is_precomputed": false, "is_popular": false}, {"name": "sha256($salt.sha256($pass))", "hashcat_id": 20720, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "bae9edada8358fcebcd811f7d362f46277fb9d488379869fba65d79701d48b8b:869dc2ed80187919", "is_precomputed": false, "is_popular": false}, {"name": "sha256($salt.sha256_bin($pass))", "hashcat_id": 21420, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "5934ea4d670c13a71155faba42056b2525f71bdc9215d31108990c11bf3d98e3:9269771356270099311432765354522635185291064175409115041569", "is_precomputed": false, "is_popular": false}, {"name": "sha256($salt.utf16le($pass))", "hashcat_id": 1440, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "a4bd99e1e0aba51814e81388badb23ecc560312c4324b2018ea76393ea1caca9:12345678", "is_precomputed": false, "is_popular": false}, {"name": "sha256(md5($pass))", "hashcat_id": 20800, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "74ee1fae245edd6f27bf36efc3604942479fceefbadab5dc5c0b538c196eb0f1", "is_precomputed": false, "is_popular": false}, {"name": "sha256(sha256($pass).$salt)", "hashcat_id": 20710, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "bfede293ecf6539211a7305ea218b9f3f608953130405cda9eaba6fb6250f824:7218532375810603", "is_precomputed": false, "is_popular": false}, {"name": "sha256(sha256_bin($pass))", "hashcat_id": 21400, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "0cc1b58a543f372327aa0281e97ab56e345267ee46feabf7709515debb7ec43c", "is_precomputed": false, "is_popular": false}, {"name": "sha256(utf16le($pass).$salt)", "hashcat_id": 1430, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "4cc8eb60476c33edac52b5a7548c2c50ef0f9e31ce656c6f4b213f901bc87421:890128", "is_precomputed": false, "is_popular": false}, {"name": "sha384($pass.$salt)", "hashcat_id": 10810, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "ca1c843a7a336234baf9db2e10bc38824ce523402fbd7741286b1602bdf6cb869a45289bb9fb706bd404b9f3842ff729:2746460797049820734631508", "is_precomputed": false, "is_popular": false}, {"name": "sha384($salt.$pass)", "hashcat_id": 10820, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "63f63d7f82d4a4cb6b9ff37a6bc7c5ec39faaf9c9078551f5cbf7960e76ded87b643d37ac53c45bc544325e7ff83a1f2:93362", "is_precomputed": false, "is_popular": false}, {"name": "sha384($salt.utf16le($pass))", "hashcat_id": 10840, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "316e93ea8e04de3e5a909c53d36923a31a16c1b9e89b44201d6082f87ca49c5bca53cad65f685207db3ea2ccc7ca40f8:700067651", "is_precomputed": false, "is_popular": false}, {"name": "sha384(utf16le($pass).$salt)", "hashcat_id": 10830, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "3516a589d2ed4071bf5e36f22e11212b3ad9050b9094b23067103d51e99dcb25c4dc397dba8034fed11a8184acfbb699:577730514588712", "is_precomputed": false, "is_popular": false}, {"name": "sha512($pass.$salt)", "hashcat_id": 1710, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "e5c3ede3e49fb86592fb03f471c35ba13e8d89b8ab65142c9a8fdafb635fa2223c24e5558fd9313e8995019dcbec1fb584146b7bb12685c7765fc8c0d51379fd:6352283260", "is_precomputed": false, "is_popular": true}, {"name": "sha512($salt.$pass)", "hashcat_id": 1720, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "976b451818634a1e2acba682da3fd6efa72adf8a7a08d7939550c244b237c72c7d42367544e826c0c83fe5c02f97c0373b6b1386cc794bf0d21d2df01bb9c08a:2613516180127", "is_precomputed": false, "is_popular": true}, {"name": "sha512($salt.utf16le($pass))", "hashcat_id": 1740, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "bae3a3358b3459c761a3ed40d34022f0609a02d90a0d7274610b16147e58ece00cd849a0bd5cf6a92ee5eb5687075b4e754324dfa70deca6993a85b2ca865bc8:1237015423", "is_precomputed": false, "is_popular": false}, {"name": "sha512(utf16le($pass).$salt)", "hashcat_id": 1730, "description": "Raw Hash salted and/or iterated", "category": "Raw Hash salted and/or iterated", "example": "13070359002b6fbb3d28e50fba55efcf3d7cc115fe6e3f6c98bf0e3210f1c6923427a1e1a3b214c1de92c467683f6466727ba3a51684022be5cc2ffcb78457d2:341351589", "is_precomputed": false, "is_popular": false}, {"name": "Amazon AWS4-HMAC-SHA256", "hashcat_id": 28700, "description": "Raw Hash authenticated", "category": "Raw Hash authenticated", "example": "$AWS-Sig-v4$0$20220221T000000Z$us-east-1$s3$421ab6e4af9f49fa30fa9c253fcfeb2ce91668e139e6b23303c5f75b04f8a3c4$3755ed2bc1b2346e003ccaa7d02ae8b73c72bcbe9f452ccf066c78504d786bbb", "is_precomputed": false, "is_popular": false}, {"name": "CRC32", "hashcat_id": 11500, "description": "Raw Checksum", "category": "Raw Checksum", "example": "c762de4a:00000000", "is_precomputed": false, "is_popular": false}, {"name": "CRC32C", "hashcat_id": 27900, "description": "Raw Checksum", "category": "Raw Checksum", "example": "5e23d60f:00000000", "is_precomputed": false, "is_popular": false}, {"name": "CRC64Jones", "hashcat_id": 28000, "description": "Raw Checksum", "category": "Raw Checksum", "example": "65c1f848fe38cce6:4260950400318054", "is_precomputed": false, "is_popular": false}, {"name": "Java Object hashCode()", "hashcat_id": 18700, "description": "Raw Checksum", "category": "Raw Checksum", "example": "29937c08", "is_precomputed": false, "is_popular": false}, {"name": "MurmurHash", "hashcat_id": 25700, "description": "Raw Checksum", "category": "Raw Checksum", "example": "b69e7687:05094309", "is_precomputed": false, "is_popular": false}, {"name": "MurmurHash3", "hashcat_id": 27800, "description": "Raw Checksum", "category": "Raw Checksum", "example": "23e93f65:00000000", "is_precomputed": false, "is_popular": false}, {"name": "ChaCha20", "hashcat_id": 15400, "description": "Raw Cipher, Known-plaintext attack", "category": "Raw Cipher, Known-plaintext attack", "example": "$chacha20$*0400000000000003*16*0200000000000001*5152535455565758*6b05fe554b0bc3b3", "is_precomputed": false, "is_popular": false}, {"name": "Linux Kernel Crypto API (2.4)", "hashcat_id": 14500, "description": "Raw Cipher, Known-plaintext attack", "category": "Raw Cipher, Known-plaintext attack", "example": "$cryptoapi$9$2$03000000000000000000000000000000$00000000000000000000000000000000$d1d20e91a8f2e18881dc79369d8af761", "is_precomputed": false, "is_popular": false}, {"name": "PBKDF2-HMAC-MD5", "hashcat_id": 11900, "description": "Generic K<PERSON>", "category": "Generic K<PERSON>", "example": "md5:1000:MTg1MzA=:Lz84VOcrXd699Edsj34PP98+f4f3S0rTZ4kHAIHoAjs=", "is_precomputed": false, "is_popular": false}, {"name": "PBKDF2-HMAC-SHA1", "hashcat_id": 12000, "description": "Generic K<PERSON>", "category": "Generic K<PERSON>", "example": "sha1:1000:MzU4NTA4MzIzNzA1MDQ=:19ofiY+ahBXhvkDsp0j2ww==", "is_precomputed": false, "is_popular": true}, {"name": "PBKDF2-HMAC-SHA256", "hashcat_id": 10900, "description": "Generic K<PERSON>", "category": "Generic K<PERSON>", "example": "sha256:1000:MTc3MTA0MTQwMjQxNzY=:PYjCU215Mi57AYPKva9j7mvF4Rc5bCnt", "is_precomputed": false, "is_popular": true}, {"name": "PBKDF2-HMAC-SHA512", "hashcat_id": 12100, "description": "Generic K<PERSON>", "category": "Generic K<PERSON>", "example": "sha512:1000:ODQyMDEwNjQyODY=:MKaHNWXUsuJB3IEwBHbm3w==", "is_precomputed": false, "is_popular": true}, {"name": "scrypt", "hashcat_id": 8900, "description": "Generic K<PERSON>", "category": "Generic K<PERSON>", "example": "SCRYPT:1024:1:1:MDIwMzMwNTQwNDQyNQ==:5FW+zWivLxgCWj7qLiQbeC8zaNQ+qdO0NUinvqyFcfo=", "is_precomputed": false, "is_popular": true}, {"name": "phpass", "hashcat_id": 400, "description": "Generic K<PERSON>", "category": "Generic K<PERSON>", "example": "$H$984478476IagS59wHZvyQMArzfx58u.", "is_precomputed": false, "is_popular": true}, {"name": "TACACS+", "hashcat_id": 16100, "description": "Network Protocol", "category": "Network Protocol", "example": "$tacacs-plus$0$5fde8e68$4e13e8fb33df$c006", "is_precomputed": false, "is_popular": false}, {"name": "SIP digest authentication (MD5)", "hashcat_id": 11400, "description": "Network Protocol", "category": "Network Protocol", "example": "$sip$*********************************username*asterisk*REGISTER*sip******************2b01df0b****MD5*ad0520061ca07c120d7e8ce696a6df2d", "is_precomputed": false, "is_popular": false}, {"name": "IKE-PSK MD5", "hashcat_id": 5300, "description": "Network Protocol", "category": "Network Protocol", "example": "https://hashcat.net/misc/example_hashes/hashcat.ikemd5", "is_precomputed": false, "is_popular": false}, {"name": "IKE-PSK SHA1", "hashcat_id": 5400, "description": "Network Protocol", "category": "Network Protocol", "example": "https://hashcat.net/misc/example_hashes/hashcat.ikesha1", "is_precomputed": false, "is_popular": false}, {"name": "SNMPv3 HMAC-MD5-96", "hashcat_id": 25100, "description": "Network Protocol", "category": "Network Protocol", "example": "$SNMPv3$1$45889431$30818f0201033011020409242fc0020300ffe304010102010304383036041180001f88808106d566db57fd600000000002011002020118040a6d61747269785f4d4435040c0000000000000000000000000400303d041180001f88808106d566db57fd60000000000400a226020411f319300201000201003018301606082b06010201010200060a2b06010401bf0803020a$80001f88808106d566db57fd6000000000$1b37c3ea872731f922959e90", "is_precomputed": false, "is_popular": false}, {"name": "SNMPv3 HMAC-MD5-96/HMAC-SHA1-96", "hashcat_id": 25000, "description": "Network Protocol", "category": "Network Protocol", "example": "$SNMPv3$0$45889431$30818f0201033011020409242fc0020300ffe304010102010304383036041180001f88808106d566db57fd600000000002011002020118040a6d61747269785f4d4435040c0000000000000000000000000400303d041180001f88808106d566db57fd60000000000400a226020411f319300201000201003018301606082b06010201010200060a2b06010401bf0803020a$80001f88808106d566db57fd6000000000$1b37c3ea872731f922959e90", "is_precomputed": false, "is_popular": false}, {"name": "SNMPv3 HMAC-SHA1-96", "hashcat_id": 25200, "description": "Network Protocol", "category": "Network Protocol", "example": "$SNMPv3$2$45889431$30818f02010330110204371780f3020300ffe304010102010304383036041180001f88808106d566db57fd600000000002011002020118040a6d61747269785f534841040c0000000000000000000000000400303d041180001f88808106d566db57fd60000000000400a2260204073557d50201000201003018301606082b06010201010200060a2b06010401bf0803020a$80001f88808106d566db57fd6000000000$81f14f1930589f26f6755f6b", "is_precomputed": false, "is_popular": false}, {"name": "SNMPv3 HMAC-SHA224-128", "hashcat_id": 26700, "description": "Network Protocol", "category": "Network Protocol", "example": "$SNMPv3$3$45889431$308197020103301102047aa1a79e020300ffe30401010201030440303e041180001f88808106d566db57fd600000000002011002020118040e6d61747269785f5348412d3232340410000000000000000000000000000000000400303d041180001f88808106d566db57fd60000000000400a2260204272f76620201000201003018301606082b06010201010200060a2b06010401bf0803020a$80001f88808106d566db57fd6000000000$2f7a3891dd2e27d3f567e4d6d0257962", "is_precomputed": false, "is_popular": false}, {"name": "SNMPv3 HMAC-SHA256-192", "hashcat_id": 26800, "description": "Network Protocol", "category": "Network Protocol", "example": "$SNMPv3$4$45889431$30819f020103301102047fc51818020300ffe304010102010304483046041180001f88808106d566db57fd600000000002011002020118040e6d61747269785f5348412d32353604180000000000000000000000000000000000000000000000000400303d041180001f88808106d566db57fd60000000000400a22602040efec2600201000201003018301606082b06010201010200060a2b06010401bf0803020a$80001f88808106d566db57fd6000000000$36d655bfeb59e933845db47d719b68ac7bc59ec087eb89a0", "is_precomputed": false, "is_popular": false}, {"name": "SNMPv3 HMAC-SHA384-256", "hashcat_id": 26900, "description": "Network Protocol", "category": "Network Protocol", "example": "$SNMPv3$5$45889431$3081a70201033011020455c0c85c020300ffe30401010201030450304e041180001f88808106d566db57fd600000000002011002020118040e6d61747269785f5348412d333834042000000000000000000000000000000000000000000000000000000000000000000400303d041180001f88808106d566db57fd60000000000400a226020411b3c3590201000201003018301606082b06010201010200060a2b06010401bf0803020a$80001f88808106d566db57fd60$89424907553231aaa27055f4b3b0a97c626ed4cdc4b660d903765b607af792a5", "is_precomputed": false, "is_popular": false}, {"name": "SNMPv3 HMAC-SHA512-384", "hashcat_id": 27300, "description": "Network Protocol", "category": "Network Protocol", "example": "$SNMPv3$6$45889431$3081b702010330110204367c80d4020300ffe30401010201030460305e041180001f88808106d566db57fd600000000002011002020118040e6d61747269785f5348412d35313204300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400303d041180001f88808106d566db57fd60000000000400a22602046ea3546f0201000201003018301606082b06010201010200060a2b06010401bf0803020a$80001f88808106d566db57fd6000000000$9e4681768d5dee9e2d0ca7380dfa19f0a0f805c550142b889af548f5506c2c3587df980707600b58d97ed1beaa9feaf9", "is_precomputed": false, "is_popular": false}, {"name": "WPA-EAPOL-PBKDF2", "hashcat_id": 2500, "description": "Network Protocol", "category": "Network Protocol", "example": "https://hashcat.net/misc/example_hashes/hashcat.hccapx", "is_precomputed": false, "is_popular": true}, {"name": "WPA-EAPOL-PMK", "hashcat_id": 2501, "description": "Network Protocol", "category": "Network Protocol", "example": "https://hashcat.net/misc/example_hashes/hashcat-pmk.hccapx", "is_precomputed": false, "is_popular": false}, {"name": "WPA-PBKDF2-PMKID+EAPOL", "hashcat_id": 22000, "description": "Network Protocol", "category": "Network Protocol", "example": "WPA*02*024022795224bffca545276c3762686f*6466b38ec3fc*225edc49b7aa*54502d4c494e4b5f484153484341545f54455354*10e3be3b005a629e89de088d6a2fdc489db83ad4764f2d186b9cde15446e972e*0103007502010a0000000000000000000148ce2ccba9c1fda130ff2fbbfb4fd3b063d1a93920b0f7df54a5cbf787b16171000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001630140100000fac040100000fac040100000fac028000*a2", "is_precomputed": false, "is_popular": true}, {"name": "WPA-PMK-PMKID+EAPOL", "hashcat_id": 22001, "description": "Network Protocol", "category": "Network Protocol", "example": "WPA*01*5ce7ebe97a1bbfeb2822ae627b726d5b*27462da350ac*accd10fb464e*686173686361742d6573736964***", "is_precomputed": false, "is_popular": false}, {"name": "WPA-PMKID-PBKDF2", "hashcat_id": 16800, "description": "Network Protocol", "category": "Network Protocol", "example": "2582a8281bf9d4308d6f5731d0e61c61*4604ba734d4e*89acf0e761f4*ed487162465a774bfba60eb603a39f3a", "is_precomputed": false, "is_popular": true}, {"name": "WPA-PMKID-PMK", "hashcat_id": 16801, "description": "Network Protocol", "category": "Network Protocol", "example": "2582a8281bf9d4308d6f5731d0e61c61*4604ba734d4e*89acf0e761f4", "is_precomputed": false, "is_popular": false}, {"name": "IPMI2 RAKP HMAC-SHA1", "hashcat_id": 7300, "description": "Network Protocol", "category": "Network Protocol", "example": "b7c2d6f13a43dce2e44ad120a9cd8a13d0ca23f0414275c0bbe1070d2d1299b1c04da0f1a0f1e4e2537300263a2200000000000000000000140768617368636174:472bdabe2d5d4bffd6add7b3ba79a291d104a9ef", "is_precomputed": false, "is_popular": false}, {"name": "CRAM-MD5", "hashcat_id": 10200, "description": "Network Protocol", "category": "Network Protocol", "example": "$cram_md5$********************************$dXNlciA0NGVhZmQyMmZlNzY2NzBmNmIyODc5MDgxYTdmNWY3MQ==", "is_precomputed": false, "is_popular": false}, {"name": "JWT (JSON Web Token)", "hashcat_id": 16500, "description": "Network Protocol", "category": "Network Protocol", "example": "eyJhbGciOiJIUzI1NiJ9.eyIzNDM2MzQyMCI6NTc2ODc1NDd9.f1nXZ3V_Hrr6ee-AFCTLaHRnrkiKmio2t3JqwL32guY", "is_precomputed": false, "is_popular": true}, {"name": "Radmin3", "hashcat_id": 29200, "description": "Network Protocol", "category": "Network Protocol", "example": "$radmin3$75007300650072006e0061006d006500*c63bf695069d564844c4849e7df6d41f1fbc5f3a7d8fe27c5f20545a238398fa*0062fb848c21d606baa0a91d7177daceb69ad2f6d090c2f1b3a654cfb417be66f739ae952f5c7c5170743459daf854a22684787b24f8725337b3c3bd1e0f2a6285768ceccca77f26c579d42a66372df7782b2eefccb028a0efb51a4257dd0804d05e0a83f611f2a0f10ffe920568cc7af1ec426f450ec99ade1f2a4905fd319f8c190c2db0b0e24627d635bc2b4a2c4c9ae956b1e02784c9ce958eb9883c60ba8ea2731dd0e515f492c44f39324e4027587c1330f14216e17f212eaec949273797ae74497782ee8b6f640dd2d124c59db8c37724c8a5a63bad005f8e491b459ff1b92f861ab6d99a2548cb8902b0840c7f20a108ede6bf9a60093053781216fe", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 17, TGS-REP", "hashcat_id": 19600, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5tgs$17$user$realm$ae8434177efd09be5bc2eff8$90b4ce5b266821adc26c64f71958a475cf9348fce65096190be04f8430c4e0d554c86dd7ad29c275f9e8f15d2dab4565a3d6e21e449dc2f88e52ea0402c7170ba74f4af037c5d7f8db6d53018a564ab590fc23aa1134788bcc4a55f69ec13c0a083291a96b41bffb978f5a160b7edc828382d11aacd89b5a1bfa710b0e591b190bff9062eace4d26187777db358e70efd26df9c9312dbeef20b1ee0d823d4e71b8f1d00d91ea017459c27c32dc20e451ea6278be63cdd512ce656357c942b95438228e", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 17, Pre-Auth", "hashcat_id": 19800, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5pa$17$hashcat$HASHCATDOMAIN.COM$a17776abe5383236c58582f515843e029ecbff43706d177651b7b6cdb2713b17597ddb35b1c9c470c281589fd1d51cca125414d19e40e333", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 17, DB", "hashcat_id": 28800, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5db$17$test$TEST.LOCAL$1c41586d6c060071e08186ee214e725e", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 18, TGS-REP", "hashcat_id": 19700, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5tgs$18$user$realm$8efd91bb01cc69dd07e46009$7352410d6aafd72c64972a66058b02aa1c28ac580ba41137d5a170467f06f17faf5dfb3f95ecf4fad74821fdc7e63a3195573f45f962f86942cb24255e544ad8d05178d560f683a3f59ce94e82c8e724a3af0160be549b472dd83e6b80733ad349973885e9082617294c6cbbea92349671883eaf068d7f5dcfc0405d97fda27435082b82b24f3be27f06c19354bf32066933312c770424eb6143674756243c1bde78ee3294792dcc49008a1b54f32ec5d5695f899946d42a67ce2fb1c227cb1d2004c0", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 18, Pre-Auth", "hashcat_id": 19900, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5pa$18$hashcat$HASHCATDOMAIN.COM$96c289009b05181bfd32062962740b1b1ce5f74eb12e0266cde74e81094661addab08c0c1a178882c91a0ed89ae4e0e68d2820b9cce69770", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 18, DB", "hashcat_id": 28900, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5db$18$test$TEST.LOCAL$266b5a53a6d663c3f69174f3309acada8e467c097c7973699f86286a6cf1a6c7", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 23, AS-REQ Pre-Auth", "hashcat_id": 7500, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5pa$23$user$realm$salt$4e751db65422b2117f7eac7b721932dc8aa0d9966785ecd958f971f622bf5c42dc0c70b532363138363631363132333238383835", "is_precomputed": false, "is_popular": false}, {"name": "Kerberos 5, etype 23, TGS-REP", "hashcat_id": 13100, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5tgs$23$*user$realm$test/spn*$63386d22d359fe42230300d56852c9eb$891ad31d09ab89c6b3b8c5e5de6c06a7f49fd559d7a9a3c32576c8fedf705376cea582ab5938f7fc8bc741acf05c5990741b36ef4311fe3562a41b70a4ec6ecba849905f2385bb3799d92499909658c7287c49160276bca0006c350b0db4fd387adc27c01e9e9ad0c20ed53a7e6356dee2452e35eca2a6a1d1432796fc5c19d068978df74d3d0baf35c77de12456bf1144b6a750d11f55805f5a16ece2975246e2d026dce997fba34ac8757312e9e4e6272de35e20d52fb668c5ed", "is_precomputed": false, "is_popular": true}, {"name": "Kerberos 5, etype 23, AS-REP", "hashcat_id": 18200, "description": "Network Protocol", "category": "Network Protocol", "example": "$krb5asrep$23$<EMAIL>:3e156ada591263b8aab0965f5aebd837$007497cb51b6c8116d6407a782ea0e1c5402b17db7afa6b05a6d30ed164a9933c754d720e279c6c573679bd27128fe77e5fea1f72334c1193c8ff0b370fadc6368bf2d49bbfdba4c5dccab95e8c8ebfdc75f438a0797dbfb2f8a1a5f4c423f9bfc1fea483342a11bd56a216f4d5158ccc4b224b52894fadfba3957dfe4b6b8f5f9f9fe422811a314768673e0c924340b8ccb84775ce9defaa3baa0910b676ad0036d13032b0dd94e3b13903cc738a7b6d00b0b3c210d1f972a6c7cae9bd3c959acf7565be528fc179118f28c679f6deeee1456f0781eb8154e18e49cb27b64bf74cd7112a0ebae2102ac", "is_precomputed": false, "is_popular": true}, {"name": "NetNTLMv1 / NetNTLMv1+ESS", "hashcat_id": 5500, "description": "Network Protocol", "category": "Network Protocol", "example": "u4-netntlm::kNS:338d08f8e26de93300000000000000000000000000000000:9526fb8c23a90751cdd619b6cea564742e1e4bf33006ba41:cb8086049ec4736c", "is_precomputed": false, "is_popular": true}, {"name": "NetNTLMv1 / NetNTLMv1+ESS (NT)", "hashcat_id": 27000, "description": "Network Protocol", "category": "Network Protocol", "example": "::5V4T:ada06359242920a500000000000000000000000000000000:0556d5297b5daa70eaffde82ef99293a3f3bb59b7c9704ea:9c23f6c094853920", "is_precomputed": false, "is_popular": false}, {"name": "NetNTLMv2", "hashcat_id": 5600, "description": "Network Protocol", "category": "Network Protocol", "example": "admin::N46iSNekpT:08ca45b7d7ea58ee:88dcbe4446168966a153a0064958dac6:5c7830315c7830310000000000000b45c67103d07d7b95acd12ffa11230e0000000052920b85f78d013c31cdb3b92f5d765c783030", "is_precomputed": false, "is_popular": true}, {"name": "NetNTLMv2 (NT)", "hashcat_id": 27100, "description": "Network Protocol", "category": "Network Protocol", "example": "0UL5G37JOI0SX::6VB1IS0KA74:ebe1afa18b7fbfa6:aab8bf8675658dd2a939458a1077ba08:010100000000000031c8aa092510945398b9f7b7dde1a9fb00000000f7876f2b04b700", "is_precomputed": false, "is_popular": false}, {"name": "Flask Session Cookie ($salt.$salt.$pass)", "hashcat_id": 29100, "description": "Network Protocol", "category": "Network Protocol", "example": "eyJ1c2VybmFtZSI6ImFkbWluIn0.YjdgRQ.1OTlf1PD0H9wXsu_qS0aywAJVD8", "is_precomputed": false, "is_popular": false}, {"name": "iSCSI CHAP authentication, MD5(CHAP)", "hashcat_id": 4800, "description": "Network Protocol", "category": "Network Protocol", "example": "afd09efdd6f8ca9f18ec77c5869788c3:01020304050607080910111213141516:01", "is_precomputed": false, "is_popular": false}, {"name": "RACF", "hashcat_id": 8500, "description": "Operating System", "category": "Operating System", "example": "$racf$*USER*FC2577C6EBE6265B", "is_precomputed": false, "is_popular": false}, {"name": "AIX {smd5}", "hashcat_id": 6300, "description": "Operating System", "category": "Operating System", "example": "{smd5}a5/yTL/u$VfvgyHx1xUlXZYBocQpQY0", "is_precomputed": false, "is_popular": false}, {"name": "AIX {ssha1}", "hashcat_id": 6700, "description": "Operating System", "category": "Operating System", "example": "{ssha1}06$bJbkFGJAB30L2e23$dCESGOsP7jaIIAJ1QAcmaGeG.kr", "is_precomputed": false, "is_popular": false}, {"name": "AIX {ssha256}", "hashcat_id": 6400, "description": "Operating System", "category": "Operating System", "example": "{ssha256}06$aJckFGJAB30LTe10$ohUsB7LBPlgclE3hJg9x042DLJvQyxVCX.nZZLEz.g2", "is_precomputed": false, "is_popular": false}, {"name": "AIX {ssha512}", "hashcat_id": 6500, "description": "Operating System", "category": "Operating System", "example": "{ssha512}06$bJbkFGJAB30L2e23$bXiXjyH5YGIyoWWmEVwq67nCU5t7GLy9HkCzrodRCQCx3r9VvG98o7O3V0r9cVrX3LPPGuHqT5LLn0oGCuI1..", "is_precomputed": false, "is_popular": false}, {"name": "LM", "hashcat_id": 3000, "description": "Operating System", "category": "Operating System", "example": "299bd128c1101fd6", "is_precomputed": false, "is_popular": true}, {"name": "QNX /etc/shadow (MD5)", "hashcat_id": 19000, "description": "Operating System", "category": "Operating System", "example": "@m@75f6f129f9c9e77b6b1b78f791ed764a@8741857532330050", "is_precomputed": false, "is_popular": false}, {"name": "QNX /etc/shadow (SHA256)", "hashcat_id": 19100, "description": "Operating System", "category": "Operating System", "example": "@s@0b365cab7e17ee1e7e1a90078501cc1aa85888d6da34e2f5b04f5c614b882a93@5498317092471604", "is_precomputed": false, "is_popular": false}, {"name": "QNX /etc/shadow (SHA512)", "hashcat_id": 19200, "description": "Operating System", "category": "Operating System", "example": "@S@715df9e94c097805dd1e13c6a40f331d02ce589765a2100ec7435e76b978d5efc364ce10870780622cee003c9951bd92ec1020c924b124cfff7e0fa1f73e3672@2257314490293159", "is_precomputed": false, "is_popular": false}, {"name": "DPAPI masterkey file v1 (context 1 and 2)", "hashcat_id": 15300, "description": "Operating System", "category": "Operating System", "example": "$DPAPImk$1*1*S-15-21-466364039-425773974-453930460-1925*des3*sha1*24000*b038489dee5ad04e3e3cab4d957258b5*208*cb9b5b7d96a0d2a00305ca403d3fd9c47c561e35b4b2cf3aebfd1d3199a6481d56972be7ebd6c291b199e6f1c2ffaee91978706737e9b1209e6c7d3aa3d8c3c3e38ad1ccfa39400d62c2415961c17fd0bd6b0f7bbd49cc1de1a394e64b7237f56244238da8d37d78", "is_precomputed": false, "is_popular": false}, {"name": "DPAPI masterkey file v1 (context 3)", "hashcat_id": 15310, "description": "Operating System", "category": "Operating System", "example": "$DPAPImk$1*3*S-15-21-407415836-404165111-436049749-1915*des3*sha1*14825*3e86e7d8437c4d5582ff668a83632cb2*208*96ad763b59e67c9f5c3d925e42bbe28a1412b919d1dc4abf03b2bed4c5c244056c14931d94d441117529b7171dfd6ebbe6eecf5d958b65574c293778fbadb892351cc59d5c65d65d2fcda73f5b056548a4a5550106d03d0c39d3cca7e5cdc0d521f48ac9e51cecc5", "is_precomputed": false, "is_popular": false}, {"name": "DPAPI masterkey file v2 (context 1 and 2)", "hashcat_id": 15900, "description": "Operating System", "category": "Operating System", "example": "$DPAPImk$2*2*S-15-21-423929668-478423897-489523715-1834*aes256*sha512*8000*740866e4105c77f800f02d367dd96699*288*ebc2907e16245dfe6c902ad4be70a079e62204c8a947498455056d150e6babb3c90b1616a8dff0e390dd26dda1978dffcbd7b9d7d1ea5c6d3e4df36db4d977051ec01fd6f0882a597c51834cb86445cad50c716f48b37cfd24339d8b43da771526fb01376798251edaa868fa2b1fa85c4142864b899987d4bbdc87b53433ed945fa4ab49c7f9d4d01df3ae19f25013b2", "is_precomputed": false, "is_popular": false}, {"name": "DPAPI masterkey file v2 (context 3)", "hashcat_id": 15910, "description": "Operating System", "category": "Operating System", "example": "$DPAPImk$2*3*S-15-21-464497560-472124119-475628788-1088*aes256*sha512*13450*685165fdb6d0627a15100215ec331ed8*288*7e1ea6f7ec3c2111f2a3903c73aefe66e524d8b241dc1482d0bd07cc1f3ccdadd8cebd4263b3b7c2496da48f40d2eb4890513e6624aeefbe6bbc6ea73f2f71fecf9cc5fef3891a2e697a4415ba7a069642069c142335d53cc750d42a4f4d2b0592956b4a0e57a5d5b1bfd27f0a8eac9d0d5fc5c5e5e23af18ce1a8eff442ed335e5db3bad6e89146f71aa9351e132fd9", "is_precomputed": false, "is_popular": false}, {"name": "GRUB 2", "hashcat_id": 7200, "description": "Operating System", "category": "Operating System", "example": "grub.pbkdf2.sha512.10000.7d391ef48645f626b427b1fae06a7219b5b54f4f02b2621f86b5e36e83ae492bd1db60871e45bc07925cecb46ff8ba3db31c723c0c6acbd4f06f60c5b246ecbf.26d59c52b50df90d043f070bd9cbcd92a74424da42b3666fdeb08f1a54b8f1d2f4f56cf436f9382419c26798dc2c209a86003982b1e5a9fcef905f4dfaa4c524", "is_precomputed": false, "is_popular": false}, {"name": "MS-AzureSync PBKDF2-HMAC-SHA256", "hashcat_id": 12800, "description": "Operating System", "category": "Operating System", "example": "v1;PPH1_MD4,84840328224366186645,100,005a491d8bf3715085d69f934eef7fb19a15ffc233b5382d9827910bc32f3506", "is_precomputed": false, "is_popular": false}, {"name": "BSDi Crypt, Extended DES", "hashcat_id": 12400, "description": "Operating System", "category": "Operating System", "example": "_9G..8147mpcfKT8g0U.", "is_precomputed": false, "is_popular": false}, {"name": "NTLM", "hashcat_id": 1000, "description": "Operating System", "category": "Operating System", "example": "b4b9b02e6f09a9bd760f388b67351e2b", "is_precomputed": false, "is_popular": true}, {"name": "Radmin2", "hashcat_id": 9900, "description": "Operating System", "category": "Operating System", "example": "22527bee5c29ce95373c4e0f359f079b", "is_precomputed": false, "is_popular": false}, {"name": "Samsung Android Password/PIN", "hashcat_id": 5800, "description": "Operating System", "category": "Operating System", "example": "0223b799d526b596fe4ba5628b9e65068227e68e:f6d45822728ddb2c", "is_precomputed": false, "is_popular": true}, {"name": "Windows Hello PIN/Password", "hashcat_id": 28100, "description": "Operating System", "category": "Operating System", "example": "$WINHELLO$*SHA512*10000*00761655*3b3d3197efb2839a6072e922cc03be910be55d1e60389689c05b520d2d57c06258dc5a48798ba65424004cbe2e003d0509036f3394bcae108eb6b77c7eb306d7*c0772a3aca949db60f274f315b3a5f63fea552fc0d1f2032db5293ca9690735217d918d4cf697aa45b2fe598168804040e18fe00758be94aac971985ea7a5521*bff47e398df761733b5aeda7035cdf289547db3afb94b70cbad2aaea21a5cd58*8a4d5b88832e10bad57303324e6c9021733733df4acbf91366f51cebdc755e00fe1d01b3202469ee6ad5e667975b4f50e3110b00ef60414cd2cf96cc47df532e36b997727ffec2924d979d3fb6e677cb5827f4313131a46be8712926c42158339b55183e2fd7f2f0761980b1413897825c3759c566ff8a438189a6c8fb2d630dc33c6330de45c784d11957c686b40b6fe31fd8f2b1b664f542392326af5d334fdf92155343335e1b964955ac0b0e6f7254a599f0f0dc99becc2216515ba9e9472a54e60a14507fc353ebc47b9f0a8249a2a1bfa5d2cf526bd15ee68bd52e944ece9de6bbda913bc5083e26229673340fcc5285df0d38cbc7bb14584ced2fe9e9b3c283fa3c5ad4dd2034b7a67c8e7a1632fae8979a0abdd19be91c6bc371966121e04d433923e44df0b60c156bd90bc61c9fed01a7a76353f79dd4da3e07e12810ec3765128ec44b44b0789d6aa9e9702211a22ab8055ea32e9513fb1bd9d24ca04b33282632f63ab1b213e9644f97bc31dc4d2e7050c1fa23c0000facbf7c76fd7be4b112586f73f0c27abcf7cbe8c9d9fb83af70f60c490936fef84ed5301f73917b4e4170674a5d5e4bfbebdfeda9584221a0f190545efea7245dd2517ade393bedc255c4e016d9919e6e3f3711bca677fc099bf4e1730a752ea2a90a20ff3d09c909771849d3b009ba8d95d2b84fff889e38b079f1325aa42daa067a52abb5c064de3a5040e4a64e76b397b5c9ee6d045f3b5150cf428a92c141735908bb278077d52beefdc87efa156b8ebda071cb425fad0372a8a7cb6eb29926e8f6411ff1b818750c5b6888302fee9b1591b1c23db131538db2aa3de61dcd76fb7067be7ab71ee372bac18be0f446c974e92e79e27e7e3b2aa5ffc3f5f923f2df8ac2edcbb9392d1ac35e4cd52037d9dceedec6391e713e78770307bfde6a31b4e115904d285ac35db055ae8253b9968b7ed7b948da5f*785435725a573571565662727670754100", "is_precomputed": false, "is_popular": false}, {"name": "Windows Phone 8+ PIN/password", "hashcat_id": 13800, "description": "Operating System", "category": "Operating System", "example": "95fc4680bcd2a5f25de3c580cbebadbbf256c1f0ff2e9329c58e36f8b914c11f:4471347156480581513210137061422464818088437334031753080747625028271635402815635172140161077854162657165115624364524648202480341513407048222056541500234214433548175101668212658151115765112202168288664210443352443335235337677853484573107775345675846323265745", "is_precomputed": false, "is_popular": false}, {"name": "Cisco-ASA MD5", "hashcat_id": 2410, "description": "Operating System", "category": "Operating System", "example": "02dMBMYkTdC5Ziyp:36", "is_precomputed": false, "is_popular": false}, {"name": "Cisco-IOS $8$ (PBKDF2-SHA256)", "hashcat_id": 9200, "description": "Operating System", "category": "Operating System", "example": "$8$TnGX/fE4KGHOVU$pEhnEvxrvaynpi8j4f.EMHr6M.FzU8xnZnBr/tJdFWk", "is_precomputed": false, "is_popular": false}, {"name": "Cisco-IOS $9$ (scrypt)", "hashcat_id": 9300, "description": "Operating System", "category": "Operating System", "example": "$9$2MJBozw/9R3UsU$2lFhcKvpghcyw8deP25GOfyZaagyUOGBymkryvOdfo6", "is_precomputed": false, "is_popular": false}, {"name": "Cisco-IOS type 4 (SHA256)", "hashcat_id": 5700, "description": "Operating System", "category": "Operating System", "example": "2btjjy78REtmYkkW0csHUbJZOstRXoWdX1mGrmmfeHI", "is_precomputed": false, "is_popular": true}, {"name": "Cisco-PIX MD5", "hashcat_id": 2400, "description": "Operating System", "category": "Operating System", "example": "dRRVnUmUHXOTt9nk", "is_precomputed": false, "is_popular": false}, {"name": "Citrix NetScaler (SHA1)", "hashcat_id": 8100, "description": "Operating System", "category": "Operating System", "example": "1765058016a22f1b4e076dccd1c3df4e8e5c0839ccded98ea", "is_precomputed": false, "is_popular": false}, {"name": "Citrix NetScaler (SHA512)", "hashcat_id": 22200, "description": "Operating System", "category": "Operating System", "example": "2f9282ade42ce148175dc3b4d8b5916dae5211eee49886c3f7cc768f6b9f2eb982a5ac2f2672a0223999bfd15349093278adf12f6276e8b61dacf5572b3f93d0b4fa886ce", "is_precomputed": false, "is_popular": false}, {"name": "Domain Cached Credentials (DCC), MS Cache", "hashcat_id": 1100, "description": "Operating System", "category": "Operating System", "example": "4dd8965d1d476fa0d026722989a6b772:3060147285011", "is_precomputed": false, "is_popular": true}, {"name": "Domain Cached Credentials 2 (DCC2), MS Cache 2", "hashcat_id": 2100, "description": "Operating System", "category": "Operating System", "example": "$DCC2$10240#tom#e4e938d12fe5974dc42a90120bd9c90f", "is_precomputed": false, "is_popular": true}, {"name": "FortiGate (FortiOS)", "hashcat_id": 7000, "description": "Operating System", "category": "Operating System", "example": "AK1AAECAwQFBgcICRARNGqgeC3is8gv2xWWRony9NJnDgE=", "is_precomputed": false, "is_popular": false}, {"name": "FortiGate256 (FortiOS256)", "hashcat_id": 26300, "description": "Operating System", "category": "Operating System", "example": "SH2MCKr6kt9rLQKbn/YTlncOnR6OtcJ1YL/h8hw2wWicjSRf3bbkSrL+q6cDpg=", "is_precomputed": false, "is_popular": false}, {"name": "ArubaOS", "hashcat_id": 125, "description": "Operating System", "category": "Operating System", "example": "5387280701327dc2162bdeb451d5a465af6d13eff9276efeba", "is_precomputed": false, "is_popular": false}, {"name": "Juniper IVE", "hashcat_id": 501, "description": "Operating System", "category": "Operating System", "example": "3u+UR6n8AgABAAAAHxxdXKmiOmUoqKnZlf8lTOhlPYy93EAkbPfs5+49YLFd/B1+omSKbW7DoqNM40/EeVnwJ8kYoXv9zy9D5C5m5A==", "is_precomputed": false, "is_popular": false}, {"name": "Juniper NetScreen/SSG (ScreenOS)", "hashcat_id": 22, "description": "Operating System", "category": "Operating System", "example": "nNxKL2rOEkbBc9BFLsVGG6OtOUO/8n:user", "is_precomputed": false, "is_popular": false}, {"name": "Juniper/NetBSD sha1crypt", "hashcat_id": 15100, "description": "Operating System", "category": "Operating System", "example": "$sha1$15100$jiJDkz0E$E8C7RQAD3NetbSDz7puNAY.5Y2jr", "is_precomputed": false, "is_popular": false}, {"name": "iPhone passcode (UID key + System Keybag)", "hashcat_id": 26500, "description": "Operating System", "category": "Operating System", "example": "$uido$77889b1bca161ce876d976a102c7bf82$3090545724551425617156367874312887832777$50000$2d4c86b71c0c04129a47c6468e2437d1fecd88e232a7b15112d5364682dc391dbbbb921cf6e02664", "is_precomputed": false, "is_popular": false}, {"name": "macOS v10.4, macOS v10.5, macOS v10.6", "hashcat_id": 122, "description": "Operating System", "category": "Operating System", "example": "1430823483d07626ef8be3fda2ff056d0dfd818dbfe47683", "is_precomputed": false, "is_popular": false}, {"name": "macOS v10.7", "hashcat_id": 1722, "description": "Operating System", "category": "Operating System", "example": "648742485c9b0acd786a233b2330197223118111b481abfa0ab8b3e8ede5f014fc7c523991c007db6882680b09962d16fd9c45568260531bdb34804a5e31c22b4cfeb32d", "is_precomputed": false, "is_popular": false}, {"name": "macOS v10.8+ (PBKDF2-SHA512)", "hashcat_id": 7100, "description": "Operating System", "category": "Operating System", "example": "$ml$35460$93a94bd24b5de64d79a5e49fa372827e739f4d7b6975c752c9a0ff1e5cf72e05$752351df64dd2ce9dc9c64a72ad91de6581a15c19176266b44d98919dfa81f0f96cbcb20a1ffb400718c20382030f637892f776627d34e021bad4f81b7de8222", "is_precomputed": false, "is_popular": false}, {"name": "bcrypt $2*$, Blowfish (Unix)", "hashcat_id": 3200, "description": "Operating System", "category": "Operating System", "example": "$2a$05$LhayLxezLhK1LhWvKxCyLOj0j1u.Kj0jZ0pEmm134uzrQlFvQJLF6", "is_precomputed": false, "is_popular": true}, {"name": "md5crypt, MD5 (Unix), Cisco-IOS $1$ (MD5)", "hashcat_id": 500, "description": "Operating System", "category": "Operating System", "example": "$1$28772684$iEwNOgGugqO9.bIz5sk8k/", "is_precomputed": false, "is_popular": false}, {"name": "descrypt, DES (Unix), Traditional DES", "hashcat_id": 1500, "description": "Operating System", "category": "Operating System", "example": "48c/R8JAv757A", "is_precomputed": false, "is_popular": false}, {"name": "sha1($salt.sha1(utf16le($username).':'.utf16le($pass)))", "hashcat_id": 29000, "description": "Operating System", "category": "Operating System", "example": "339b5eaa53f28516008e9ca710857d3a4785b6fc:8ca064ff42fcab5a8f0692544b8dd3d3054bd73fe9afaa08c6b6b310538cc9a7:757365726e616d65", "is_precomputed": false, "is_popular": false}, {"name": "sha256crypt $5$, SHA256 (Unix)", "hashcat_id": 7400, "description": "Operating System", "category": "Operating System", "example": "$5$rounds=5000$GX7BopJZJxPc/KEK$le16UF8I2Anb.rOrn22AUPWvzUETDGefUmAV8AZkGcD", "is_precomputed": false, "is_popular": false}, {"name": "sha512crypt $6$, SHA512 (Unix)", "hashcat_id": 1800, "description": "Operating System", "category": "Operating System", "example": "$6$52450745$k5ka2p8bFuSmoVT1tzOyyuaREkkKBcCNqoDKzYiJL9RaE8yMnPgh2XzzF0NDrUhgrcLwg78xs1w5pJiypEdFX/", "is_precomputed": false, "is_popular": false}, {"name": "SQLCipher", "hashcat_id": 24600, "description": "Database Server", "category": "Database Server", "example": "SQLCIPHER*1*64000*25548249195677404156261816261456*85b5e156e1cf1e0be5e9f4217186817b*33435c230bbc7989bbd027630e3f47cd", "is_precomputed": false, "is_popular": false}, {"name": "MSSQL (2000)", "hashcat_id": 131, "description": "Database Server", "category": "Database Server", "example": "0x01002702560500000000000000000000000000000000000000008db43dd9b1972a636ad0c7d4b8c515cb8ce46578", "is_precomputed": false, "is_popular": false}, {"name": "MSSQL (2005)", "hashcat_id": 132, "description": "Database Server", "category": "Database Server", "example": "0x010018102152f8f28c8499d8ef263c53f8be369d799f931b2fbe", "is_precomputed": false, "is_popular": false}, {"name": "MSSQL (2012, 2014)", "hashcat_id": 1731, "description": "Database Server", "category": "Database Server", "example": "0x02000102030434ea1b17802fd95ea6316bd61d2c94622ca3812793e8fb1672487b5c904a45a31b2ab4a78890d563d2fcf5663e46fe797d71550494be50cf4915d3f4d55ec375", "is_precomputed": false, "is_popular": false}, {"name": "MongoDB ServerKey SCRAM-SHA-1", "hashcat_id": 24100, "description": "Database Server", "category": "Database Server", "example": "$mongodb-scram$*0*dXNlcg==*10000*4p+f1tKpK18hQqrVr0UGOw==*Jv9lrpUQ2bVg2ZkXvRm2rppsqNw=", "is_precomputed": false, "is_popular": false}, {"name": "MongoDB ServerKey SCRAM-SHA-256", "hashcat_id": 24200, "description": "Database Server", "category": "Database Server", "example": "$mongodb-scram$*1*dXNlcg==*15000*qYaA1K1ZZSSpWfY+yqShlcTn0XVcrNipxiYCLQ==*QWVry9aTS/JW+y5CWCBr8lcEH9Kr/D4je60ncooPer8=", "is_precomputed": false, "is_popular": false}, {"name": "PostgreSQL", "hashcat_id": 12, "description": "Database Server", "category": "Database Server", "example": "a6343a68d964ca596d9752250d54bb8a:postgres", "is_precomputed": false, "is_popular": false}, {"name": "PostgreSQL CRAM (MD5)", "hashcat_id": 11100, "description": "Database Server", "category": "Database Server", "example": "$postgres$postgres*f0784ea5*2091bb7d4725d1ca85e8de6ec349baf6", "is_precomputed": false, "is_popular": false}, {"name": "PostgreSQL SCRAM-SHA-256", "hashcat_id": 28600, "description": "Database Server", "category": "Database Server", "example": "SCRAM-SHA-256$4096:IKfxzJ8Nq4PkLJCfgKcPmA==$iRw3qwTp18uaBnsTOEExbtgWdKeBMbSSnZvqD4sdqLQ=:hPciC1CcnBna3szR8Mf3MVc8t0W7QPbIHoMMrh4zRV0=", "is_precomputed": false, "is_popular": false}, {"name": "Oracle H: Type (Oracle 7+)", "hashcat_id": 3100, "description": "Database Server", "category": "Database Server", "example": "7A963A529D2E3229:3682427524", "is_precomputed": false, "is_popular": false}, {"name": "Oracle S: Type (Oracle 11+)", "hashcat_id": 112, "description": "Database Server", "category": "Database Server", "example": "ac5f1e62d21fd0529428b84d42e8955b04966703:38445748184477378130", "is_precomputed": false, "is_popular": false}, {"name": "Oracle T: Type (Oracle 12+)", "hashcat_id": 12300, "description": "Database Server", "category": "Database Server", "example": "78281A9C0CF626BD05EFC4F41B515B61D6C4D95A250CD4A605CA0EF97168D670EBCB5673B6F5A2FB9CC4E0C0101E659C0C4E3B9B3BEDA846CD15508E88685A2334141655046766111066420254008225", "is_precomputed": false, "is_popular": false}, {"name": "MySQL $A$ (sha256crypt)", "hashcat_id": 7401, "description": "Database Server", "category": "Database Server", "example": "$mysql$A$005*F9CC98CE08892924F50A213B6BC571A2C11778C5*625479393559393965414D45316477456B484F41316E64484742577A2E3162785353526B7554584647562F", "is_precomputed": false, "is_popular": false}, {"name": "MySQL CRAM (SHA1)", "hashcat_id": 11200, "description": "Database Server", "category": "Database Server", "example": "$mysqlna$1c24ab8d0ee94d70ab1f2e814d8f0948a14d10b9*437e93572f18ae44d9e779160c2505271f85821d", "is_precomputed": false, "is_popular": false}, {"name": "MySQL323", "hashcat_id": 200, "description": "Database Server", "category": "Database Server", "example": "7196759210defdc0", "is_precomputed": false, "is_popular": false}, {"name": "MySQL4.1/MySQL5", "hashcat_id": 300, "description": "Database Server", "category": "Database Server", "example": "fcf7c1b8749cf99d88e5f34271d636178fb5d130", "is_precomputed": false, "is_popular": false}, {"name": "Sybase ASE", "hashcat_id": 8000, "description": "Database Server", "category": "Database Server", "example": "0xc00778168388631428230545ed2c976790af96768afa0806fe6c0da3b28f3e132137eac56f9bad027ea2", "is_precomputed": false, "is_popular": false}, {"name": "DNSSEC (NSEC3)", "hashcat_id": 8300, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "7b5n74kq8r441blc2c5qbbat19baj79r:.lvdsiqfj.net:33164473:1", "is_precomputed": false, "is_popular": false}, {"name": "KNX IP Secure - Device Authentication Code", "hashcat_id": 25900, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "$knx-ip-secure-device-authentication-code$*3033*fa7c0d787a9467c209f0a6e7cf16069ed704f3959dce19e45d7935c0a91bce41*f927640d9bbe9a4b0b74dd3289ad41ec", "is_precomputed": false, "is_popular": false}, {"name": "CRAM-MD5 <PERSON>", "hashcat_id": 16400, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "{CRAM-MD5}5389b33b9725e5657cb631dc50017ff1535ce4e2a1c414009126506fc4327d0d", "is_precomputed": false, "is_popular": false}, {"name": "SSHA-256(Base64), LDAP {SSHA256}", "hashcat_id": 1411, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "{SSHA256}OZiz0cnQ5hgyel3Emh7NCbhBRCQ+HVBwYplQunHYnER7TLuV", "is_precomputed": false, "is_popular": false}, {"name": "SSHA-512(Base64), LDAP {SSHA512}", "hashcat_id": 1711, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "{SSHA512}ALtwKGBdRgD+U0fPAy31C28RyKYx7+a8kmfksccsOeLknLHv2DBXYI7TDnTolQMBuPkWDISgZr2cHfnNPFjGZTEyNDU4OTkw", "is_precomputed": false, "is_popular": false}, {"name": "Dahua Authentication MD5", "hashcat_id": 24900, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "GRuHbyVp", "is_precomputed": false, "is_popular": false}, {"name": "RedHat 389-DS LDAP (PBKDF2-HMAC-SHA256)", "hashcat_id": 10901, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "{PBKDF2_SHA256}AAAgADkxMjM2NTIzMzgzMjQ3MjI4MDAwNTk5OTAyOTk4NDI2MjkyMzAzNjg0NjQwOTMxNjI3OTMzNjg0MDI0OTY5NTe5ULagRTYpLaUoeqJMg8x9W/DXu+9VTFaVhaYvebYrY+sOqn1ZMRnws22C1uAkiE2tFM8qN+xw5xe7OmCPZ203NuruK4oB33QlsKIEz4ppm0TR94JB9PJx7lIQwFHD3FUNUNryj4jk6UYyJ4+V1Z9Ug/Iy/ylQBJgfs5ihzgxHYZrfp1wUCXFzlZG9mxmziPm8VFnAhaX4+FBAZvLAx33jpbKOwEg7TmwP2VJ8BNFLQRqwYdlqIjQlAhncXH+dqIF9VdM4MonAA0hx76bMvFTP7LF5VO1IqVmcuYz7YG9v4KKRjnvoUUqOj6okUBQTay3EzsdFVnUW1FemYOccJd5q", "is_precomputed": false, "is_popular": false}, {"name": "ColdFusion 10+", "hashcat_id": 12600, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "aee9edab5653f509c4c63e559a5e967b4c112273bc6bd84525e630a3f9028dcb:5136256866783777334574783782810410706883233321141647265340462733", "is_precomputed": false, "is_popular": false}, {"name": "Apache $apr1$ MD5, md5apr1, MD5 (APR)", "hashcat_id": 1600, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "$apr1$71850310$gh9m4xcAn3MGxogwX/ztb.", "is_precomputed": false, "is_popular": false}, {"name": "Episerver 6.x < .NET 4", "hashcat_id": 141, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "$episerver$*0*bEtiVGhPNlZpcUN4a3ExTg==*utkfN0EOgljbv5FoZ6+AcZD5iLk", "is_precomputed": false, "is_popular": false}, {"name": "hMailServer", "hashcat_id": 1421, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "8fe7ca27a17adc337cd892b1d959b4e487b8f0ef09e32214f44fb1b07e461c532e9ec3", "is_precomputed": false, "is_popular": false}, {"name": "nsldap, SHA-1(Base64), Netscape LDAP SHA", "hashcat_id": 101, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "{SHA}uJ6qx+YUFzQbcQtyd2gpTQ5qJ3s=", "is_precomputed": false, "is_popular": false}, {"name": "nslda<PERSON>, SSHA-1(Base64), Netscape LDAP SSHA", "hashcat_id": 111, "description": "FTP, HTTP, SMTP, LDAP Server", "category": "FTP, HTTP, SMTP, LDAP Server", "example": "{SSHA}AZKja92fbuuB9SpRlHqaoXxbTc43Mzc2MDM1Ng==", "is_precomputed": false, "is_popular": false}, {"name": "SAP CODVN B (BCODE)", "hashcat_id": 7700, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "USER$C8B48F26B87B7EA7", "is_precomputed": false, "is_popular": false}, {"name": "SAP CODVN B (BCODE) from RFC_READ_TABLE", "hashcat_id": 7701, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "027642760180$77EC386300000000", "is_precomputed": false, "is_popular": false}, {"name": "SAP CODVN F/G (PASSCODE)", "hashcat_id": 7800, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "USER$ABCAD719B17E7F794DF7E686E563E9E2D24DE1D0", "is_precomputed": false, "is_popular": false}, {"name": "SAP CODVN F/G (PASSCODE) from RFC_READ_TABLE", "hashcat_id": 7801, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "604020408266$32837BA7B97672BA4E5A00000000000000000000", "is_precomputed": false, "is_popular": false}, {"name": "SAP CODVN H (PWDSALTEDHASH) iSSHA-1", "hashcat_id": 10300, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "{x-issha, 1024}C0624EvGSdAMCtuWnBBYBGA0chvqAflKY74oEpw/rpY=", "is_precomputed": false, "is_popular": false}, {"name": "PeopleSoft", "hashcat_id": 133, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "uXmFVrdBvv293L9kDR3VnRmx4ZM=", "is_precomputed": false, "is_popular": false}, {"name": "PeopleSoft PS_TOKEN", "hashcat_id": 13500, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "b5e335754127b25ba6f99a94c738e24cd634c35a:aa07d396f5038a6cbeded88d78d1d6c907e4079b3dc2e12fddee409a51cc05ae73e8cc24d518c923a2f79e49376594503e6238b806bfe33fa8516f4903a9b4", "is_precomputed": false, "is_popular": false}, {"name": "SolarWinds Orion", "hashcat_id": 21500, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "$solarwinds$0$admin$fj4EBQewCQUZ7IYHl0qL8uj9kQSBb3m7N4u0crkKK0Uj9rbbAnSrBZMXO7oWx9KqL3sCzwncvPZ9hyDV9QCFTg==", "is_precomputed": false, "is_popular": false}, {"name": "SolarWinds Orion v2", "hashcat_id": 21501, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "$solarwinds$1$3pHkk55NTYpAeV3EJjcAww==$N4Ii2PxXX/bTZZwslQLIKrp0wvfZ5aN9hpyiR896ozJMJTPO1Q7BK1Eht8Vhl4kXq/42Vn2zp3qYeAkRuqsuEw==", "is_precomputed": false, "is_popular": false}, {"name": "SolarWinds Serv-U", "hashcat_id": 24, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "e983672a03adcc9767b24584338eb378", "is_precomputed": false, "is_popular": false}, {"name": "Lotus Notes/Domino 5", "hashcat_id": 8600, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "3dd2e1e5ac03e230243d58b8c5ada076", "is_precomputed": false, "is_popular": false}, {"name": "Lotus Notes/Domino 6", "hashcat_id": 8700, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "(GDpOtD35gGlyDksQRxEU)", "is_precomputed": false, "is_popular": false}, {"name": "Lotus Notes/Domino 8", "hashcat_id": 9100, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "(HsjFebq0Kh9kH7aAZYc7kY30mC30mC3KmC30mCluagXrvWKj1)", "is_precomputed": false, "is_popular": false}, {"name": "OpenEdge Progress Encode", "hashcat_id": 26200, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "lebVZteiEsdpkncc", "is_precomputed": false, "is_popular": false}, {"name": "Oracle Transportation Management (SHA256)", "hashcat_id": 20600, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "otm_sha256:1000:1234567890:S5Q9Kc0ETY6ZPyQU+JYY60oFjaJuZZaSinggmzU8PC4=", "is_precomputed": false, "is_popular": false}, {"name": "Huawei sha1(md5($pass).$salt)", "hashcat_id": 4711, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "53c724b7f34f09787ed3f1b316215fc35c789504:hashcat1", "is_precomputed": false, "is_popular": false}, {"name": "AuthMe sha256", "hashcat_id": 20711, "description": "Enterprise Application Software (EAS)", "category": "Enterprise Application Software (EAS)", "example": "$SHA$7218532375810603$bfede293ecf6539211a7305ea218b9f3f608953130405cda9eaba6fb6250f824", "is_precomputed": false, "is_popular": false}, {"name": "AES Crypt (SHA256)", "hashcat_id": 22400, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$aescrypt$1*efc648908ca7ec727f37f3316dfd885c*eff5c87a35545406a57b56de57bd0554*3a66401271aec08cbd10cf2070332214093a33f36bd0dced4a4bb09fab817184*6a3c49fea0cafb19190dc4bdadb787e73b1df244c51780beef912598bd3bdf7e", "is_precomputed": false, "is_popular": false}, {"name": "VMware VMX (PBKDF2-HMAC-SHA1 + AES-256-CBC)", "hashcat_id": 27400, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$vmx$0$10000$264bbab02fdf7c1a793651120bec3723$cbb368564d8dfb99f509d4922f4693413f3816af713f0e76bc2409ff9336935d", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 (legacy)", "hashcat_id": 14600, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_testfiles.7z", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 RIPEMD-160 + AES", "hashcat_id": 29541, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_ripemd160_aes_cbc-essiv_256.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 RIPEMD-160 + Serpent", "hashcat_id": 29542, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_ripemd160_serpent_xts-plain64_256.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 RIPEMD-160 + Twofish", "hashcat_id": 29543, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_ripemd160_twofish_cbc-plain64_128.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-1 + AES", "hashcat_id": 29511, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha1_aes_cbc-essiv_128.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-1 + Serpent", "hashcat_id": 29512, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha1_serpent_cbc-plain64_256.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-1 + Twofish", "hashcat_id": 29513, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha1_twofish_xts-plain64_256.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-256 + AES", "hashcat_id": 29521, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha256_aes_cbc-plain64_128.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-256 + Serpent", "hashcat_id": 29522, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha256_serpent_xts-plain64_512.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-256 + Twofish", "hashcat_id": 29523, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha256_twofish_cbc-essiv_256.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-512 + AES", "hashcat_id": 29531, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha512_aes_cbc-plain64_256.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-512 + Serpent", "hashcat_id": 29532, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha512_serpent_cbc-essiv_128.txt", "is_precomputed": false, "is_popular": false}, {"name": "LUKS v1 SHA-512 + Twofish", "hashcat_id": 29533, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_luks_sha512_twofish_cbc-plain64_256.txt", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 512 bit (legacy)", "hashcat_id": 13711, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_ripemd160_twofish_13711.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1024 bit (legacy)", "hashcat_id": 13712, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_ripemd160_twofish-serpent_13712.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1536 bit (legacy)", "hashcat_id": 13713, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_ripemd160_serpent-twofish-aes_13713.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 512 bit + boot-mode (legacy)", "hashcat_id": 13741, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_ripemd160_aes_boot.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1024 bit + boot-mode (legacy)", "hashcat_id": 13742, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_ripemd160_aes-twofish_boot.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1536 bit + boot-mode (legacy)", "hashcat_id": 13743, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_ripemd160_aes-twofish-serpent_boot.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 512 bit", "hashcat_id": 29411, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$531aca1fa6db5118506320114cb11a9f00dade61720533fc12982b28ec71a1a3856ac6ee44b4acc207c8230352208d5f0dc37bf755bd98830279d6befcb6001c$df025f816a0aa1baf3b9b51be00fadb451ffbe9bdfc381115eeceeef778e29a8761f853b7c99e0ea9ec452ba77677f888ea40a39cf65db74d87147690684e273313dea15ff2039797e112006e5f80f2c5baf2c11eb62cb63cfb45883f8885fc7cd5bdb74ef57ec4fe3cec5c2025364582380366169d9419ac41b6f6e878429239e52538f9698e73700b920e7b58c56a4563f5aa512e334ddc56909ac2a0ad4146833f050edd78b7954e6549d0fa2e3b26ed2a769a6c029bfa4de62d49575acce078ef035e366ec13b6092cb205e481bc822f87972bfbe4a3915fad620c4b8645e96bcc468d5804208ae251a560068a09455657f4539dc7e80637fa85fbce058ffee421a98d85b2ae1118d9bd4f24e1e810627cc9893b7166e199dc91fd7f79740530a472df0948f285293478042b28cd2caef086a6ce9d5f656f97adde7d68924ef477fdf2a0c0b107671a1f94b2906d8fb58114836982e4e130e6944df8b42288512376553a1fa6526f9e46dc19b99bb568b30269d9f5d7db2d70a9aa85371b0ac71a6f6f564aaef26a0508c16bf03934973504a5188de37b18a689a020bc37a54d2863879e12902b43bc71c057fa47cbaac1e0100696af365e8226daeba346", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1024 bit", "hashcat_id": 29412, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$531aca1fa6db5118506320114cb11a9f00dade61720533fc12982b28ec71a1a3856ac6ee44b4acc207c8230352208d5f0dc37bf755bd98830279d6befcb6001c$df025f816a0aa1baf3b9b51be00fadb451ffbe9bdfc381115eeceeef778e29a8761f853b7c99e0ea9ec452ba77677f888ea40a39cf65db74d87147690684e273313dea15ff2039797e112006e5f80f2c5baf2c11eb62cb63cfb45883f8885fc7cd5bdb74ef57ec4fe3cec5c2025364582380366169d9419ac41b6f6e878429239e52538f9698e73700b920e7b58c56a4563f5aa512e334ddc56909ac2a0ad4146833f050edd78b7954e6549d0fa2e3b26ed2a769a6c029bfa4de62d49575acce078ef035e366ec13b6092cb205e481bc822f87972bfbe4a3915fad620c4b8645e96bcc468d5804208ae251a560068a09455657f4539dc7e80637fa85fbce058ffee421a98d85b2ae1118d9bd4f24e1e810627cc9893b7166e199dc91fd7f79740530a472df0948f285293478042b28cd2caef086a6ce9d5f656f97adde7d68924ef477fdf2a0c0b107671a1f94b2906d8fb58114836982e4e130e6944df8b42288512376553a1fa6526f9e46dc19b99bb568b30269d9f5d7db2d70a9aa85371b0ac71a6f6f564aaef26a0508c16bf03934973504a5188de37b18a689a020bc37a54d2863879e12902b43bc71c057fa47cbaac1e0100696af365e8226daeba346", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1536 bit", "hashcat_id": 29413, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$531aca1fa6db5118506320114cb11a9f00dade61720533fc12982b28ec71a1a3856ac6ee44b4acc207c8230352208d5f0dc37bf755bd98830279d6befcb6001c$df025f816a0aa1baf3b9b51be00fadb451ffbe9bdfc381115eeceeef778e29a8761f853b7c99e0ea9ec452ba77677f888ea40a39cf65db74d87147690684e273313dea15ff2039797e112006e5f80f2c5baf2c11eb62cb63cfb45883f8885fc7cd5bdb74ef57ec4fe3cec5c2025364582380366169d9419ac41b6f6e878429239e52538f9698e73700b920e7b58c56a4563f5aa512e334ddc56909ac2a0ad4146833f050edd78b7954e6549d0fa2e3b26ed2a769a6c029bfa4de62d49575acce078ef035e366ec13b6092cb205e481bc822f87972bfbe4a3915fad620c4b8645e96bcc468d5804208ae251a560068a09455657f4539dc7e80637fa85fbce058ffee421a98d85b2ae1118d9bd4f24e1e810627cc9893b7166e199dc91fd7f79740530a472df0948f285293478042b28cd2caef086a6ce9d5f656f97adde7d68924ef477fdf2a0c0b107671a1f94b2906d8fb58114836982e4e130e6944df8b42288512376553a1fa6526f9e46dc19b99bb568b30269d9f5d7db2d70a9aa85371b0ac71a6f6f564aaef26a0508c16bf03934973504a5188de37b18a689a020bc37a54d2863879e12902b43bc71c057fa47cbaac1e0100696af365e8226daeba346", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 512 bit + boot-mode", "hashcat_id": 29441, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$528c2997054ce1d22cbc5233463df8119a0318ab94aa715e6e686c898f36690b443221a18f578fb893e0db1e4b875cc711eab542e70e13b55d6aa26134e1a8d3$4f5ae6caaea7390a5e2f50130e85f9e551216dd0895f4fb0bcdec219246c249236771e1f2a1f447054d363c398ab367ed7f9574eb0611211e742f429cd53b56fcdb42d2eb183c134847dc6efc7c8293d6481aa53406f0446398591956f79ca3ce76e80208fd409d0f6f14c68312fc119ab4292972338b1457c73585ae2fc863bf202f141495de50253799cbc27010fba6de6b0a36888d12f4e3964aaaf43a830097aee7d40c5e79e5e80e7b0228a67a95bb4969dd8afa0d51d6fff340f82e824547c708b5aa59274009d7d847c53a8019e73c068c6e96a4c3c6c27d0e9f4a8c3a9c52c964eebc00128e9a539f4f569606c92bfc2d4662494a1a6aca239d73399645c86bd66b8985b5bf217b29eeba0507a388aeec85fe94f6b42a1b805ecb90a08b2c8081fe51e76bc1d97f73ae10c72a9b2db694304e04807820c088f91bb97d4585493f3e6cc392a7e56a64a66b8e11b51898b4f956d1b5fe8cf55772fd6f8c0f2a2bb2d9fef05ab2bb90f251ff2e6aa0dfffeac9e045be2ec44ebc8dd4d260748e308205475dcc2cef369e869bfc1e6d7335620c694f524260770838c768346d83af7b467cdc80814d8f55a535dbac35fc278d0d1f6101db95019cee097bb", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1024 bit + boot-mode", "hashcat_id": 29442, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$a3c0fa44ec59bf7a3eed64bf70b8a60623664503eeb972eb51fa25ee921d813f8e45d3e1ab1c0088a62482bb78c6e07308d2308d3d66831505b0cb02fe214fba$c8a51cf9be2ada3c46045afa7df810f2e7b57792150de63b111a9aa78d70e25d832b3d6901aa455b32da240ff68380d66da27f4f7ccc5fadc6b3ff68e27b6d5c48e6512865e3b9fbe2a64a55454cfc333d7850603ecf8e1cf19abaaf8c1581a6fa14c5091ebe70e6338081d72d6a95b542764f3865946edc8e626e166cc2e0f6260032f8decdd98f9a82aa2b065a41e9b42ce8c33d3f935706431d19888bd5b2bd4d34d9bceb8596b15994f247169ee7f8cd34b6955362b60f37a4167c7b63bab8af65e7c592e9ba4535c255b4b3d93b302aa017ea335af20f9d9696f1eb37770ca87b0245d29887cc4611a3a43d11170219c509814eb1fc122a189c08394f22309dd48a996cbfc70cf67f76b6b19e46407a12ef001b2c360501dbd63d1c9f85132204709204992078318920b32aac917bb98d8eeefb60abef47571404d069a6df7881f8e7815c18789f23561d7d33f47e1aa97fb4a60bac0332b0e742a9b0498e5641401567615fd6dbd0fcfff07aebce0d543f2c498486f15f38dcf1dd55d7144d3fc51bf1f491798b183a84f3f49a72944c8054cdab915e19dc376ae3fa681d4afcd7b13f425e96340a696a4f11929b2e769ba207c5bf2c2976a3834c499d", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt RIPEMD160 + XTS 1536 bit + boot-mode", "hashcat_id": 29443, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$1a8c0135fa94567aa866740cb27c5b9763c95be3ac0b7b5c744a36e48c08ae38d6d06ae5db926c64d05295cef134fb4d8eaa96a7b5673a1439f55c8ab829390e$a945babc464e63f3aa33dcfed72c1bcf4051af13da96a2601a060d8c8be0343a7a4f0394b2bdd419b019bd10c3d39f0b6d9afd833816ee9ee5a8afada52db174a85ee029c46b706f8f96e937bb71569b65c2339a3ac8d831733888717fe08029013931ebed1fe932ceb16e52a5d54204e181057584d06991b8e9b16ba557d38f00e7c2be5ea864473e5e35d00a58b7ef8888c78d52ac1933011ca6c447bd16751024186657d1e314540e2c847115b70a51a23e61426ae09e646d715f807eed85e5c14ab2130da0ba86ddc40d3cdce035b454fceb969094d8d1b66e69f34e24d642dc244a81d163c395837d4cd9e2d581f4bb470ad4e5a2037068947f14676796f4adf208621c3db4629b3fec9a24edebfc37f97ea657295a2efbdd18fc44a0cc04f429d4da374db3ba2f3fc7dece70b64ac2c2a94ce5334b20b4251534f9ff3f60b1b252019d2617379bba68a4bc621cbd070881301beb0300bee243d113347d2f0a52fa79fb9fb349eba0056678618c006287e9730a0af32daa17841d88b99e25a9afcedd292a0592565f0ba533f1022ed4d6e51e64b98bab390fee3646133a0e02a5724bb14203fd50006e4be86544b62a9cb64188fbbf4ccd90a32022aa7c", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 512 bit (legacy)", "hashcat_id": 13751, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha256_twofish_13751.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1024 bit (legacy)", "hashcat_id": 13752, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha256_twofish-serpent_13752.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1536 bit (legacy)", "hashcat_id": 13753, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha256_serpent-twofish-aes_13753.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 512 bit + boot-mode (legacy)", "hashcat_id": 13761, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha256_aes_boot_pim500.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1024 bit + boot-mode (legacy)", "hashcat_id": 13762, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha256_serpent-aes_boot.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1536 bit + boot-mode (legacy)", "hashcat_id": 13763, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha256_serpent-twofish-aes_boot.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 512 bit", "hashcat_id": 29451, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$b8a19a544414e540172595aef79e6616f504799b40a407edfb69d40534e93f0bdb3187876f0b7a21739b3a9bb02bd4752eac4d2021e65a2a9413cc389964fad4$6e2cd37f337eb3fe3c75909fe9911609d084fb8c09543f949e738fc2fcfa4825ca5f1e08678e711142553f95b19ba720fa6c8ae5d325be0b36b93c1b2683b0944d2ad4e858c1d83f21a302ef721b9a570233219b9fcf95919fef9ca353af32d7ceb0b3058986c4ed9580b8058325403d45048e43d9e94a1e8fbaa0658f82f81940ea821e1bd526829ee6478a32da4095ab9e7c04dac3b6cc08f99348467a5bf068ba54d0aededdf6005c18ee37e21ee8d980cabe470be49d332661761934f5c07126001c290002587ba4b49982fefaac41b62f7e74ce943bb40a2d78094f734d1bc2aa3dedff43ee2a7b8f3525743c76194637da9ebc2794bac14601e03aa98e9118023a184970b6b8f84f546af88b81e2fde836e286b57cbcbdd7d39334860571a5cc612b77f0c51c741854abeb320bf961aea99b88798199bf826970f2b1b8027499955f68e15328080289d8cf0569057e1ed887f956ce72b14dd13a1f61134e1195d13c68d9c298ae0183107e3a93dd13ee0730f1fabe3935ee70f4c6a1923abb3e0d0c8ecf45260c1444e7e73386acf29d3239d0160e097e6193099e10cc98f61bfda49df6b0635e73a9ccc7bdcc543306b40dd12b91023f61b21418af91", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1024 bit", "hashcat_id": 29452, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$1c3197f32dc5b72b4d60474a7a43afefb0d2e856a8fc4957c3fb1188b62cb0ca002f585c125bb33c5a5e85a665afae9fce15cb127c2fd9b5ee074a48fd95b3a5$8364dfd645968187d546443ba234f5cc40e78c4bdcd1e0c6d0a1208dd892442bc1dfe2a45bc4821e843bb6d9f4adf742c48c432daf0d4a51d42cafdfca281f0fab0caabde8005405840383bbfd8dbf227384891ffa501531549e0b9562c2dd77f0e6552d253acb20cbee9a75d17ec283a46006ee89cd53e3b538e054952ae6db7aac9f2f190590e697a2a8e22d080e88c32f4d27b5afe100647da2a5c80cfcb69e5a3db67cb2fcd86d89c1c53fab1bf3a287bb9002d092e75eb1fe6269a1603545dbf97b9d7fcc9485b6400f7b0abaccc31642cefd83f037e7314c6990c51af24ae894cc1c49a09d18f3ad91b3ef37ae5414fef280ec776d9c0bf84b2eb312c8cb0046bedf6f29b4aab30cdb34333f613000a39bf650341cbf33bdd47ba7bd9be8108a1254390b045d82b208d21aa45de7ca399f8e91845b9ffb47d9e6eeb506965622a2e842ec6897277388cbb6ca2a50117e228e84bebd98f9aba40f38dc3bce3b576cb08596836e50ef276ee3a76b8ce76735fd172e9bae284aa83e2677dac56e4624e66604a90e2e3ae704c64a0f27b51ce9e472891bbc212b4a6055e4482b2e6963507f9ffb477224372289fcfee5764a5f4bc7307a509e7c37c69b4857", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1536 bit", "hashcat_id": 29453, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$f421bdc1087b8319c12d84a680ceab0102e8e41c9ccffe76dbe0215dcfcb7b543f3e1bbedd099e88646823dae5bad8468b72436961ea8e0449a6b92b8bda7b9b$a1fe215e997ec3be2ee5eb3b4d47c41d50998df2f883404fb66270f72b5ce666e7d5ca7847c4a8b2762723da1ad088b0ad75c4fd2ccbbfa4e3adf091b6af4f44f5484ce0c89a5b0db0cbe99b3a9d43d7ff6c4ddbc9636cacfedb26b59340c6eb3e8c587db41fc01f10da2974af96531b2bee5f0b9818c3b86a3cac4ba20e08c49be84af65eb40d51626161f4eef187bf5776a89e791f3f5cbcfaa510df201fb2bf35ff03e81d0572af9abbed3cac82681925a3d1954440a6037df78f7a1e63bea81c852571a21fb550f9fe114b82bf7b94290e362cef233186f17396488c0f259c83c50ac4f8cc27d3a134ddc98f14c2fe0dd6e7d6f5eec63848314dc5984979eeb79df326f80ee0e7f671072117903cb72bbbce4f750fca3f008dadf532241e05913704df6ca03edb9641775c3b6e3e328fd078c6d70298512118312cab8316bb6ddc0b860952c621b2bb4cec1b3c7da9b1cb4c494fec382fe85aefdc56570b54845a14651535d261db519be0e860a4e20c30c86cff6f9de6e16b68d09a0e9593d271df2740950e65f1fb16e3fee034183e540e2a3b0f76156f06946b5d1bfc62fe0cab3daa14603a8d21eb03a4d266e965b010c265c9a0e093084d262a8c03", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 512 bit + boot-mode", "hashcat_id": 29461, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$c8a5f07efc320ecd797ac2c5b911b0f7ee688f859890dd3fa39b4808eb3113219e2bf1517f46a20feba286a3f3e997c80361132262bc0dacb6e9f7088bec9f56$89a0b989ad9d4cc847170422ecd3384c9ee5ccf813fa8fe8ba4d2e6a993c99032337032b83471e9e0aa2531d85481c6d66f3a0d24688e1a17b5e81b3f68736ed05279ac05bcb83bea0c813d807e8c5547f11774c93a0e9de280c1ac5b5f170c0a4b5234f7d0d35a8ec7ec69454607cd35be24428a7be1799beed0ccd6a2af49b920446ebb0cb0bebda4a86c386fcffbb61cb93894ad74819a288c6e5b2e12111011e9f149d165b91f79897f71a96bc17c2b7a5e184147a90e9289d143b597ea98797c560e91b454461d03182f1a6c0bfd2b332829f30f0f18c8253d3194aac7996d4c401a3c1de7b266962a7dd8bc0b071a357121f00bafda835584a119f8fa23306545c413856ad3b2784b8de8ce9377f180baeb0f41590eb603110ff0a82f67349711d6f1b5d707f9c655318af88530962b9127fcf3c73b4d26319a9760cd795cd5ecba203dade9e1c79af14a9e06b9b56ce0af024e6ac582bd3ced1051fb865b55b4b6eaa65789a0c31c04cc4f2fc7b458fda188907f16810f4ce6e12a264cdcb264f1c26533758b92f585a3bbc2cac84731d74e9603d1c43b321ca36b01e5724e0e5558bcba56b57c8d59ded93c12d2664350cf6a048bcfc5d62aa85c590", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1024 bit + boot-mode", "hashcat_id": 29462, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$6bb6eef1af55eb2b2849e1fc9c90c08f705010efa6443581111216b3e145201374bb8e626e4d94a4ce7ecabb11aa57610063fceed38ca9873b0e1194bd12121d$2f6b8a71994c5982049c4517ca7178a55b68cee773e06532b46d68810ede1b18783d7bca98bebf1778d14ecc18e0791190402c6a82bf3ec93e715e65997812363cc6e6bcad4f751fce16f37bbc1d6ac1d0a24c5685e85501a7c46d1cd5b04c55c605357906e5957b99230e2e9834a206e6ff48270ddf3c08c39e5c8390b2a7b7e6064719dbac29ef7513ea78c0edf420eb7ac6db684e890c5fcacfb230996f335f48f4472eaa33f3abe59943a8e3bc27ff4c24fd42015fdacd5e2eaf448049b4aa5ef1c038ca853871fc7f2573aace0874cdd1f3e01140803c1ad036b801cc1a54d619064b9b31e70e7e2601fd7b40f67814320c56721e86ddb3c62ec8cb9680ca7d2504b9decf360e32497ace8171dd9602f01db3be1541f659643e1bdc5139815acdf4debf0186707569c9b57c0fd0031ce03a5091d7937bca8f37015fa35af5f44968176164c0b9194f895a2346dacc51f5e3e7be5682ea7860c4b4302a0f22edecc7ccaebb1c824c5ca4ed4c5e674e742a1d55a7d3e732e40f0107ffad1e3876ec909fac58f1ee21ac99de2c8c29272b1df9dd7f724ff497925898506c4f6e2ae81e285239e5260b119af959338340876b5b8fdd6fede67ae37d3c750265", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA256 + XTS 1536 bit + boot-mode", "hashcat_id": 29463, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$f95b222552195378a228d932f7df38ca459b6d812899be43944ba2e9bf47967ba35da17bf69cc3f424521983989a66fd3c7865af6dd8ac2aeb82e10c92cae66f$62c89b7053d2ba18ee5adcebcf426cc7720f029f7ea5409b3b7182593afbee99f6a3828887d9da6438fafd766589c35c210de60b013d9f816f9a1c8e7e76159347611c3dba00f433aa419dcb9eaf59af6886fccd7d12ae09c2b3d7a8a6102c511e8a34b4c39df8b1938dd5fe037d7087cf2a33b5410df9a6d83d218819b32bc13999c2dd7e96eb740902699ffe5fbaa47270cf1a7e3488198495059e1520ad4ad8beec0c63827286c300555a30febfe29a359d7e364c0b52613d9cff9348152f6871b6210681ab8cfdf24b96c4793c546083197d6e5377a59d7fcab9aa679fddf550ac1ab04249d0d679e8a39ddcca26f9b8b21f7f8b71d64a0ad3d9e3ed9e2e41abd6a9b4ff4d4a7ab29c27882487909fb1118a91de8e2e2d0dea7501a63b7553fd4ff26a5f64964031c9aa3fabbc09e3f58b09ce42bbf3f05afe0f9ea18331c7ba1a887afe307fedc2be93568fe80def12e97d5e129c373814a560573ee6350f59b329352e28137aa31688c499ae1c20b25c91506c520cae56c969790204de1ba46773197fb6a72fd4742712375e89cb5ee41f3ec8b64f3322ba389c947e671b0414e981fe582898af8a5bab09e094f03cb4cab047e7547313a7d1ddba7b70", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA512 + XTS 512 bit (legacy)", "hashcat_id": 13721, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha512_twofish_13721.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA512 + XTS 1024 bit (legacy)", "hashcat_id": 13722, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha512_twofish-serpent_13722.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA512 + XTS 1536 bit (legacy)", "hashcat_id": 13723, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_sha512_serpent-twofish-aes_13723.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA512 + XTS 512 bit", "hashcat_id": 29421, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$2be25b279d8d2694e0ad1e5049902e717f1bdf741bbd678bf307d510741b649d78c54dca46fb2c92723afd9a40769b295e66d445ec232af5bddf91481ee41256$e56b77839e8bf55265077bab405901218ac7933f74073f1208f1de72aace5da4e07d5f83ca580c0216d36c200b54570a1d58e9d8e5c98a597dec23b74a465aeac572a99af70e1a1e20fd29c7c296099e4eed5b715cb470617ea4f20140b62ec4694af67d9158deac3ce846718e10518875ce8cea0286a487a295979e67159d06e871789bf5535b75c809b340f8627e18679e3dab839a1c9823ea14a07d5cc4251b777dddb408da147c70e7cc788a01c27b0ba4f4700d3248f59fa8217874ae4958ea4518522b44f7191ec19459faef7678422adecd58777487ef54a5305ff2caaa545dcb82f7e7a3eb30bd9f7ebab542d0964a367f9c710cf26bbd704e841d591428da3486db31c57f91c6167bf99e31839363cb93bc60d755031f96f2d2c964e1d85b7eaa104985ef801a21d99352c025d7415d5b2f1aa37dc513345d0ff6a1bca92ad7b8c265f322d04f2992895de32636c9b03318cf7154632d547debc1c5e0c8f8730a045efcf3d16ff956cf803716eee22168bc5a5ab72ddb5087436722cb0f59a5b7b03bc557ffb50e8757d1a5639e2bcddd8060de4ee5535fb614b4fc159c6a39040dcbe83889b9c6fac1c9364a7bea930d916ea23fafa0fde07ef609", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA512 + XTS 1024 bit", "hashcat_id": 29422, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$37e6db10454a5d74c1e75eca0bc8a70e67ac032357e4bd6a4315c0174cf9780f92210dfc0a3e977969f2890828d446aecc317dc40fb3162915998cc703e49257$a950a1603342913900052011a7fa85fb0b1fd4489f17237ac1a8bbfd644e871ab95a4019f14b2b938d627646b9958b530dd0739760024ad323d36962b60ba92908e55a876fc392ac2dce6a2410bcdd30a01cba90427f02ccb96e222ab1381266a6f626aa00b0f59e743c1a77433cbb28648f04c91853bdf9b8b29917b2341bf7deb013131ad228ea0c7f9435985318431dae59faff46db3726341b97a956da4ad11766124cd06644c1ba1083b36d3f380f20c272e460b958841fc23be1820ad2e0e6db66eaf4ea171035add0ab543ce8e853e3119ceb9d7f32c0948b81604b81075bcb33efe747fec300a7c68ec383d28d560cccce713c0acf51d74c0db718ba93a9e720b657dda2409adf1ce35aa7e1c0d7ed3df98dd0b6d455a355ce02bda8bea8afc0a8341ac78214efd4372b4430270009ec65badf186e5f0d815dcf597b4703af95e3bfc03313125d2a88b9bb3788b6bbc3c7212713cd584a226b155a2e6872b33730af6fba29aa3dccdb0ec35b5d6e3d981faf39c8dd35fdcff502d14736bc6a47af6e4d7f3518f8ef5e0a4e5d521589a761757f86e2bef471d9867e9b532903c479e4966dcc99189fcdfa3d676f50ccd33fb7cc0aa3e85542ff2648c9", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt SHA512 + XTS 1536 bit", "hashcat_id": 29423, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$d44f26d1742260f88023d825729cc5a64cf8475d887632a2fb4a84af27af138cfadc4bcbb122f6ba68339ae8427d1f72c0c4aeef041291492ae0a7d8677d8da4$3227ae2a26d9a433076b44458b14f52766cf0e4baeb473a789180660d62e42bbea7c042379a5a74e259463e1c18381fa13aee27141264be381de71c12f8f704913f211c45fda0295e963d90fc35272e907858c0522601f6e7a73b43ff222663f149a485fc6c464e5f3b7cc0b6508f30621385365ca8a4e0bff4061f64f5fbdb11f70f19d77e56fa6ff015ad76ecaaccd759d30da05d2a6fbf00ac9673ac3c23efd339313c2a99511e928f976bf9b2664d97685498d5931af2d453edc6fb1129e324eaba64264711fbe21d0d202b3659106e8100634f09c38cd15b1b3acba79d7f31d31fe23c166392e300db09f10550c83187566dc0fdf768b872555851b34e3c15ad7e7438a72e6126c895cf1204987df4b42cb7bc2fe03c5777867d269378c6e496df2a1a3457b907f7143a139d800868ad95e2901723c6ebb991054b4e991c67fe4c17702d9829d9dc1fe8bf4a956460721c858e31dbcbe56850a4ed31558c6ee89ba2cba2ef4bde77fed11848f9f92e0add54964a683c3686dbab4695ebc42554da922a08c6fff32cac936ea447e771aa74a689eb269ffef677294ef297600dfd73bbbb734d2968e38a98b4a8a77ff0eec8246d93b542e3521a3eb636101", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 512 bit (legacy)", "hashcat_id": 13771, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_streebog512_aes_13771.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1024 bit (legacy)", "hashcat_id": 13772, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_streebog512_aes-twofish_13772.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1536 bit (legacy)", "hashcat_id": 13773, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_streebog512_aes-twofish-serpent_13773.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 512 bit + boot-mode (legacy)", "hashcat_id": 13781, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_streebog512_aes_boot_13781.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1024 bit + boot-mode (legacy)", "hashcat_id": 13782, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_streebog512_aes-twofish_boot_13782.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1536 bit + boot-mode (legacy)", "hashcat_id": 13783, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_streebog512_serpent-twofish-aes_boot_13783.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 512 bit", "hashcat_id": 29471, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$444ec71554f0a2989b34bd8a5750ae7b5ed8b1ccdead29120fc030bd5186f312a7fa18ab4f4389d7798e43c073afd1e71dda2052db38dec04a700e8d6b488802$ead0cf95d6e6cecc8eaf6464baf94a64acbbd1a86f826333115b6380bda18cf936150efd6ffc2a344bb78b0b4875781a8c5079772429ef50ddf148f35895496d2e39f32ffaf68a007b070b0beaad316c4b3adf43c0c58ad24430a34abf168ed455b64958ca5465cae0684adadc00f7b9c13fc7671b4520892d23aebff49ea92bc15e804cc650dc3bbd5b8f5122051636f0c576977d4b64ba355bf6e6a8e042fc5165f2a8affa51aa12ff718cee4c543976bf565997b4b57c74e79584e317f4bdb3920f2937c4251af87f432bb8ce78dcb30675246f0303db4aaea913c93be5a26d16dbf8d4d20773aa2a4608d2151491ca6593b51965baeaf9b58f78905df522bf88976fe9436a916c8de38d5a6ca7ca7f436e7982a36335a404298304322ebe194bb34e91e8f7ee7c6541679bb0ce9d80bf4431d1c475b1a785e943e57f8e27a4e665940389b6da2771bd27d943955185379f83ca6a124ec55b2b63d4ef2e2ad6ee27de25f959708f3a64facfe07f06e29459a14f02699751d530f258d0c744a759c188de4f9423f2bd21d3d999ea28df4f3a93a2c47a7e788fe43ccbfbe267277b048002da1ef8c1e7b26690230285675a3a8fdc0f2acf46a4cb24141b3ad1", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1024 bit", "hashcat_id": 29472, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$0f5da0b17c60edcd392058752ec29c389b140b54cd1f94de43dccea703b1fd37936e75a500b7f9d4e94e7f214c4696c051be9697792a856ebf9c0f5a598cf8ba$5621e49c7505eba3b4738acdc860b6ed648f52e5b673ae06bb04616de438a090ab19abea11c30984ead06859de9b7aec8e436c40816f67a56cb53d5f125e58c42225315a4bf494da8128f0df924bcf6ad4b91c9efc5cb0be67cb0cd753c392388d780f57aba39197513a191cc684e9ebee41bc901dd99e9a625141cf98e55e8f74d838baea3bf8f411b85c14eff8cddd1720c2539eef7a38a72c4ed9745a05476b6a16bcda2a5391c94b6f499e3bea64ff412d03d060741e938ed3dc905d8bd6dbb2420e9277251ebe3421be389ea8b02782baeb258b9ec7e0732b3817ee6da58209871aee4e16d57a132c6215782364570238157d8a7fdcd29f54ab2295f68d027dc9f2e0c951afad7500cafe3219e6530699918ac55f4fa1141bc3596155b05bae2fdc8b0a5438edeb5bb0cfac592565b20645be90b406a1fd59846957e7539fd8423bfd4c7ae7d608aacb084ae887baa1a83b14afff8d2063565086c66e293234a8667af39642b90a38c3a5bd4fa8a787c60f73882535c9b34cb7b243465dcc32aff29cee0e741ff059c6acd8ddcbdb3cfafecdcd0f45c84dd871be4fbffd5ac2ab9e01898009adcf7d932c37d6568ad875e4d6ea15db29a1e8ba5a4e86bd", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1536 bit", "hashcat_id": 29473, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$18d2e8314961850f8fc26d2bc6f896db9c4eee301b5fa7295615166552b2422042c6cf6212187ec9c0234908e7934009c23ceed0c4858a7a4deecbc59b50a303$afdc7d583cde1b0c06f0bf56162ef1d6d8df8f194aadcbe395780b3d1d7127faf39910eb10f4805abdd1c3ef7a66972603124a475e2b9224699e60a9e12f4096597f20c5fb0528f590d7bd317e41dc6a2128cf5e58a99803a28c213feb8286350b1d7ab56d43bb52e511f3c860e5002472a4454a549509c8ce0c34f17ece23d5b61aa7c63389c8ca44ed10c2caae03e7ed30b3ef98565926d7e4f3a2a9abf03b278083bed7aaadd78d5bffb7cd45ffae92990c06d9e9f375a77a94226035d1f90e177c46a04dab416dfb7ed7c4ed9ee7e84580bed65c5fee9f4b1545b9a7cf6af533870d393eced609aebe308ec1eee3729da09eb7df7a8d1282b15c4a1b8266a456c06b4ea20c209c549d5d6b58a861f8e15cca3b6cef114accbf470ec76d717f6d7d416d7a32f064ab560c1167f9ef4e93310fbd927b088bffbb0cf5d5c2e271c9cad4c604e489e9983a990b23e1a2f973682fdfe38df385474f73ecdc9bce701d01d627192d3051240f4b96bbdcf2346b275e05aa75add4acb97b286cc00e830fee95d0f86a8b1e315ccb6f3f8642180392b3baac01ed2c97c200489b5e5ca4dcb0a6417e622b6196482a10e640b2b6b08e3f62acac3d45dfc6b88c666205", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 512 bit + boot-mode", "hashcat_id": 29481, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$2bfe4a72e13388a9ce074bbe0711a48d62f123df85b09e0350771edc4a0e4f397038a49b900275c9158145a96b52f95e92f927b3f963c7eadb71a07518d64323$1041c457d2794d0aa505f794153b52b24441271185d386833fbabf0e880c51b544f583d0db2ab6a926ddd3cdd0b68a61d7f5fe3f0ac6aa06ca676a868f373d35073605cf9d521ff55862b5005213a881a7b9025afc3409fa34dc86496620835df072fecd5b501f15e08113835c510d9f0bfd09d2ef1ac0e7bd01f0523d74a54fe984eb497cb960cce5bb154e024dc0c6c61a61e20a45a8f8ef319c63ca9646fbe00930302a5910891a1bc84bd936c926ca535b3b40c9e0ab255363b24a28bb8216d3d32244a725774e6ebbd73d6d3f2a2adcbc28d5341679cbb747efd56db1a09ce80b24640583ffc6f7ca5bd60d59114afcc78601184ba8feadb8d472f86c32bebf70e8158aa56f9db3b3200ef432aa7b370aa4ba408ef11b70d6806f1a21aaa3b629fa06f71dac0ae3e0ce95c7e5b550fc8c46017e151cbbcdf64b3b62b1b846a08925a217227286acfdad35b28407d589bec9578c2a4e9a4320f4a78e1e590fdf53c0a20fe0a1bb6c7d693abcd0e991c449e569477980d4b8972d21e4abc917d897e48ca427c954c3a3e0c8465ef40de51ffc9188047c043224c4a18638f1d91cd88c36623a1d880f18fd0d6ca0b3bbfa7d5d795acfb63576e2c2d83772e8", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1024 bit + boot-mode", "hashcat_id": 29482, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$af7a64c7c81f608527552532cc7049b0d369e2ce20202d7a41ffb94300cbc9c7ce2130247f49ace4c1512fc3d1b4289ca965e8eb065b35faee5b8479a4e43c4a$269f4ee7f6d20f22fe61b2570d46df07b4307f44ba6926f3b44524f0a47be2a0d677d225e2c50ff618b2e5078a19f0613a856bb3145d765cc4c1726aef27b5f03648dcf421b040e7b4fde3193ad9f8a0ae6d91c079610f826e3d556776753d8ca11320632c16a2e49a4eec6e8371681b39be2d7bb826d81dea19eb1dda2e6c71c520a2ad9128b3209a7caf10c196a16ac6f4267ffea8e7be7ddb856976438e0e997773dab75e3dfe0c047f82e4ed0b6e107261b891c4b161fa3c29017428afaaabee5c2dc727fa23b4195265716d92d06e7b828535a77521113077e6f219d7ca721eb5dab66524a530ca3ceba52e3703ec3f435ad1dfee092b090174f4acd1546107db5b403a0ba4fa90c0b4ec19af92a54ebedfd28783dcd83c78499bd33baf3ed10af229ff29634110e2970b6c04232dc95120a365947688abe011f0c8de0e651d9bd112ce0bdf80c4e37c77b962d92f1418272e7484df5217f5f2f3ba1e9b965773ed2727c5d03938516dd789236479b5ff340335c92260b1ad82a313ffa568f912fac799f93b857aaff7b4d76cb525f120a0a9efc376d39c8e27866eff689be01f5adf693ae63ad4b2a77ca96ea985ab7931457f4d8d1afaeb7e423dd752", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Streebog-512 + XTS 1536 bit + boot-mode", "hashcat_id": 29483, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$0c9d7444e9e64a833e857163787b2f6349224bdb4bbf788ce25156c870514226674725be3eebc3f2a2c2ee8adbf8bb3ec1405a333e8e091cec0c5aa77fa9b650$48ca01d954912bf3a3b1c38c00297a33ea0e014156ce08d9526150c085e5e2776a1faeb272c6e9539f466f4f93ffe6497c77d3aed54ffcdf1a3e6171cffac7b2ad96bd9e7cc553058894058def68beea05891b0ce734b6a166b8a5f24b4052fc7014b424bd6c33c9d710fb409cdf1a6c7567c1ba6a3010b03f9bda8aa2ef6733542d198a316da0c83106a6c31043f11ac191169db3db994493168ea996737355ccff84f27f6792b3dc87025d3594edb9e759ba3885980df17bc8c751ce3aba0df67aa7997906348729e81c4893cc654dc6b1da3ff7c588a327f45b8acff976d0593cc607dad48a25468d7c3ebc6dd49aa32fc526dd513852cdec4b36f3683b4998800afa25bb968c242d4c66b9b0c77b20d7bd40ffb403e9e087990d59c94ee7d36e9ebfa35a310bab963c253596e6bc89f67d5307823851c526ac789d0628a3eb81f2cdfd7d7612d8be1dade1b17f30aa2bb5d02eb8534caca0c334a269085939a5041c4ad112d325b1bfe3e9851bfdcad80bbc05ecbddc3f2ac09e2ad7182daf6ca5ccc510a100514f5d2dce1ff5046e0c8e7edf0bdc27f8fcdf4e9b3bce786c24bfa28dacee65ee8c913fc18eee5df61b8a43224f3a9c4e1b5de7b600d9e0", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Whirlpool + XTS 512 bit (legacy)", "hashcat_id": 13731, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_whirlpool_twofish_13731.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Whirlpool + XTS 1024 bit (legacy)", "hashcat_id": 13732, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_whirlpool_twofish-serpent_13732.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Whirlpool + XTS 1536 bit (legacy)", "hashcat_id": 13733, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/vc/hashcat_whirlpool_serpent-twofish-aes_13733.vc", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Whirlpool + XTS 512 bit", "hashcat_id": 29431, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$48f79476aa0aa8327a8a9056e61450f4e2883c9e9669142f2e2f022c2f85303b897d088dea03d64329f6c402a56fed05b3919715929090a25c8ae84c67dbdb36$4ebfa3e9ccc0b391c130a4c3dd6495a1d6eb5d2eab72f8009096f7475ecb736bb3225b6da144e1596d859dad159fae5a739beea88ea074771e9d0b2d7c48ae302606a60d7cff6db54f3e460c548c06a4f47dc1ac203a8c8349fbff6a652219a63f27bc76327543e22be4f8dab8e4f90a4283fbf1552119fe24114ce8869eb20ce87dd72300f7aad3f7b4a26a355f16517725449151cf0373dbd0b281f6ac753485a14a5361cc75d40928e241a6b4684658801774843238048cf8c7f2fd88950abac040e12b0c41fdcaca3702907e951ec11c061a91b3050a4855abe6f3b50b4bd0b17c4be1f5b50b873eadc2d8446cd72c4fcac576bbce3acea769f740c5322ee8c927ffd4dd11c8a9e66f06e58df2e5d4d85c13b44c412bab839c9512b7a0acdd97b37dcccc4b70854eda0f36de12d62dd10cc13bc6154103d083bf6540bc78e5d0aad5d063cc74dad4cbe6e060febda2a9fd79c238f99dcb0766ff4addcfd0c03e619c765f65b1c75d5d22c6536958bcda78077ff44b64c4da741bf50154df310d4e0724238a777b524237b9478277e400ad8146dc3ca1da83e3d2f1c5115a4b7fcdc71dd7d56ba86a2f9b721c9a4137aabb07c3c5fedcf5342c4fae4898c9", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Whirlpool + XTS 1024 bit", "hashcat_id": 29432, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$1b721942019ebe8cedddbed7744a0702c0e053281a467e0ed69bf875c7406407d72eb8f2aea21270e41898c0a2c14382f86e04c15e7bc019d1d9dd813eabee0a$e5173e3cb1d927859d3e6de1006335a5184ae12b4c8dc2db2b1cd785063152a776f4dc5cacc1856a919b880d704b7450f5a0e0c9521bc9b4d67213c36a50e6664a1cbcea33f997b858e654111c7e9fca74f361528e85a28880381ec2600e3c1cd508c3833dd21cc91978185cba53caefd7b3c82d219d49f0b41e536d32e8d3ce194ad7923ca742213e19dcebdbd9687979d5a594654a5c611e8b829c4019e90a3cfb14e5fd7f8ed91e0fc79eed182399f02a3e3e202d4becaa6730e1f05f99ce06ce16dba7777ccddac72e85f2d3be5ecc9c808ac273f10ceb71cad666166abc327c4061a5f47424a5b6d9d093782f34b49924342a2e8cea663446ed4232a9a415ee2dfde988fa827b06d7438fec20ad0689543c3ee4602ce3ec3806fc7d668ef7e34330edd1e077b329a7627fa3ae5c89308258a17ecefbee114c80c2ab06f8271f14de8f2d13d1d6e5a119b71a6bae88ab151f76cdb2442284bc481d0df7e2163c3acfe763d3968195450d275af9034a00184a30cefed163e636626bffe6a35df3472508a49cb2b9b4c4a95d11c5d17e4e0539e9f13112125515778bcd1c2813c62a02673663062ad60583ec6a02c8a572865829e5b8c767b285728bea4907", "is_precomputed": false, "is_popular": false}, {"name": "VeraCrypt Whirlpool + XTS 1536 bit", "hashcat_id": 29433, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$veracrypt$5eb128daef63eff7e6db6aa10a8858f89964f47844acca68df82ebb2e73866fa75e3b7a53f9d2ff1ecdd1f4dc90e9c0fdf51f60d11b1992cd2971b4889edfc89$20bbf346fd7693f675b617cb9e4e9a43e6f445021068fc13453b130f2eb1d753ee83ecc61dabec293e88b62110cf6a8fab670e171f6aba2226550b54893263f5fa086b3cc41dd3db2eae07b585e5162c7a0d9723a426d408d83266c4d6018dc1b8b456d28a224033a30bfe62b1e58c2ddf596e07f7ff31849a6f5cfcc1c977b82d8484c270d44ededb0afdb781295e92968fc8cc69766af0ce1e72f02d6b4e124ba4b1af71519dcaade857bb3f371f93a350da6e65ee46c2ac782f134c75c10fe9d653fccc08c614dc362871911af8b83bdfc479f770dfe4b3c86b5d895842c53852fe4912738f848bf7c3e10b8189d25faceab9ef30b6fa0284edaa471752ac2b65335179b8d605417709f64fded7d94383618a921660d4cdb190bbb3769a8e56d2cd1ee07078ebc3b68ebeb016893f7099018e40cb326e32b29a62806eaf1a3fd382f4f876bf721eadfc019c5545813e81fd7168995f743663b136762b07910a63b6eec5b728a4ad07a689cceecb14c2802f334401a0a4fd2ec49e2da7f3cb24d6181f01ceed93ee73dedc3378133c83c9a71155c86785ff20dd5a64323d2fd4bf076bab3c17a1bb45edf81c30a7bd7dbbb097ece0dca83fff9138d56ae668", "is_precomputed": false, "is_popular": false}, {"name": "BestCrypt v3 Volume Encryption", "hashcat_id": 23900, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$bcve$3$08$234b8182cee7098b$35c12ef76a1e88175c4c222da3558310a0075bc7a06ecf46746d149c02a81fb8a97637d1103d2e13ddd5deaf982889594b18c12d7ca18a54875c5da4a47f90ae615ab94b8e3ed9e3c793d872a1b5ac35cfdb66c221d6d0853e9ff2e0f4435b43", "is_precomputed": false, "is_popular": false}, {"name": "FileVault 2", "hashcat_id": 16700, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$fvde$1$16$84286044060108438487434858307513$20000$f1620ab93192112f0a23eea89b5d4df065661f974b704191", "is_precomputed": false, "is_popular": false}, {"name": "VirtualBox (PBKDF2-HMAC-SHA256 & AES-128-XTS)", "hashcat_id": 27500, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$vbox$0$260000$fcc37189521686699a43e49514b91f159306be108b98895666583cd15c3e206b$8$288c3957db47e7c3dff2f7932121eb3395d21ab76b9cf3de2dc660310a25e7ad$20000$8847cd90f8acef74bae41155392908780eebb1d16452aa09b2f7b6cd7d8a4096$9f4d615b484f95c73944a98f392a3ce04f93403e8bb6257e6b6c854273d3a08a", "is_precomputed": false, "is_popular": false}, {"name": "VirtualBox (PBKDF2-HMAC-SHA256 & AES-256-XTS)", "hashcat_id": 27600, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$vbox$0$160000$54aff69fca91c20b3b15618c6732c4a2f953dd88690cd4cc731569b6b80b5572$16$cfb003087e0c618afa9ad7e44adcd97517f039e0424dedb46db8affbb73cd064019abae19ee5e4f5b05b626e6bc5d7da65c61a5f94d7bcac521c388276e5358b$20000$2e5729055136168eea79cb3f1765450a35ab7540125f2ca2a46924a99fd0524d$b28d1db1cabe99ca989a405c33a27beeb9c0683b8b4b54b0e0d85f712f64d89c", "is_precomputed": false, "is_popular": false}, {"name": "DiskCryptor SHA512 + XTS 512 bit", "hashcat_id": 20011, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/dc/hashcat_serpent.dc", "is_precomputed": false, "is_popular": false}, {"name": "DiskCryptor SHA512 + XTS 1024 bit", "hashcat_id": 20012, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/dc/hashcat_serpent_aes.dc", "is_precomputed": false, "is_popular": false}, {"name": "DiskCryptor SHA512 + XTS 1536 bit", "hashcat_id": 20013, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/dc/hashcat_aes_twofish_serpent.dc", "is_precomputed": false, "is_popular": false}, {"name": "BitLock<PERSON>", "hashcat_id": 22100, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$bitlocker$1$16$6f972989ddc209f1eccf07313a7266a2$1048576$12$3a33a8eaff5e6f81d907b591$60$316b0f6d4cb445fb056f0e3e0633c413526ff4481bbf588917b70a4e8f8075f5ceb45958a800b42cb7ff9b7f5e17c6145bf8561ea86f52d3592059fb", "is_precomputed": false, "is_popular": false}, {"name": "Android FDE (Samsung DEK)", "hashcat_id": 12900, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "38421854118412625768408160477112384218541184126257684081604771129b6258eb22fc8b9d08e04e6450f72b98725d7d4fcad6fb6aec4ac2a79d0c6ff738421854118412625768408160477112", "is_precomputed": false, "is_popular": false}, {"name": "Apple File System (APFS)", "hashcat_id": 18300, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$fvde$2$16$58778104701476542047675521040224$20000$39602e86b7cea4a34f4ff69ff6ed706d68954ee474de1d2a9f6a6f2d24d172001e484c1d4eaa237d", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 512 bit (legacy)", "hashcat_id": 6211, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_ripemd160_twofish.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1024 bit (legacy)", "hashcat_id": 6212, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_ripemd160_twofish-serpent.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1536 bit (legacy)", "hashcat_id": 6213, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_ripemd160_serpent-twofish-aes.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 512 bit + boot-mode (legacy)", "hashcat_id": 6241, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_ripemd160_twofish_boot.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1024 bit + boot-mode (legacy)", "hashcat_id": 6242, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_ripemd160_twofish-serpent_boot.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1536 bit + boot-mode (legacy)", "hashcat_id": 6243, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_ripemd160_serpent-twofish-aes_boot.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 512 bit", "hashcat_id": 29311, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$87914967f14737a67fb460f27b8aeb81de2b41bf2740b3dd78784e02763951daa47c7ca235e75c22ec8d959d6b67f7eedefad61e6a0d038079d3721a8e7215e4$15671e8c7b3dbed6453a114e6db89a52be9a9c1698a9c698f1e37f80d7afaf0efba82b6e5f5df32bd289b95343c6775e2c7f025ef1d8bfae84042a92546e15b635b5fade3aef6ee52a7a5ab018d33ea98bc115dfc62af606187fbab8cbda6e8417402c722ca8c2b07e6ca6a33bf94b2ce2a819a9f8cfaa5af70e3af6e5350d3a306f036f13ff5ba97d5728d5f6413b482c74f528211ae77b6c169215c5487d5a3ce23736b16996b86c71b12d120df28ef322f5143d9a258d0ae7aaa8c193a6dcb5bf18e3c57b5474d24b843f8dd4e83a74109396ddb4f0c50d3657a7eacc8828568e51202de48cd2dfe5acbe3d8840ade1ce44b716d5c0008f2b21b9981353cb12b8af2592a5ab744ae83623349f551acf371c81f86d17a8422654989f078179b2386e2aa8375853a1802cd8bc5d41ce45795f78b80e69fcfa3d14cf9127c3a33fa2dc76ad73960fb7bce15dd489e0b6eca7beed3733887cd5e6f3939a015d4d449185060b2f3bbad46e46d417b8f0830e91edd5ebc17cd5a99316792a36afd83fa1edc55da25518c8e7ff61e201976fa2c5fc9969e05cbee0dce7a0ef876b7340bbe8937c9d9c8248f0e0eae705fe7e1d2da48902f4f3e27d2cf532b7021e18", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1024 bit", "hashcat_id": 29312, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$d6e1644acd373e6fdb8ccaaeab0c400d22eaa0b02e2a6649e065ad50f91e2f81fc5e1600d1cdf3b4ee72a7326a9a28d336ec65adf2d54661e1a609dd9941279f$d64a9c513dfb0192734fc1e1014cdd0a399e89a0860c4077463c18609f5218254edd998adb11a02271723d1aa094550df385dd8e080cb42ed1349f69c0a6bad4b37e6dab1effbe0095471a8d640679422fe1533a21f10cb6d15e5ee8cde78e677acf3d09d008e9fbf57f09c1c57f19e51ff54631e0e2adc2ee2832425c1ec718d96a17df7e55aceffb7b23a1872f32795d4491c739e21b01e19a1b7dfcb22709c9d9302154462664a668ea635664df65804bf680ff07026d6f5b225762a3a270df832d47e7feb6277a228454a3ba9b5bbade23ecaec0eaf31ad1dbac31754c970a212bd44c9278bc6076f096a2eed602e04a70c6f7fa94ef4e75299692e5dcc6f1a7e6032b9b765e9e61faeed3f9efacc0a15b1817e74d48ec11a13d15811c7e2c4d12f36d35a04131d02f14184fc15bc20e79115dc7c980b681a19a225964469787df481b68a8f722f2bd3115dbbcb3c8ac1b07d742f78f30635dea29dfb1db83e89fc85a30b0379fc8aa69a4ea94c99052685d38c9559a1246284cdc32c5110eb8c6741352cd42e09e6389d4765c58aa84d51867cf86fba69d29eac1cd7fac2f36603d2fb2af146c5d4c2bedb4f6c6d0f387f0a8d635e33384df60f8d2415b", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1536 bit", "hashcat_id": 29313, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$3916e924d246e5ceb17b140211fff57b67150b3dee53fa475261d465b0ee3e56ee820e6ba3958d84c61508f028b2a112e9005877784e07deddcf310d01ba8171$0b620533790456d20d17c8fda84f9d93bbfe41509b931a417b82d68ed9b0bc9641b79a5bf8f71bcdbba979dfb7566a5b8ccc221f80722c1ce7ec81be4a8c880b1b057e681c187504eabf4eea32f7b81383defd4616618a99852d1678a6520883c8f3564e6dcf874150a060b9a44748d97f95b223b089ac847e31fb5a2db3656d7b57decff65e2c5c9af5bdece7a1845caa9df805fc1f7e56bf545d854beec27a9640bf1697c195e5f95b82c20d76c5a56ff4283219caa5a618e8caace9d0fcde0df6ee6e043ccbc78fd06a602cc638f7ae4675063b840ee08ffa9e143553bffd20126fa30f95e013aabf103f12c3ceeb284c80dc335fe2e78580d6ddfa80511aba9db7c93838cae0db40b9dbeccbf9d160032d334a9c35156721c746b51131baf6855fdfc1edee3099b8e4abc619e1c60e3ce68615e1eb42bd8d338046f7c854a60defe395e0d7168786a3035c9735cd42433dd0c46dcf8b5cb2c28905df80476561e55d6310b25f74d78b651ccd3484332c59a6ad490e29ea267db5ce4a47c9dcde39f420ba0755ea7e5583a3a562925acaa125d5056795b98135825232aa543a460137cc84235b85dd44d65e01e6eb1ade1b970f3ffe2b9762f5a7f261037e", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 512 bit + boot-mode", "hashcat_id": 29341, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$2b5da9924119fde5270f712ba3c3e4974460416e8465f222149499908c2fca0a4753b581f26625d11c4d3f49bdeb1c95bc3e17629d7e19ffb66175e5feab90a4$fd670194f95d578266f3f54e61b82dc00efc2bb4438e19c3f6d7a92825a7625d88ec6286ab4e1761749edc83dad4340fd167544f09913fd6b03775013ff232fc4dad6f726ef82ad4bd1c5227a7796d7db35a912beeda5b0cdd798bc34d3ac24403c87dc672a983687dd64f920c991840a56105a6311797eed9976014909700366420673f6455242c71151ac75903a353538ec24b4feb967e2b46886395cf3e934e83a6a58ef2c0180273a0c33ba2bd870b1d84afb03d5558dc17bc7fb586404ad9a7e506ed859540110c6ad73f0f1d2be47829bc666e1838ec3f1dc1f610206241ce07fbf2542ecef9348b37aa460815794ca582709697cbf0c90c3dae4cb9dd97b29d3c7d82bd8d0c81d708e74c7007468c6c55a40fd4f803a4f5a75818d7da0d1ef333b8622e7de516fa62a6fa2b8d6d5d23653dfcedffec771456ee204e5c85ee88defbe195462fbe8ce0e2a5a455dab66478b877ec37dfa66f19ab5201c56cd707ba7bee1b10360965d3868c1fdf91dda124b1b0994fee75848083d19369735905bd2864b496c6e35ecf96f6dd4728570a45746bcf8d7d0ec0b9b0b112b28fdc53efcfa7d0558c132cd683a742d62b34304d9f991029c8aedc3d8767da8c", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1024 bit + boot-mode", "hashcat_id": 29342, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$debcc3e74a7b2acb4c7eaa4ac86fd6431da1d9579f4f76f0b31f07b3d36e65099daca9e4ae569114b3cb6e64d707b6206a2ab6b31ab0c17b356da3719d0e2fa4$058f0349763970855d4c83b02a967bb2969f1b6f3e4fdbce37c6df203efbe87bfdb5ffd8fe376e9ad61862a8f659ef0db39e06ed34c4f80aa856df2219ac6a37ebb0244445db7e412b773f4e28846c5e65129cd4f4ce76979c083f08a7c4e2be30469b8363eaf8579baa870cdcb2bdca6b60e64559cb0def242576b80722bf36eb6d94640d2937b49edf9c9af67f0172f27319448425f86831c35ae35e764b9e69fcc47a42ba7a565d682366023291b1b4cbcd1b7ba6fba75c214e5849a9ba26197f7f010f01301dcbffaa7311f2ab32c2810470d3fe873334ca578adbfd04c5a39cbd53b09755e4d868dbf8a44d76cc91031f4710b8a985c70738b443572b4745ed10e6120852870b0fdb258f0a804d679eec85b5290235c9c526165b961f17ff0fe32d9f597c8f2ab9b84f3d22fef71fec67987e687590de6ab11b33f1b06f23c38ead94c3de419061b6568612c27517b0a3395e401a2c6058fc5f41f0e084e8f2157b6486624314b1f341f74cfdec9deaed7abf89ccf97b47441493e5086f1351f42a5c0929f6431753baadcd2fb347b8835d08250743bb45aaf1c6bb30eed98e911a273074b7e8ebad2174b527b1b84e1961967bf358711346482d9db1c7", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt RIPEMD160 + XTS 1536 bit + boot-mode", "hashcat_id": 29343, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$5e6628907291b0b74a4f43a23fb0693acb71c4379c3a3cc0eafbab40036bbdadfede179e04484aca0f5b6ecf7c7e8abe61d6836be6590838b8f9027da93ba77d$076b9a557c958159c5dcddfb70823b7e324bd99b40a8f39410f6afd279df3493b58b9ffce41b65f3afd2fc467f4553a946b85e6ffc74b91c9c38c689d98419339a84d3c6d116274e34482d546407006ee04af03b594998127b2a9716ca4278b1f3050d015af10a9bb11db0465373f3a786c148bb20473377d8e97264b1c4d7ec4179829ce929573b26e5987b59da8591e2dc8e3934830dd0b5ac521c8637e9bb31e4bc084d53bc6a8dc6875e857a4c8c32a577eed3c6cea5beef514160982be2c7d7e2f4d65efa3f4a0e11ac1860ff3160e7cd968e18019abfd0395080a9f8e860c627fc32c63c8b7ef46b203c63cf0f12c05ea65b1f83a5f1fc6ad6cc200a9527151c2b8016a38f1e87be9c960088eaaa98a01d9db8cdacaae26c446a846042a6c0248b666eea7a1be44dc3fc35ce100c3a3eb377e898deb097cfba9246685d7ec8527cdc5e1983c154169178e3d86cd4017606ccc42d25cbdea0aca2b1ac422372cfbb1ad2b7d465449a2c1fbbae35c8e7fdaadd683a7dc991b76aaba08b8706916924407392a2aef458c2e833290dc1ff116f3f49f918e6a133b60728ac7c464e4f3521784cf32866be32877534bb014312c4301d1740781221a5e8758ea4", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt SHA512 + XTS 512 bit (legacy)", "hashcat_id": 6221, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_sha512_twofish.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt SHA512 + XTS 1024 bit (legacy)", "hashcat_id": 6222, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_sha512_twofish-serpent.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt SHA512 + XTS 1536 bit (legacy)", "hashcat_id": 6223, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_sha512_serpent-twofish-aes.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt SHA512 + XTS 512 bit", "hashcat_id": 29321, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$5ebff6b4050aaa3374f9946166a9c4134dd3ec0df1176da2fb103909d20e8b3c9b95cbbd6d1a7ad05411a1443ad6254e059e924d78bab6a0463e71cf7c3109b7$ef4e837bf6d7a548dd8333c451b59d1132098f44c6ff19c6cb921b1de3bd0aa675e0478a05f90204d46a5d6ff598bfa40370ac8795928a6d2e0f1347696e3cfa329738170fe54298981d84f40c63d1a338c5db62679338e849124a28a79a8e505bb89a4673f0457b2737a00b908116310281b5b2eb66c6fda5599196b313d51ef26201335d715c18f6b128454a5601671e619bdcce8e54acb47d498c4161614a05063bff5497a4a3d99bff1fce2a163727af2fe9ae7512461b9dcebf3a4f1031d6235d8ce09b734294d0cedc04eafc6295f212b1b080e7b9745580d0dd18e99cfd95afef982762d5aabeaa2d3a928dcf36322cc06b07fd719c88e0b9a2625a94a77502d4bd40a85ba138cbd0cf9561aa395dc552801f68cce16e5484a672aa5b78665dc531ab1e3e728185929dc443b7f4c8a5cb687c6589bb3f4ddc2a8639d959b839b0813d50e7711b761622c3693a92e540e4f932c6c89bf4e1bff1d69151848c3d01b2f6aba52b58e5b393f6cd58ff0d2e040b1205b042b5a28d5b12cb0cc95fa32f1bcdebd4c82d889a5d87c45dcfd34e80b19bf7be35696e0fa0cbd9338b314de24c1ee7bbc0a3b6824f86af2aa5d127d21444985ff566e921431938f6", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt SHA512 + XTS 1024 bit", "hashcat_id": 29322, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$9f207bec0eded18a1b2e324d4f05d2f33f0bd1aeb43db65d33242fa48ac960fad4c14d04c553e06ad47e7e394d16e0a6544d35fb0b2415bd060bc5f537e42a58$b1681e991e2ec0b5773f6e8e5766e5fcc7335b19dd068d1f20260085ecda8eba366ff1521997c5654630ef09ba421b871a3dc66aa0dd5eba8a3bc7052398a7ad779506d86cbf687e76cd9dc50969e222820d2f905c0550995a9c068725bb6c8b04358c965ab77221fdfd829e57ce54cac6e2fa62db15043d720b72fa8962dd718a0b42c34577af9cb4a5ed04c1ae17b7af470c0d8b77987dc9e2d2593a52458c4acb83b628b1488371de85f78a2e25aeaebc18d20a8c3007d08949e93b80087707afd1fe4e07a0afee4244e5270f768e234b86852aa1556c53ffc0d6f60661369a484d55d063119e71e70af1ec775908466cac7b12bc22e1a9525c2bfa9f83f7901c8e0a1d56387ef65040b750656b0b75791738b5b7e453f24167eae56c057c94e1e4cf1a0d08894225f11b45bc31827cad1dfe62e148549385953aa16a0410dba231aace3a7b9fd9b1c2b930f01193377b59736d8a8959ca5b449655f79a4dbec0da566083f90caa2490b01a10c0a86dd4aaa719bdc1e4233db17217f03509cc20dab7246730e3f964944990690b6dcc84936e1dd487bd154ceefe58a838a0488cc93b854a112ea67f6802d2f409915e648ee5cf5fdc3c12e41acbfab7caa9", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt SHA512 + XTS 1536 bit", "hashcat_id": 29323, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$721a7f40d2b88de8e11f1a203b04ffa97a1f5671623c6783f984cc7c55e04665f95a7f3fd52f402898aaaed68d048cc4c4fabf81c26832b589687dad082f3e4e$0f23c7caba28118f21a4cbb8f32b25914ff4022e7c4c8cdd45411801c7c6bde4033badbdcb82f96c77b42025d13fa71415b3278138100ea58ee4476c81ce66f78e89c59ac22cf454684ea7e8c3900374662f23c9491891b60ed7ce8231a7ac5710ee87b51a3f7bd9566a60dc6e7e701c41f3810d7977314b321e8194349909f2ca458a976851d854eaeb934c8df2b5e063d416d3d7c464e28173a0bbba88ec75cf8fe68f21067739b2473bd804fd710de1e4d3ae9451b374edcfd8e3cd613b23aeae272e0923007482dac26a7532ab09af8aad57cd7f1c451bc260cc912d5830cb0d5332f792519e009ed5450171434e5f0f2ba9e003676933a86d83c766419fac98a7ee232eeb593d1686528fab576d5f393d82f9602bcd65975153df205b6d1bc50dacad2ea5bb184696f978efd2b1c1656bf87e03a28a536c48320c430d407ff6c2fc6e7d4ae7b115e79fd0a88df08eca4743178c7c216f35035596a90b0f0fe9c173c7d0e3d76c33a8fce1f5b9b37674bd12e93fb714c9cbba6768c101b5db8f8fd137144453f00dccc7b66911a0a8d87b198807f30be6619400331c5746d481df7ad47a1f867c07f7b8cd296a0c5e03a121c1a7a60b4f768bea49799d2f", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt Whirlpool + XTS 512 bit (legacy)", "hashcat_id": 6231, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_whirlpool_twofish.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt Whirlpool + XTS 1024 bit (legacy)", "hashcat_id": 6232, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_whirlpool_twofish-serpent.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt Whirlpool + XTS 1536 bit (legacy)", "hashcat_id": 6233, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "https://hashcat.net/misc/example_hashes/hashcat_whirlpool_serpent-twofish-aes.tc", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt Whirlpool + XTS 512 bit", "hashcat_id": 29331, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$cf53d4153414b63285e701e52c2d99e148c6ccc4508132f82cb41862d0a0ac9ea16274285ac261c339c1508eec9fea54c33e382458662913678f2a88a84959a6$78e238973985ec670d50252677430587ee28b72bfa5edfb2f79c40b734ba8a54a3662642a6ab067e75f41154688ad4adb5d6decd891462dd537188195a51e06fa5baf22b69d0f472cfeeae77ab9a90091731863af1d8b5b380da179fa7d5227ef031732b1ae06e0fe34c0b28b7a64eac34e5a08e09d7001394b3afc804ac69bf819cdd2d383fe96a721f7c683628da8e529d84bdaa68d702573d8f7ef26f75d1bd5c91efa88cb33b1e9c006b87981c55ed3b8063ab7068f8e99b128bc56ea3e883efa55d6f340b2681e50405d91f5f6d76cdbeac404944164d329d3ee01311de0bc6547310f126b5a4c0e9fb74825f91faefa60b7ac828819d4544c1872ff5041e61d5cf093553f427358b2181046376d7b876e1bccf0774d5d251b7c922c214bb5c70c715165d028e1dca73e7adeca3396d77f6e597a10dd4c58f37fdbbdc1d04cd8890ba4c5025776a88a349bb925add13193becf1ca10fe32536db0c0b06a1ef799fb692e304b3716ca5a8a80859c4012ca3e06701b46b5a32f4d10e285a0cdaf6c24e0d98139e7f306e52503c9b503aa28f1fbbb236284907068074fcb3e267e3c4aab2bd3b79b24a7a08106bb55850fa2bb8e2f6d9919a6743cb822c164", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt Whirlpool + XTS 1024 bit", "hashcat_id": 29332, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$e9e503972b72dee996b0bfced2df003a54b42399e3586520cf1f69475ba32aff564e40e604a505af95ce15220f558ae815e94ce4953882a8299ee3fffb12e9bd$62bf8e2c41c0a8337ce20d45715440cc83e394200d351c5b04be5b70fa11b8467320a091a1d703c88cc7b26fd114795c04a973b3266ba97f55d4b4e4771bb1b4a6aabc9d57e03f0ae7c8a77dfc3d37078efba45031e7d63bb514726e2f2dc6da8cce167a17e36b32c326a5bcaa2c4b445f6e10e1f899a9adcc2a698769f900b7909f7aec52fc9862d75286ffda67933f9c52e5c681d590ad0329b85f8db0f6bb6daa3b2d55b62c65da37e3e7fcb99954e0abe20c39724e8fb2c7f839ec67d35f151dfd8c4dd4bc8dc4393fab291efa08cc0099277d219a0ba4c6272af3684d8043ed3f502b98e196dc7aa0291627613179199976f28eff08649acf70aa0c0dc5896ed13eb18ea28fdd6c460a9c7cfedeab5ac80a3c195226cfca094a7590fa2ae5ed2133ba09b5466b2049b6291f8dcf345e5718a4c0ef3f9c8d8e07d0e5dddd07452b533fbf243ef063fb6d26759ae725d8ca430f8cf17b86665d23bdff1c9dbdfe601b88e87cb7c89f23abc4a8bb1f0b7375cc29b1d81c950ffe92e16e2080e1d6270bbb3ba753322d2b623caed87213e552c33e699d4010f0f61df2b7f460d7cd82e70a711388f1c0b591d424259d3de8b3628daf62c6c5b71864eb0e7d31", "is_precomputed": false, "is_popular": false}, {"name": "TrueCrypt Whirlpool + XTS 1536 bit", "hashcat_id": 29333, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$truecrypt$de7d6725cc4c910a7e96307df69d41335e64d17b4425ca5bf1730f27820f92df9f20f3e855d8566eb5255927153f987348789666c8e563e366a09e68a8126b11$c25ac817b2706dde5cec3946e64332b21b41b928985c1a637559ead5b4fecac74ff0d625ef6d8be93dea3eaca05394f23ee9e079d3504a77b4c0b22d3cfcafa9c670966bfa3a5f30539250d97267a9e56b5a1437b1fd2ce58f4ab78b52ba61d01c28d7a6b726d92c8819711c70f820690cf2b9bbef75f196ba87fb5f72a29e213096a8be3b6e6d0ff3dc22563dc9e7d95be68ad169c233289fccfdc2f5528c658cb178b4e78d54e96cb452859b01dd756ca0245bdd586fb450e84988071428c80af0a6dc5f16dea8094da3acb51ac5d2a710414256b2423e0333584437ea9a65a07f06bd241103a478d137e9a274a78a19d3ca121f1bc10e4c9e5fc277d23107db1fb447f71ba0f92b20e3ead77cffaca25f772182705a75e500d9aab3996bfda042f4bdfe35a3a477e355c76a711ad0f64848d6144073ce6ec4152c87973fc3e69626523463812061c51f51fc08487e8a4dbae1ca7965c11f222c607688b3384c5c29d4fe91d14d2cc940a6a9d94486d1823261928d88f56fe00e206d7a31734de0217afd38afa3d2cf3499c2dcff13332a369c4b1f39867f6dfc83ec32d19b931b082f07acac7e70bdd537e8432245c11662d89ec3cc97e582de5d2cc6bde7", "is_precomputed": false, "is_popular": false}, {"name": "eCryptfs", "hashcat_id": 12200, "description": "Full-Disk Encryption (FDE)", "category": "Full-Disk Encryption (FDE)", "example": "$ecryptfs$0$1$7c95c46e82f364b3$60bba503f0a42d0c", "is_precomputed": false, "is_popular": false}, {"name": "PDF 1.1 - 1.3 (Acrobat 2 - 4)", "hashcat_id": 10400, "description": "Document", "category": "Document", "example": "$pdf$1*2*40*-1*0*16*51726437280452826511473255744374*32*9b09be05c226214fa1178342673d86f273602b95104f2384b6c9b709b2cbc058*32*0000000000000000000000000000000000000000000000000000000000000000", "is_precomputed": false, "is_popular": false}, {"name": "PDF 1.4 - 1.6 (Acrobat 5 - 8)", "hashcat_id": 10500, "description": "Document", "category": "Document", "example": "$pdf$2*3*128*-1028*1*16*da42ee15d4b3e08fe5b9ecea0e02ad0f*32*c9b59d72c7c670c42eeb4fca1d2ca15000000000000000000000000000000000*32*c4ff3e868dc87604626c2b8c259297a14d58c6309c70b00afdfb1fbba10ee571", "is_precomputed": false, "is_popular": false}, {"name": "PDF 1.4 - 1.6 (<PERSON><PERSON><PERSON><PERSON> 5 - 8) - user and owner pass", "hashcat_id": 25400, "description": "Document", "category": "Document", "example": "$pdf$2*3*128*-3904*1*16*631ed33746e50fba5caf56bcc39e09c6*32*5f9d0e4f0b39835dace0d306c40cd6b700000000000000000000000000000000*32*842103b0a0dc886db9223b94afe2d7cd63389079b61986a4fcf70095ad630c24", "is_precomputed": false, "is_popular": false}, {"name": "PDF 1.7 Level 3 (Acrobat 9)", "hashcat_id": 10600, "description": "Document", "category": "Document", "example": "$pdf$5*5*256*-1028*1*16*20583814402184226866485332754315*127*f95d927a94829db8e2fbfbc9726ebe0a391b22a084ccc2882eb107a74f7884812058381440218422686648533275431500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000*127*00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000*32*0000000000000000000000000000000000000000000000000000000000000000*32*0000000000000000000000000000000000000000000000000000000000000000", "is_precomputed": false, "is_popular": false}, {"name": "PDF 1.7 Level 8 (Acrobat 10 - 11)", "hashcat_id": 10700, "description": "Document", "category": "Document", "example": "$pdf$5*6*256*-1028*1*16*21240790753544575679622633641532*127*2d1ecff66ea354d3d34325a6503da57e03c199c21b13dd842f8d515826054d8d2124079075354457567962263364153200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000*127*00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000*32*0000000000000000000000000000000000000000000000000000000000000000*32*0000000000000000000000000000000000000000000000000000000000000000", "is_precomputed": false, "is_popular": false}, {"name": "MS Office 2007", "hashcat_id": 9400, "description": "Document", "category": "Document", "example": "$office$*2007*20*128*16*411a51284e0d0200b131a8949aaaa5cc*117d532441c63968bee7647d9b7df7d6*df1d601ccf905b375575108f42ef838fb88e1cde", "is_precomputed": false, "is_popular": false}, {"name": "MS Office 2010", "hashcat_id": 9500, "description": "Document", "category": "Document", "example": "$office$*2010*100000*128*16*77233201017277788267221014757262*b2d0ca4854ba19cf95a2647d5eee906c*e30cbbb189575cafb6f142a90c2622fa9e78d293c5b0c001517b3f5b82993557", "is_precomputed": false, "is_popular": false}, {"name": "MS Office 2013", "hashcat_id": 9600, "description": "Document", "category": "Document", "example": "$office$*2013*100000*256*16*7dd611d7eb4c899f74816d1dec817b3b*948dc0b2c2c6c32f14b5995a543ad037*0b7ee0e48e935f937192a59de48a7d561ef2691d5c8a3ba87ec2d04402a94895", "is_precomputed": false, "is_popular": false}, {"name": "MS Office 2016 - SheetProtection", "hashcat_id": 25300, "description": "Document", "category": "Document", "example": "$office$2016$0$100000$876MLoKTq42+/DLp415iZQ==$TNDvpvYyvlSUy97UOLKNhXynhUDDA7H8kLql0ISH5SxcP6hbthdjaTo4Z3/MU0dcR2SAd+AduYb3TB5CLZ8+ow==", "is_precomputed": false, "is_popular": false}, {"name": "Open Document Format (ODF) 1.2 (SHA-256, AES)", "hashcat_id": 18400, "description": "Document", "category": "Document", "example": "$odf$*1*1*100000*32*751854d8b90731ce0579f96bea6f0d4ac2fb2f546b31f1b6af9a5f66952a0bf4*16*2185a966155baa9e2fb597298febecbc*16*c18eaae34bcbbe9119be017fe5f8b52d*0*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", "is_precomputed": false, "is_popular": false}, {"name": "Open Document Format (ODF) 1.1 (SHA-1, <PERSON><PERSON><PERSON>)", "hashcat_id": 18600, "description": "Document", "category": "Document", "example": "$odf$*0*0*1024*16*bff753835f4ea15644b8a2f8e4b5be3d147b9576*8*ee371da34333b69d*16*a902eff54a4d782a26a899a31f97bef4*0*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", "is_precomputed": false, "is_popular": false}, {"name": "Apple Secure Notes", "hashcat_id": 16200, "description": "Document", "category": "Document", "example": "$ASN$*1*20000*80771171105233481004850004085037*d04b17af7f6b184346aad3efefe8bec0987ee73418291a41", "is_precomputed": false, "is_popular": false}, {"name": "Apple iWork", "hashcat_id": 23300, "description": "Document", "category": "Document", "example": "$iwork$2$1$1$4000$b31b7320d1e7a5ee$01f54d6f9e5090eb16fef2b05f8242bc$69561c985268326b7353fb22c3685a378341127557bd2bbea1bd10afb31f2127344707b662a2c29480c32b8b93dea0538327f604e5aa8733be83af25f370f7ac", "is_precomputed": false, "is_popular": false}, {"name": "1Pass<PERSON>, agilekeychain", "hashcat_id": 6600, "description": "Password Manager", "category": "Password Manager", "example": "https://hashcat.net/misc/example_hashes/hashcat.agilekeychain", "is_precomputed": false, "is_popular": false}, {"name": "1Password, cloudkeychain", "hashcat_id": 8200, "description": "Password Manager", "category": "Password Manager", "example": "https://hashcat.net/misc/example_hashes/hashcat.cloudkeychain", "is_precomputed": false, "is_popular": false}, {"name": "Password Safe v2", "hashcat_id": 9000, "description": "Password Manager", "category": "Password Manager", "example": "https://hashcat.net/misc/example_hashes/hashcat.psafe2.dat", "is_precomputed": false, "is_popular": false}, {"name": "Password Safe v3", "hashcat_id": 5200, "description": "Password Manager", "category": "Password Manager", "example": "https://hashcat.net/misc/example_hashes/hashcat.psafe3", "is_precomputed": false, "is_popular": false}, {"name": "LastPass + LastPass sniffed", "hashcat_id": 6800, "description": "Password Manager", "category": "Password Manager", "example": "a2d1f7b7a1862d0d4a52644e72d59df5:500:<EMAIL>", "is_precomputed": false, "is_popular": false}, {"name": "KeePass 1 (AES/Twofish) and KeePass 2 (AES)", "hashcat_id": 13400, "description": "Password Manager", "category": "Password Manager", "example": "$keepass$*2*6000*222*15b6b685bae998f2f608c909dc554e514f2843fbac3c7c16ea3600cc0de30212*c417098b445cfc7a87d56ba17200836f30208d38f75a4169c0280bab3b10ca2a*0d15a81eadccc58b1d3942090cd0ba66*57c4aa5ac7295a97da10f8b2f2d2bfd7a98b0faf75396bc1b55164a1e1dc7e52*2b822bb7e7d060bb42324459cb24df4d3ecd66dc5fc627ac50bf2d7c4255e4f8*1*64*aaf72933951a03351e032b382232bcafbeeabc9bc8e6988b18407bc5b8f0e3cc", "is_precomputed": false, "is_popular": false}, {"name": "KeePass 1 (AES/Twofish) and KeePass 2 (AES) - keyfile only mode", "hashcat_id": 29700, "description": "Password Manager", "category": "Password Manager", "example": "$keepass$*2*60000*0*02078d460c3c837003f22ee2ba42b3ac2a9ad9e913efb61349b3f91aacd0b004*c901781373cb6806df4b4c7b427ba698440f9e9dd68101e6a198e4a95cb10098*c602f182f8b03671c944a5af357eede7*135443633e6d2b6dba314dee0a1e2b5d0c025ca5fcaf692a20d77fb62cc44f63*51b0b2d19d82c88a0d1a646151be0b68c5e3c841a7a21b4abb2e9be14f298ed1", "is_precomputed": false, "is_popular": false}, {"name": "Bitwarden", "hashcat_id": 23400, "description": "Password Manager", "category": "Password Manager", "example": "$bitwarden$2*100000*2******************************+v5rHxYydSRUDlan+4pSoiYQwAgEhdmivlb+exQX+fg=", "is_precomputed": false, "is_popular": false}, {"name": "Ansible Vault", "hashcat_id": 16900, "description": "Password Manager", "category": "Password Manager", "example": "$ansible$0*0*6b761adc6faeb0cc0bf197d3d4a4a7d3f1682e4b169cae8fa6b459b3214ed41e*426d313c5809d4a80a4b9bc7d4823070*d8bad190c7fbc7c3cb1c60a27abfb0ff59d6fb73178681c7454d94a0f56a4360", "is_precomputed": false, "is_popular": false}, {"name": "Mozilla key3.db", "hashcat_id": 26000, "description": "Password Manager", "category": "Password Manager", "example": "$mozilla$*3DES*b735d19e6cadb5136376a98c2369f22819d08c79*2b36961682200a877f7d5550975b614acc9fefe3*f03f3575fd5bdbc9e32232316eab7623", "is_precomputed": false, "is_popular": false}, {"name": "Mozilla key4.db", "hashcat_id": 26100, "description": "Password Manager", "category": "Password Manager", "example": "$mozilla$*AES*5add91733b9b13310ea79a4b38de5c3f797c3bf1*54c17e2a8a066cbdc55f2080c5e9f02ea3954d712cb34b4547f5186548f46512*10000*040e4b5a00f993e63f67a34f6cfc5704*eae9c6c003e6d1b2aa8aa21630838808", "is_precomputed": false, "is_popular": false}, {"name": "Apple Keychain", "hashcat_id": 23100, "description": "Password Manager", "category": "Password Manager", "example": "$keychain$*74cd1efd49e54a8fdc8750288801e09fa26a33b1*66001ad4e0498dc7*5a084b7314971b728cb551ac40b2e50b7b5bd8b8496b902efe7af07538863a45394ead8399ec581681f7416003c49cc7", "is_precomputed": false, "is_popular": false}, {"name": "7-<PERSON><PERSON>", "hashcat_id": 11600, "description": "Archive", "category": "Archive", "example": "$7z$0$19$0$salt$8$f6196259a7326e3f0000000000000000$185065650$112$98$f3bc2a88062c419a25acd40c0c2d75421cf23263f69c51b13f9b1aada41a8a09f9adeae45d67c60b56aad338f20c0dcc5eb811c7a61128ee0746f922cdb9c59096869f341c7a9cb1ac7bb7d771f546b82cf4e6f11a5ecd4b61751e4d8de66dd6e2dfb5b7d1022d2211e2d66ea1703f96", "is_precomputed": false, "is_popular": false}, {"name": "RAR3-hp", "hashcat_id": 12500, "description": "Archive", "category": "Archive", "example": "$RAR3$*0*45109af8ab5f297a*adbf6c5385d7a40373e8f77d7b89d317", "is_precomputed": false, "is_popular": false}, {"name": "RAR3-p (Uncompressed)", "hashcat_id": 23700, "description": "Archive", "category": "Archive", "example": "$RAR3$*1*e54a73729887cb53*49b0a846*16*14*1*34620bcca8176642a210b1051901921e*30", "is_precomputed": false, "is_popular": false}, {"name": "RAR5", "hashcat_id": 13000, "description": "Archive", "category": "Archive", "example": "$rar5$16$74575567518807622265582327032280$15$f8b4064de34ac02ecabfe9abdf93ed6a$8$9843834ed0f7c754", "is_precomputed": false, "is_popular": false}, {"name": "PKZIP (Compressed Multi-File)", "hashcat_id": 17220, "description": "Archive", "category": "Archive", "example": "$pkzip2$3*1*1*0*8*24*a425*8827*d1730095cd829e245df04ebba6c52c0573d49d3bbeab6cb385b7fa8a28dcccd3098bfdd7*1*0*8*24*2a74*882a*51281ac874a60baedc375ca645888d29780e20d4076edd1e7154a99bde982152a736311f*2*0*e3*1c5*eda7a8de*0*29*8*e3*eda7*5096*1455781b59707f5151139e018bdcfeebfc89bc37e372883a7ec0670a5eafc622feb338f9b021b6601a674094898a91beac70e41e675f77702834ca6156111a1bf7361bc9f3715d77dfcdd626634c68354c6f2e5e0a7b1e1ce84a44e632d0f6e36019feeab92fb7eac9dda8df436e287aafece95d042059a1b27d533c5eab62c1c559af220dc432f2eb1a38a70f29e8f3cb5a207704274d1e305d7402180fd47e026522792f5113c52a116d5bb25b67074ffd6f4926b221555234aabddc69775335d592d5c7d22462b75de1259e8342a9ba71cb06223d13c7f51f13be2ad76352c3b8ed*$/pkzip2$", "is_precomputed": false, "is_popular": false}, {"name": "PKZIP (Compressed)", "hashcat_id": 17200, "description": "Archive", "category": "Archive", "example": "$pkzip2$1*1*2*0*e3*1c5*eda7a8de*0*28*8*e3*eda7*5096*a9fc1f4e951c8fb3031a6f903e5f4e3211c8fdc4671547bf77f6f682afbfcc7475d83898985621a7af9bccd1349d1976500a68c48f630b7f22d7a0955524d768e34868880461335417ddd149c65a917c0eb0a4bf7224e24a1e04cf4ace5eef52205f4452e66ded937db9545f843a68b1e84a2e933cc05fb36d3db90e6c5faf1bee2249fdd06a7307849902a8bb24ec7e8a0886a4544ca47979a9dfeefe034bdfc5bd593904cfe9a5309dd199d337d3183f307c2cb39622549a5b9b8b485b7949a4803f63f67ca427a0640ad3793a519b2476c52198488e3e2e04cac202d624fb7d13c2*$/pkzip2$", "is_precomputed": false, "is_popular": false}, {"name": "PKZIP (Mixed Multi-File)", "hashcat_id": 17225, "description": "Archive", "category": "Archive", "example": "$pkzip2$3*1*1*0*0*24*3e2c*3ef8*0619e9d17ff3f994065b99b1fa8aef41c056edf9fa4540919c109742dcb32f797fc90ce0*1*0*8*24*431a*3f26*18e2461c0dbad89bd9cc763067a020c89b5e16195b1ac5fa7fb13bd246d000b6833a2988*2*0*23*17*1e3c1a16*2e4*2f*0*23*1e3c*3f2d*54ea4dbc711026561485bbd191bf300ae24fa0997f3779b688cdad323985f8d3bb8b0c*$/pkzip2$", "is_precomputed": false, "is_popular": false}, {"name": "PKZIP (Mixed Multi-File Checksum-Only)", "hashcat_id": 17230, "description": "Archive", "category": "Archive", "example": "$pkzip2$8*1*1*0*8*24*a425*8827*3bd479d541019c2f32395046b8fbca7e1dca218b9b5414975be49942c3536298e9cc939e*1*0*8*24*2a74*882a*537af57c30fd9fd4b3eefa9ce55b6bff3bbfada237a7c1dace8ebf3bb0de107426211da3*1*0*8*24*2a74*882a*5f406b4858d3489fd4a6a6788798ac9b924b5d0ca8b8e5a6371739c9edcfd28c82f75316*1*0*8*24*2a74*882a*1843aca546b2ea68bd844d1e99d4f74d86417248eb48dd5e956270e42a331c18ea13f5ed*1*0*8*24*2a74*882a*aca3d16543bbfb2e5d2659f63802e0fa5b33e0a1f8ae47334019b4f0b6045d3d8eda3af1*1*0*8*24*2a74*882a*fbe0efc9e10ae1fc9b169bd060470bf3e39f09f8d83bebecd5216de02b81e35fe7e7b2f2*1*0*8*24*2a74*882a*537886dbabffbb7cac77deb01dc84760894524e6966183b4478a4ef56f0c657375a235a1*1*0*8*24*eda7*5096*40eb30ef1ddd9b77b894ed46abf199b480f1e5614fde510855f92ae7b8026a11f80e4d5f*$/pkzip2$", "is_precomputed": false, "is_popular": false}, {"name": "PKZIP (Uncompressed)", "hashcat_id": 17210, "description": "Archive", "category": "Archive", "example": "$pkzip2$1*1*2*0*1d1*1c5*eda7a8de*0*28*0*1d1*eda7*5096*1dea673da43d9fc7e2be1a1f4f664269fceb6cb88723a97408ae1fe07f774d31d1442ea8485081e63f919851ca0b7588d5e3442317fff19fe547a4ef97492ed75417c427eea3c4e146e16c100a2f8b6abd7e5988dc967e5a0e51f641401605d673630ea52ebb04da4b388489901656532c9aa474ca090dbac7cf8a21428d57b42a71da5f3d83fed927361e5d385ca8e480a6d42dea5b4bf497d3a24e79fc7be37c8d1721238cbe9e1ea3ae1eb91fc02aabdf33070d718d5105b70b3d7f3d2c28b3edd822e89a5abc0c8fee117c7fbfbfd4b4c8e130977b75cb0b1da080bfe1c0859e6483c42f459c8069d45a76220e046e6c2a2417392fd87e4aa4a2559eaab3baf78a77a1b94d8c8af16a977b4bb45e3da211838ad044f209428dba82666bf3d54d4eed82c64a9b3444a44746b9e398d0516a2596d84243b4a1d7e87d9843f38e45b6be67fd980107f3ad7b8453d87300e6c51ac9f5e3f6c3b702654440c543b1d808b62f7a313a83b31a6faaeedc2620de7057cd0df80f70346fe2d4dccc318f0b5ed128bcf0643e63d754bb05f53afb2b0fa90b34b538b2ad3648209dff587df4fa18698e4fa6d858ad44aa55d2bba3b08dfdedd3e28b8b7caf394d5d9d95e452c2ab1c836b9d74538c2f0d24b9b577*$/pkzip2$", "is_precomputed": false, "is_popular": false}, {"name": "PKZIP Master Key", "hashcat_id": 20500, "description": "Archive", "category": "Archive", "example": "f1eff5c0368d10311dcfc419", "is_precomputed": false, "is_popular": false}, {"name": "PKZIP Master Key (6 byte optimization)", "hashcat_id": 20510, "description": "Archive", "category": "Archive", "example": "f1eff5c0368d10311dcfc419", "is_precomputed": false, "is_popular": false}, {"name": "SecureZIP AES-128", "hashcat_id": 23001, "description": "Archive", "category": "Archive", "example": "$zip3$*0*1*128*0*b4630625c92b6e7848f6fd86*df2f62611b3d02d2c7e05a48dad57c7d93b0bac1362261ab533807afb69db856676aa6e350320130b5cbf27c55a48c0f75739654ac312f1cf5c37149557fc88a92c7e3dde8d23edd2b839036e88092a708b7e818bf1b6de92f0efb5cce184cceb11db6b3ca0527d0bdf1f1137ee6660d9890928cd80542ac1f439515519147c14d965b5ba107c6227f971e3e115170bf*0*0*0*file.txt", "is_precomputed": false, "is_popular": false}, {"name": "SecureZIP AES-192", "hashcat_id": 23002, "description": "Archive", "category": "Archive", "example": "$zip3$*0*1*192*0*53ff2de8c280778e1e0ab997*603eb37dbab9ea109e2c405e37d8cae1ec89e1e0d0b9ce5bf55d1b571c343b6a3df35fe381c30249cb0738a9b956ba8e52dfc5552894296300446a771032776c811ff8a71d9bb3c4d6c37016c027e41fea2d157d5b0ce17804b1d7c1606b7c1121d37851bd705e001f2cd755bbf305966d129a17c1d48ff8e87cfa41f479090cd456527db7d1d43f9020ad8e73f851a5*0*0*0*file.txt", "is_precomputed": false, "is_popular": false}, {"name": "SecureZIP AES-256", "hashcat_id": 23003, "description": "Archive", "category": "Archive", "example": "$zip3$*0*1*256*0*39bff47df6152a0214d7a967*65ff418ffb3b1198cccdef0327c03750f328d6dd5287e00e4c467f33b92a6ef40a74bb11b5afad61a6c3c9b279d8bd7961e96af7b470c36fc186fd3cfe059107021c9dea0cf206692f727eeca71f18f5b0b6ee1f702b648bba01aa21c7b7f3f0f7d547838aad46868155a04214f22feef7b31d7a15e1abe6dba5e569c62ee640783bb4a54054c2c69e93ece9f1a2af9d*0*0*0*file.txt", "is_precomputed": false, "is_popular": false}, {"name": "WinZip", "hashcat_id": 13600, "description": "Archive", "category": "Archive", "example": "$zip2$*0*3*0*e3222d3b65b5a2785b192d31e39ff9de*1320*e*19648c3e063c82a9ad3ef08ed833*3135c79ecb86cd6f48fc*$/zip2$", "is_precomputed": false, "is_popular": false}, {"name": "Android Backup", "hashcat_id": 18900, "description": "Archive", "category": "Archive", "example": "$ab$5*0*10000*b8900e4885ff9cad8f01ee1957a43bd633fea12491440514ae27aa83f2f5c006ec7e7fa0bce040add619919b4eb60608304b7d571a2ed87fd58c9ad6bc5fcf4c*7d254d93e16be9312fb1ccbfc6265c40cb0c5eab7b605a95a116e2383fb1cf12b688223f96221dcd2bf5410d4ca6f90e0789ee00157fa91658b42665d6b6844c*fc9f6be604d1c59ac32664ec2c5b9b30*00c4972149af3adcc235899e9d20611ea6e8de2212afcb9fcfefde7e35b691c2d0994eb47e4f9a260526ba47f4caea71af9c7fadcd5685d50126276f6acdd59966528b13ccc26036a0eaba2f2451aa64b05766d0edd03c988dcf87e2a9eec52d", "is_precomputed": false, "is_popular": false}, {"name": "Stuffit5", "hashcat_id": 24700, "description": "Archive", "category": "Archive", "example": "66a75cb059", "is_precomputed": false, "is_popular": false}, {"name": "AxCrypt 1", "hashcat_id": 13200, "description": "Archive", "category": "Archive", "example": "$axcrypt$*1*10000*aaf4a5b4a7185551fea2585ed69fe246*45c616e901e48c6cac7ff14e8cd99113393be259c595325e", "is_precomputed": false, "is_popular": false}, {"name": "AxCrypt 1 in-memory SHA1", "hashcat_id": 13300, "description": "Archive", "category": "Archive", "example": "$axcrypt_sha1$b89eaac7e61417341b710b727768294d0e6a277b", "is_precomputed": false, "is_popular": false}, {"name": "AxCrypt 2 AES-128", "hashcat_id": 23500, "description": "Archive", "category": "Archive", "example": "$axcrypt$*2*10000*6d44c6d19076bce9920c5fb76b246c161926ce65abb93ec2003919d78898aadd5bc6e5754201ff25d681ad89fa2861d20ef7c3fd7bde051909dfef8adcb50491*68f78a1b80291a42b2a117d6209d3eb3541a8d47ed6b970b2b8294b2bc78347fc2b494a0599f8cba6d45e88fd8fbc5b4dd7e888f6c9543e679489de132167222e130d5925278693ad8599284705fdf99360b2199ed0005be05867b9b7aa6bb4be76f5f979819eb27cf590a47d81830575b2af09dda756360c844b89c7dcec099cfdd27d2d0c95d24f143405f303e4843*1000*debdeb8ea7b9800b01855de09b105fdb8840efc1f67dc742283d13a5570165f8", "is_precomputed": false, "is_popular": false}, {"name": "AxCrypt 2 AES-256", "hashcat_id": 23600, "description": "Archive", "category": "Archive", "example": "$axcrypt$*2*10000*79bea2d51670484a065241c52613b41a33bf56d2dda9993770e8b0188e3bbf881bea6552a2986c70dc97240b0f91df2eecfa2c7044998041b3fbd58369cfef79*4982f7a860d4e92079bc677c1f89304aa3a2d9ab8c81efaff6c78a12e2873a3a23e6ae6e23a7144248446d8b44e3e82b19a307b2105570a39e1a7bed70b77bbf6b3e85371fe5bb52d1d4c7fcb3d755b308796ab7c4ff270c9217f05477aff5e8e94e5e8af1fba3ce069ce6fc94ae7aeebcb3da270cab672e95c8042a848cefc70bde7201b52cba9a8a0615ac70315792*1000*e2438859e86f7b4076b0ee4044ad5d17c3bb1f5a05fcb1af28ed7326cf71ced2", "is_precomputed": false, "is_popular": false}, {"name": "iTunes backup < 10.0", "hashcat_id": 14700, "description": "Archive", "category": "Archive", "example": "$itunes_backup$*9*b8e3f3a970239b22ac199b622293fe4237b9d16e74bad2c3c3568cd1bd3c471615a6c4f867265642*10000*4542263740587424862267232255853830404566**", "is_precomputed": false, "is_popular": false}, {"name": "WBB3 (Woltlab Burning Board)", "hashcat_id": 8400, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "8084df19a6dc81e2597d051c3d8b400787e2d5a9:6755045315424852185115352765375338838643", "is_precomputed": false, "is_popular": false}, {"name": "PHPS", "hashcat_id": 2612, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "$PHPS$34323438373734$5b07e065b9d78d69603e71201c6cf29f", "is_precomputed": false, "is_popular": false}, {"name": "SMF (Simple Machines Forum) > v1.1", "hashcat_id": 121, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "ecf076ce9d6ed3624a9332112b1cd67b236fdd11:17782686", "is_precomputed": false, "is_popular": false}, {"name": "MediaWiki B type", "hashcat_id": 3711, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "$B$56668501$0ce106caa70af57fd525aeaf80ef2898", "is_precomputed": false, "is_popular": false}, {"name": "Redmine", "hashcat_id": 4521, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "1fb46a8f81d8838f46879aaa29168d08aa6bf22d:3290afd193d90e900e8021f81409d7a9", "is_precomputed": false, "is_popular": false}, {"name": "Umbraco HMAC-SHA1", "hashcat_id": 24800, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "8uigXlGMNI7BzwLCJlDbcKR2FP4=", "is_precomputed": false, "is_popular": false}, {"name": "<PERSON><PERSON><PERSON> < 2.5.18", "hashcat_id": 11, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "19e0e8d91c722e7091ca7a6a6fb0f4fa:54718031842521651757785603028777", "is_precomputed": false, "is_popular": false}, {"name": "OpenCart", "hashcat_id": 13900, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "6e36dcfc6151272c797165fce21e68e7c7737e40:472433673", "is_precomputed": false, "is_popular": false}, {"name": "PrestaShop", "hashcat_id": 11000, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "810e3d12f0f10777a679d9ca1ad7a8d9:M2uZ122bSHJ4Mi54tXGY0lqcv1r28mUluSkyw37ou5oia4i239ujqw0l", "is_precomputed": false, "is_popular": false}, {"name": "Tripcode", "hashcat_id": 16000, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "pfaRCwDe0U", "is_precomputed": false, "is_popular": false}, {"name": "Drupal7", "hashcat_id": 7900, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "$S$C33783772bRXEx1aCsvY.dqgaaSu76XmVlKrW9Qu8IQlvxHlmzLf", "is_precomputed": false, "is_popular": false}, {"name": "PunBB", "hashcat_id": 4522, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "4a2b722cc65ecf0f7797cdaea4bce81f66716eef:653074362104", "is_precomputed": false, "is_popular": false}, {"name": "MyBB 1.2+, IPB2+ (Invision Power Board)", "hashcat_id": 2811, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "8d2129083ef35f4b365d5d87487e1207:47204", "is_precomputed": false, "is_popular": false}, {"name": "vBulletin < v3.8.5", "hashcat_id": 2611, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "16780ba78d2d5f02f3202901c1b6d975:568", "is_precomputed": false, "is_popular": false}, {"name": "bcrypt(md5($pass)) / bcryptmd5", "hashcat_id": 25600, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "$2a$05$/VT2Xs2dMd8GJKfrXhjYP.DkTjOVrY12yDN7/6I8ZV0q/1lEohLru", "is_precomputed": false, "is_popular": false}, {"name": "bcrypt(sha1($pass)) / bcryptsha1", "hashcat_id": 25800, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "$2a$05$Uo385Fa0g86uUXHwZxB90.qMMdRFExaXePGka4WGFv.86I45AEjmO", "is_precomputed": false, "is_popular": false}, {"name": "bcrypt(sha512($pass)) / bcryptsha512", "hashcat_id": 28400, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "$2a$12$KhivLhCuLhSyMBOxLxCyLu78x4z2X/EJdZNfS3Gy36fvRt56P2jbS", "is_precomputed": false, "is_popular": false}, {"name": "osCommerce, xt:Commerce", "hashcat_id": 21, "description": "Forums, CMS, E-Commerce", "category": "Forums, CMS, E-Commerce", "example": "374996a5e8a5e57fd97d893f7df79824:36", "is_precomputed": false, "is_popular": false}, {"name": "TOTP (HMAC-SHA1)", "hashcat_id": 18100, "description": "One-Time Password", "category": "One-Time Password", "example": "597056:3600", "is_precomputed": false, "is_popular": false}, {"name": "STDOUT", "hashcat_id": 2000, "description": "Plaintext", "category": "Plaintext", "example": "n/a", "is_precomputed": false, "is_popular": false}, {"name": "Plaintext", "hashcat_id": 99999, "description": "Plaintext", "category": "Plaintext", "example": "hashcat", "is_precomputed": false, "is_popular": false}, {"name": "Web2py pbkdf2-sha512", "hashcat_id": 21600, "description": "Framework", "category": "Framework", "example": "pbkdf2(1000,20,sha512)$744943$c5f8cdef76e3327c908d8d96d4abdb3d8caba14c", "is_precomputed": false, "is_popular": false}, {"name": "Django (PBKDF2-SHA256)", "hashcat_id": 10000, "description": "Framework", "category": "Framework", "example": "pbkdf2_sha256$20000$H0dPx8NeajVu$GiC4k5kqbbR9qWBlsRgDywNqC2vd9kqfk7zdorEnNas=", "is_precomputed": false, "is_popular": false}, {"name": "Django (SHA-1)", "hashcat_id": 124, "description": "Framework", "category": "Framework", "example": "sha1$fe76b$02d5916550edf7fc8c886f044887f4b1abf9b013", "is_precomputed": false, "is_popular": false}, {"name": "Atlassian (PBKDF2-HMAC-SHA1)", "hashcat_id": 12001, "description": "Framework", "category": "Framework", "example": "{PKCS5S2}NzIyNzM0NzY3NTIwNjI3MdDDis7wPxSbSzfFqDGf7u/L00kSEnupbz36XCL0m7wa", "is_precomputed": false, "is_popular": false}, {"name": "Ruby on Rails Restful-Authentication", "hashcat_id": 19500, "description": "Framework", "category": "Framework", "example": "d7d5ea3e09391da412b653ae6c8d7431ec273ea2:238769868762:8962783556527653675", "is_precomputed": false, "is_popular": false}, {"name": "Ruby on Rails Restful Auth (one round, no sitekey)", "hashcat_id": 27200, "description": "Framework", "category": "Framework", "example": "3999d08db95797891ec77f07223ca81bf43e1be2:5dcc47b04c49d3c8e1b9e4ec367fddeed21b7b85", "is_precomputed": false, "is_popular": false}, {"name": "Python passlib pbkdf2-sha512", "hashcat_id": 20200, "description": "Framework", "category": "Framework", "example": "$pbkdf2-sha512$25000$LyWE0HrP2RsjZCxlDGFMKQ$1vC5Ohk2mCS9b6akqsEfgeb4l74SF8XjH.SljXf3dMLHdlY1GK9ojcCKts6/asR4aPqBmk74nCDddU3tvSCJvw", "is_precomputed": false, "is_popular": false}, {"name": "Python passlib pbkdf2-sha256", "hashcat_id": 20300, "description": "Framework", "category": "Framework", "example": "$pbkdf2-sha256$29000$x9h7j/Ge8x6DMEao1VqrdQ$kra3R1wEnY8mPdDWOpTqOTINaAmZvRMcYd8u5OBQP9A", "is_precomputed": false, "is_popular": false}, {"name": "Python passlib pbkdf2-sha1", "hashcat_id": 20400, "description": "Framework", "category": "Framework", "example": "$pbkdf2$131000$r5WythYixPgfQ2jt3buXcg$8Kdr.QQEOaZIXNOrrru36I/.6Po", "is_precomputed": false, "is_popular": false}, {"name": "JKS Java Key Store Private Keys (SHA1)", "hashcat_id": 15500, "description": "Private Key", "category": "Private Key", "example": "$jksprivk$*5A3AA3C3B7DD7571727E1725FB09953EF3BEDBD9*0867403720562514024857047678064085141322*81*C3*50DDD9F532430367905C9DE31FB1*test", "is_precomputed": false, "is_popular": false}, {"name": "RSA/DSA/EC/OpenSSH Private Keys ($0$)", "hashcat_id": 22911, "description": "Private Key", "category": "Private Key", "example": "$sshng$0$8$7532262427635482$1224$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", "is_precomputed": false, "is_popular": false}, {"name": "RSA/DSA/EC/OpenSSH Private Keys ($6$)", "hashcat_id": 22921, "description": "Private Key", "category": "Private Key", "example": "$sshng$6$8$7620048997557487$1224$13517a1204dc69528c474ef5cbb02d548698771f2a607c04ea54eb92f13dedba0f2185d2884b4db0c95ce6432856108ea2db858be443e0f8004ffcd60857e4ff1e42b17f056998ec5f96806a06e39cc6e6d7ef4ce8ae62b57b2ec0d0236c35cf4bc00dd6fda45e4788dcca0f0e44dddae1dad2d6e7b705d076f2f8fc5837eec4a002d9633bcad1f395ca8e85e78459abe293451567494d440c3f087bb7fe4d6588018f92ca327dda514a99d7b4b32434da0e3b1bf9344afb2fe29f8d8315a385fe8b81fd4c202c7d82cd9f0bb1600e59762ab6ea1b42e4e299f0a59ce510767e1e1138453d362d0a1aa6680e86b5aa0bd5c62165f4fe7c2867f9533578085adc36739d6c9cf7b36899aac39dcabac8b39194433423e8e18ba28496bbe14dd01231eb5b091ae9de0f7f9ea714c22edac394077fb758fe496e1880571ade399ac229457ddd98577f8a01a036ad3bc8b03a9fb02e26b4b76f6cb676eabe82d1606fca0c5fca62cd1d82c3df1ed58ab4acd4611b2827ebde722bc05e471a427225818aa36dabf5bf1203ccb0ebc8dec097e49f7f948bfe7b939e6d0ff1125b863c033768f588964f8b77ca1e2425751f873f80e5d6a0671f7860cf4a46533585094726c3afe5f7203fa4a01650fa9839772c713a033139cfc6a6e6f7dc62e5844d4c57ef4fc3321bc85d597a54bd6fe37e9e696cf3b5ec66f55232e0964dc5cf880d8a41a9891150618bd9c088fd9824af0d86f817f2c79429c3d56cd6eb41eb6120f9accc10a863f23a2bb6c57d4bd6193f2283ae0215e2e87e672a8438e2550c044fa9556bdb4afc40d8c2752ffbc6c95571756a3c230bb2fa95f519f8da238ef0857ecf860247a8b26e28269f9bad564e7d8bfba2eac9760b52449251cb35e183f5b309a09071535154c6f1013b58f305b544f3589c9eb0e9ac4267a84374a3eab49c53aa9bedbf97f8f19ebc212d8db74ee03554a3514140667fa4ce8e06aad3f32d1b00015be0e8979fe66736018589beee06d6f318851dbe8d9689e70202185d71fc5e5a3d2996ddb8ae1d7718c49855c6f8c43301e0915f324f30d0d9c6a8504a91ad5a7179aafb87ede58598394949910874850994abe815817359152ff6a7c8cc6f19524dfc5e50ddfd038a2275bf809e3c8f05ed3e3137ebd62d91cd3578533787c3847e3c5e07e5a891480e5ceabcf6c344e7bec8b640ab9a03e90b846b35d2f46ba150accef32d2597b064810b15fd54fca6d2b146feabcd05c0b51617ae95e36f6817a62c3ff42c5c2f6f1d20a8a1fd334d3b7d3f83bba057b79d9b5508bb0cb706ba00acb0ab797401fdcfac80b5b6e38e51aec0b38f33ff4690425ca28d88a2e876591521230150b4e20a4a82e50061cee9c0705100bfe5fdbd8ef27aec20387cf32455ef305bce2a91ae6da91fc41376b97149e9b41c901b24811df9272ff09718923b8d94e8e459a164a22b0eca47653f3efcbf08188c5da78cd9fb9eda1761094f9d8bc3d479e9f40c7d79ebaaba2a5c632329f20a9962040ff8f512b42c5f32a8460d87b8e93c6f980a1562c436eea1c8994fbf671dda3c4ccd3c142acfcdde2ab61227289ad408213ac8e22d9ef487f36925f5ba3b8e7e913d25c4a8592c861d13f03b615bc2760aabc61d68db80d35296a3312fdf4b56c0fbee5ab3fea1cf9caf3960a564046939e8002d2dd909db446d85aeae9dd42a33fe28684f722172e6", "is_precomputed": false, "is_popular": false}, {"name": "RSA/DSA/EC/OpenSSH Private Keys ($1, $3$)", "hashcat_id": 22931, "description": "Private Key", "category": "Private Key", "example": "$sshng$1$16$14987802644369864387956120434709$1232$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", "is_precomputed": false, "is_popular": false}, {"name": "RSA/DSA/EC/OpenSSH Private Keys ($4$)", "hashcat_id": 22941, "description": "Private Key", "category": "Private Key", "example": "$sshng$4$16$01684556100059289727957814500256$1232$b04d45fdfdf02a9ca91cbc9c53f9e59956822c72c718929aca9251cffd9ac48e48c490b7b6b6043df3a70cf5fbcc2f358b0e8b70d39155c93032b0fd79ec68f6cb8b7de8422ec95cb027a9eaacc453b0b99b5d3f8d6771d6b95b0242a1d8664de8598e8d6b6d6ee360fda5ae0106061a79e88ef2eef98a000b638f8fdc367155ec2d1120b366f74f0933efe5d174e7107db29dc8fb592b22b9837114415d78036c116b2d31b2080c7159442f2d1a61900f5ae4913548c8e7fc716dd4f812bc7e57b2dd5d3f56c6ae0e91c3bc2897d9341cb282d86b915d43cf20ad16fbd2056104529576142354a430281f5e458923ef8014ff9950351798bfcbbcb66cb98bb2cccea48c134b0e05e978d4308c82617869b207f0ed7b227893f2cdde2d6b6a98246de8a2494d5e018a84724780fbe8d1fa91c922908d18ccffbbbbc81e6578fe8bb5c8596a8cf689f3f12b810dee95887e12439e487313229a37913e3cd12bddba3bac94fab03aad8607f6034fa87f7a7a2ac74d0c0a6e6bc905f569221861e1e388cf379cda799d7b56eac58440d17fe97fa68a537d34317376c00dfa9a99e04725a0d2fcf27ee50463e725813c96fe2eed16de59e8a6944d903e11f7923d57ae6d4a1f8085ce19f4d180f13027806f3965fdf875ea092f103f28a5f42f356254958fa7eb0bca2389a6ad4e305640cc64501e6b16330b063037b1cf6fe64131f308e50d9d1dc687ffa487681941084ff21cb54c1b5903b7a78d9913595fa0124f1dde49b1bee2ea83837efe34e2cd6051a4a7a1437eaa84ad332ffd9946b952ed634948789d9541820a0f9c6f44ab6d3cad645743c76c54e79bfdc4fb8e43a0fd7d871baea98e78131bc530b6d736fa1ec5ac70438609497ab2ff8d516146b4b1b3488791cb84dccc0096b570e2ffb3a93cccefec0af7ce616a64466d2d4196941ba9e051dc00ed05e963a7b4a286973ee0b5df4fd92dfb0b229b10730d454832d945c6a596862212d109ce78ac14ffb5d775548b2f3e2ae4be059a24465cc10b7c810f8cc3db7cb327619cc104ebea575ac097d20701dc623f7aa893b785cc20851f3972390e00ab3355655f7d5bea323832c17d8e078e917843ef7fcaca349366092b6743bf7511d5fceb2d992fbd18574be532365be41ad80a114704a64a7aefdf98c907aa10e4d5c547dd8d21647ea9d5c975fe1b24525d94c3eb03e071742fd5f09f22da669b649fac9f87d8cf16c475d006421f69a9b2d5c4037ccc9bf9f0aa0e7df8ac5fcb0d88a528833f9640799026d2fe8694fa1a0307c5f24002172464b290bedd85667800edbff2f1de7119e5b65730a24922e42d53ef28b0a59817a298426dc72e29a85e59e3d777b19eb934bcd620a903aff72927cdbe7253f77694ab0ef970378b4347f6166ca2a40b23cc31970f0cbefd08d2d72bf2c3961d67c73a5a24f75a65e540dc5735520b0d81250af8980ddca3e22a9b25773afd27c76e564ff437d4208df14d802f1d0848390f45924cdd6ced3c9ffb726bb358b334ea0e0481acdd103f2db05f508f62588621d0b8fa274a69eba0d418d85086d9139391f7e28dc54fe9bab801f1fea854f27ad2e5907ae6f9a4b4527d16a8af3c8cbe2c6d82209dc6c7da060da58294eb00380598330c4c19d45581d09e04c0153a8559700b3a8ceab9b8124f84d397356cd9e38e3916afc1f63a3e1dfbc7df8dd0a7d0704e38a0ea523dfc2b9defd5", "is_precomputed": false, "is_popular": false}, {"name": "RSA/DSA/EC/OpenSSH Private Keys ($5$)", "hashcat_id": 22951, "description": "Private Key", "category": "Private Key", "example": "$sshng$5$16$52935050547964524511665675049973$1232$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", "is_precomputed": false, "is_popular": false}, {"name": "XMPP SCRAM PBKDF2-SHA1", "hashcat_id": 23200, "description": "Instant Messaging Service", "category": "Instant Messaging Service", "example": "$xmpp-scram$0$4096$32$bbc1467455fd9886f6c5d15200601735e159e807d53a1c80853b570321aaeceb$8301c6e0245e4a986ed64a9b1803afb1854d9712", "is_precomputed": false, "is_popular": false}, {"name": "Teamspeak 3 (channel hash)", "hashcat_id": 28300, "description": "Instant Messaging Service", "category": "Instant Messaging Service", "example": "$teamspeak$3$E0aV0IQ29EDyxRfkFoQflUGJ6zo=$mRgDUkNpd0IwUEcTJQBmE0NHYwdDEhFzQ0VgMRcFJUIRYnaHBwNXRZJwk2ZUaURzdXkVYiUROERmI0hYYGFYCDiIJCeIU3N5EhRVcZFnSIRCJlkUFkY4YFMDcheYeTl4RYZEdpKGJYhxAIQJEYGYEA==", "is_precomputed": false, "is_popular": false}, {"name": "Telegram Desktop < v2.1.14 (PBKDF2-HMAC-SHA1)", "hashcat_id": 22600, "description": "Instant Messaging Service", "category": "Instant Messaging Service", "example": "$telegram$1*4000*913a7e42143b4eed0fb532dacfa04e3a0eae036ae66dd02de76323046c575531*cde5f7a3bda3812b4a3cd4df1269c6be18ca7536981522c251cab531c274776804634cdca5313dc8beb9895f903a40d874cd50dbb82e5e4d8f264820f3f2e2111a5831e1a2f16b1a75b2264c4b4485dfe0f789071130160af205f9f96aef378ee05602de2562f8c3b136a75ea01f54f4598af93f9e7f98eb66a5fd3dabaa864708fe0e84b59b77686974060f1533e3acc5367bc493915b5614603cf5601cfa0a6b8eae4c4bd24948176dd7ff470bc0863f35fdfce31a667c70e37743f662bc9c5ec86baff3ebb6bf7de96bcdfaca18baf9617a979424f792ef6e65e346ea2cbc1d53377f47c3fc681d7eda8169e6e20cd6a22dd94bf24933b8ffc4878216fa9edc7c72a073446a14b63e12b223f840217a7eac51b6afcc15bfa12afd3e85d3bd", "is_precomputed": false, "is_popular": false}, {"name": "Telegram Mobile App Passcode (SHA256)", "hashcat_id": 22301, "description": "Instant Messaging Service", "category": "Instant Messaging Service", "example": "$telegram$0*518c001aeb3b4ae96c6173be4cebe60a85f67b1e087b045935849e2f815b5e41*25184098058621950709328221838128", "is_precomputed": false, "is_popular": false}, {"name": "Skype", "hashcat_id": 23, "description": "Instant Messaging Service", "category": "Instant Messaging Service", "example": "3af0389f093b181ae26452015f4ae728:user", "is_precomputed": false, "is_popular": false}, {"name": "Terra Station Wallet (AES256-CBC(PBKDF2($pass)))", "hashcat_id": 29600, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "", "is_precomputed": false, "is_popular": false}, {"name": "MetaMask Wallet", "hashcat_id": 26600, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$metamask$h0c2mQBGgnhlJ4EWMhdAAZhHlFeZNVlAEwOHQHaEBhY=$q9de9oljOBLWBQRtk9Ugog==$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", "is_precomputed": false, "is_popular": false}, {"name": "BitShares v0.x - sha512(sha512_bin(pass))", "hashcat_id": 21000, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "caec04bdf7c17f763a9ec7439f7c9abda112f1bfc9b1bb684fef9b6142636979b9896cfc236896d821a69a961a143dd19c96d59777258201f1bbe5ecc2a2ecf5", "is_precomputed": false, "is_popular": false}, {"name": "Bitcoin WIF private key (P2PKH), compressed", "hashcat_id": 28501, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "**********************************", "is_precomputed": false, "is_popular": false}, {"name": "Bitcoin WIF private key (P2PKH), uncompressed", "hashcat_id": 28502, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "**********************************", "is_precomputed": false, "is_popular": false}, {"name": "Bitcoin WIF private key (P2WPKH, Bech32), compressed", "hashcat_id": 28503, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "******************************************", "is_precomputed": false, "is_popular": false}, {"name": "Bitcoin WIF private key (P2WPKH, Bech32), uncompressed", "hashcat_id": 28504, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "******************************************", "is_precomputed": false, "is_popular": false}, {"name": "Bitcoin WIF private key (P2SH(P2WPKH)), compressed", "hashcat_id": 28505, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "**********************************", "is_precomputed": false, "is_popular": false}, {"name": "Bitcoin WIF private key (P2SH(P2WPKH)), uncompressed", "hashcat_id": 28506, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "**********************************", "is_precomputed": false, "is_popular": false}, {"name": "Bitcoin/Litecoin wallet.dat", "hashcat_id": 11300, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$bitcoin$96$d011a1b6a8d675b7a36d0cd2efaca32a9f8dc1d57d6d01a58399ea04e703e8bbb44899039326f7a00f171a7bbc854a54$16$1563277210780230$158555$96$628835426818227243334570448571536352510740823233055715845322741625407685873076027233865346542174$66$625882875480513751851333441623702852811440775888122046360561760525", "is_precomputed": false, "is_popular": true}, {"name": "Electrum Wallet (Salt-Type 1-3)", "hashcat_id": 16600, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$electrum$1*44358283104603165383613672586868*c43a6632d9f59364f74c395a03d8c2ea", "is_precomputed": false, "is_popular": false}, {"name": "Electrum Wallet (Salt-Type 4)", "hashcat_id": 21700, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$electrum$4*03eae309d8bda5dcbddaae8145469193152763894b7260a6c4ba181b3ac2ed5653*8c594086a64dc87a9c1f8a69f646e31e8d3182c3c722def4427aa20684776ac26092c6f60bf2762e27adfa93fe1e952dcb8d6362224b9a371953aa3a2edb596ce5eb4c0879c4353f2cc515ec6c9e7a6defa26c5df346d18a62e9d40fcc606bc8c34322bf2212f77770a683788db0baf4cb43595c2a27fe5ff8bdcb1fd915bcd725149d8ee8f14c71635fecb04da5dde97584f4581ceb7d907dceed80ae5daa8352dda20b25fd6001e99a96b7cf839a36cd3f5656304e6998c18e03dd2fb720cb41386c52910c9cb83272c3d50f3a6ff362ab8389b0c21c75133c971df0a75b331796371b060b32fe1673f4a041d7ae08bbdeffb45d706eaf65f99573c07972701c97766b4d7a8a03bba0f885eb3845dfd9152286e1de1f93e25ce04c54712509166dda80a84c2d34652f68e6c01e662f8b1cc7c15103a4502c29332a4fdbdda470c875809e15aab3f2fcb061ee96992ad7e8ab9da88203e35f47d6e88b07a13b0e70ef76de3be20dc06facbddc1e47206b16b44573f57396265116b4d243e77d1c98bc2b28aa3ec0f8d959764a54ecdd03d8360ff2823577fe2183e618aac15b30c1d20986841e3d83c0bfabcedb7c27ddc436eb7113db927e0beae7522b04566631a090b214660152a4f4a90e19356e66ee7309a0671b2e7bfde82667538d193fc7e397442052c6c611b6bf0a04f629a1dc7fa9eb44bfad1bfc6a0bce9f0564c3b483737e447720b7fd038c9a961a25e9594b76bf8c8071c83fcacd689c7469f698ee4aee4d4f626a73e21ce4967e705e4d83e1145b4260330367d8341c84723a1b02567ffbab26aac3afd1079887b4391f05d09780fc65f8b4f68cd51391c06593919d7eafd0775f83045b8f5c2e59cef902ff500654ea29b7623c7594ab2cc0e05ffe3f10abc46c9c5dac824673c307dcbff5bc5f3774141ff99f6a34ec4dd8a58d154a1c72636a2422b8fafdef399dec350d2b91947448582d52291f2261d264d29399ae3c92dc61769a49224af9e7c98d74190f93eb49a44db7587c1a2afb5e1a4bec5cdeb8ad2aac9728d5ae95600c52e9f063c11cdb32b7c1d8435ce76fcf1fa562bd38f14bf6c303c70fb373d951b8a691ab793f12c0f3336d6191378bccaed32923bba81868148f029e3d5712a2fb9f610997549710716db37f7400690c8dfbed12ff0a683d8e4d0079b380e2fd856eeafb8c6eedfac8fb54dacd6bd8a96e9f8d23ea87252c1a7c2b53efc6e6aa1f0cc30fbaaf68ee7d46666afc15856669cd9baebf9397ff9f322cce5285e68a985f3b6aadce5e8f14e9f9dd16764bc4e9f62168aa265d8634ab706ed40b0809023f141c36717bd6ccef9ec6aa6bfd2d00bda9375c2fee9ebba49590a166*1b0997cf64bb2c2ff88cb87bcacd9729d404bd46db18117c20d94e67c946fedc", "is_precomputed": false, "is_popular": false}, {"name": "Electrum Wallet (Salt-Type 5)", "hashcat_id": 21800, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$electrum$5*02170fee7c35f1ef3b229edc90fbd0793b688a0d6f41137a97aab2343d315cce16*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*33a7ee59d6d17ed1ee99dc0a71771227e6f3734b17ba36eb589bdced56244135", "is_precomputed": false, "is_popular": false}, {"name": "<PERSON>chain, My Wallet", "hashcat_id": 12700, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$blockchain$288$5420055827231730710301348670802335e45a6f5f631113cb1148a6e96ce645ac69881625a115fd35256636d0908217182f89bdd53256a764e3552d3bfe68624f4f89bb6de60687ff1ebb3cbf4e253ee3bea0fe9d12d6e8325ddc48cc924666dc017024101b7dfb96f1f45cfcf642c45c83228fe656b2f88897ced2984860bf322c6a89616f6ea5800aadc4b293ddd46940b3171a40e0cca86f66f0d4a487aa3a1beb82569740d3bc90bc1cb6b4a11bc6f0e058432cc193cb6f41e60959d03a84e90f38e54ba106fb7e2bfe58ce39e0397231f7c53a4ed4fd8d2e886de75d2475cc8fdc30bf07843ed6e3513e218e0bb75c04649f053a115267098251fd0079272ec023162505725cc681d8be12507c2d3e1c9520674c68428df1739944b8ac", "is_precomputed": false, "is_popular": true}, {"name": "Blockchain, My Wallet, V2", "hashcat_id": 15200, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$blockchain$v2$5000$288$06063152445005516247820607861028813ccf6dcc5793dc0c7a82dcd604c5c3e8d91bea9531e628c2027c56328380c87356f86ae88968f179c366da9f0f11b09492cea4f4d591493a06b2ba9647faee437c2f2c0caaec9ec795026af51bfa68fc713eaac522431da8045cc6199695556fc2918ceaaabbe096f48876f81ddbbc20bec9209c6c7bc06f24097a0e9a656047ea0f90a2a2f28adfb349a9cd13852a452741e2a607dae0733851a19a670513bcf8f2070f30b115f8bcb56be2625e15139f2a357cf49d72b1c81c18b24c7485ad8af1e1a8db0dc04d906935d7475e1d3757aba32428fdc135fee63f40b16a5ea701766026066fb9fb17166a53aa2b1b5c10b65bfe685dce6962442ece2b526890bcecdeadffbac95c3e3ad32ba57c9e", "is_precomputed": false, "is_popular": true}, {"name": "<PERSON><PERSON><PERSON>, <PERSON> Wallet, Second Password (SHA256)", "hashcat_id": 18800, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "YnM6WYERjJfhxwepT7zV6odWoEUz1X4esYQb4bQ3KZ7bbZAyOTc1MDM3OTc1NjMyODA0ECcAAD3vFoc=", "is_precomputed": false, "is_popular": false}, {"name": "Stargazer Stellar Wallet XLM", "hashcat_id": 25500, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$stellar$YAlIJziURRcBEWUwRSRDWA==$EutMmmcV5Hbf3p1I$rfSAF349RvGKG4R4Z2VCrH9WjNEKjbJa9hpOja9Yn8MwXruuFEMtw47HPn9CYj+JJ5Rb4Z87Wejj1c4fqpbMZHFOnqtQsVAr", "is_precomputed": false, "is_popular": false}, {"name": "Ethereum Pre-Sale Wallet, PBKDF2-HMAC-SHA256", "hashcat_id": 16300, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$ethereum$w*e94a8e49deac2d62206bf9bfb7d2aaea7eb06c1a378cfc1ac056cc599a569793c0ecc40e6a0c242dee2812f06b644d70f43331b1fa2ce4bd6cbb9f62dd25b443235bdb4c1ffb222084c9ded8c719624b338f17e0fd827b34d79801298ac75f74ed97ae16f72fccecf862d09a03498b1b8bd1d984fc43dd507ede5d4b6223a582352386407266b66c671077eefc1e07b5f42508bf926ab5616658c984968d8eec25c9d5197a4a30eed54c161595c3b4d558b17ab8a75ccca72b3d949919d197158ea5cfbc43ac7dd73cf77807dc2c8fe4ef1e942ccd11ec24fe8a410d48ef4b8a35c93ecf1a21c51a51a08f3225fbdcc338b1e7fdafd7d94b82a81d88c2e9a429acc3f8a5974eafb7af8c912597eb6fdcd80578bd12efddd99de47b44e7c8f6c38f2af3116b08796172eda89422e9ea9b99c7f98a7e331aeb4bb1b06f611e95082b629332c31dbcfd878aed77d300c9ed5c74af9cd6f5a8c4a261dd124317fb790a04481d93aec160af4ad8ec84c04d943a869f65f07f5ccf8295dc1c876f30408eac77f62192cbb25842470b4a5bdb4c8096f56da7e9ed05c21f61b94c54ef1c2e9e417cce627521a40a99e357dd9b7a7149041d589cbacbe0302db57ddc983b9a6d79ce3f2e9ae8ad45fa40b934ed6b36379b780549ae7553dbb1cab238138c05743d0103335325bd90e27d8ae1ea219eb8905503c5ad54fa12d22e9a7d296eee07c8a7b5041b8d56b8af290274d01eb0e4ad174eb26b23b5e9fb46ff7f88398e6266052292acb36554ccb9c2c03139fe72d3f5d30bd5d10bd79d7cb48d2ab24187d8efc3750d5a24980fb12122591455d14e75421a2074599f1cc9fdfc8f498c92ad8b904d3c4307f80c46921d8128*f3abede76ac15228f1b161dd9660bb9094e81b1b*d201ccd492c284484c7824c4d37b1593", "is_precomputed": false, "is_popular": false}, {"name": "Ethereum Wallet, PBKDF2-HMAC-SHA256", "hashcat_id": 15600, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$ethereum$p*262144*3238383137313130353438343737383736323437353437383831373034343735*06eae7ee0a4b9e8abc02c9990e3730827396e8531558ed15bb733faf12a44ce1*e6d5891d4f199d31ec434fe25d9ecc2530716bc3b36d5bdbc1fab7685dda3946", "is_precomputed": false, "is_popular": true}, {"name": "Ethereum Wallet, SCRYPT", "hashcat_id": 15700, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$ethereum$s*262144*1*8*3436383737333838313035343736303637353530323430373235343034363130*8b58d9d15f579faba1cd13dd372faeb51718e7f70735de96f0bcb2ef4fb90278*8de566b919e6825a65746e266226316c1add8d8c3d15f54640902437bcffc8c3", "is_precomputed": false, "is_popular": true}, {"name": "MultiBit Classic .key (MD5)", "hashcat_id": 22500, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$multibit$1*e5912fe5c84af3d5*5f0391c219e8ef62c06505b1f6232858f5bcaa739c2b471d45dd0bd8345334de", "is_precomputed": false, "is_popular": false}, {"name": "MultiBit Classic .wallet (scrypt)", "hashcat_id": 27700, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$multibit$3*16384*8*1*7523cb5482e81b81*91780fd49b81a782ab840157a69ba7996d81270eaf456c850f314fc1787d9b0b", "is_precomputed": false, "is_popular": false}, {"name": "MultiBit HD (scrypt)", "hashcat_id": 22700, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "$multibit$2*2e311aa2cc5ec99f7073cacc8a2d1938*e3ad782e7f92d66a3cdfaec43a46be29*5d1cabd4f4a50ba125f88c47027fff9b", "is_precomputed": false, "is_popular": false}, {"name": "Exodus Desktop Wallet (scrypt)", "hashcat_id": 28200, "description": "Cryptoc<PERSON><PERSON><PERSON>et", "category": "Cryptoc<PERSON><PERSON><PERSON>et", "example": "EXODUS:16384:8:1:IYkXZgFETRmFp4wQXyP8XMe3LtuOw8wMdLcBVQ+9YWE=:lq0W9ekN5sC0O7Xw:UD4a6mUUhkTbQtGWitXHZUg0pQ4RHI6W/KUyYE95m3k=:ZuNQckXOtr4r21x+DT1zpQ==", "is_precomputed": false, "is_popular": false}]