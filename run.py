from flask import Flask
from flask_cors import CORS
from config import Config
from routes.wordlist_routes import wordlist_bp
from routes.hydra_routes import hydra_bp
from routes.hash_routes import hash_bp
from routes.rules_routes import rules_bp
from routes.static_routes import static_bp
from routes.auth_routes import auth_bp
import os
from flasgger import Swagger

app = Flask(__name__)
CORS(app)

# Swagger configuration
swagger_template = {
    "swagger": "2.0",
    "info": {
        "title": "CrackMaster API Documentation",
        "description": "API Documentation for CrackMaster project",
        "version": "1.0.0"
    },
    "securityDefinitions": {
        "Bearer": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header",
            "description": "JWT Authorization header using Bearer scheme. Example: \"Authorization: Bearer {token}\""
        }
    },
    "tags": [
        {"name": "Wordlists", "description": "Wordlist management operations"},
        {"name": "Hydra", "description": "Hydra attack operations"},
        {"name": "Hash", "description": "Hash identification and cracking operations"},
        {"name": "Static", "description": "Static page operations"}
    ]
}

swagger_config = {
    "headers": [],
    "specs": [{
        "endpoint": 'apispec',
        "route": '/apispec.json',
        "rule_filter": lambda rule: True,
        "model_filter": lambda tag: True,
    }],
    "static_url_path": "/flasgger_static",
    "swagger_ui": True,
    "specs_route": "/docs/"
}

swagger = Swagger(app, template=swagger_template, config=swagger_config)

# Ensure upload folder exists
if not os.path.exists(Config.UPLOAD_FOLDER):
    os.makedirs(Config.UPLOAD_FOLDER)

# Register blueprints
app.register_blueprint(wordlist_bp, url_prefix='/api')
app.register_blueprint(hydra_bp, url_prefix='/api') # online attack
app.register_blueprint(hash_bp, url_prefix='/api') # Offline Attack
app.register_blueprint(rules_bp, url_prefix='/api') # Hashcat rules management
app.register_blueprint(auth_bp, url_prefix='/api') # Hashcat rules management
app.register_blueprint(static_bp)

if __name__ == '__main__':
    app.run(debug=True, host="0.0.0.0", port=5050)
