
version: '3.8'

services:
  mongodb:
    build:
      context: .
      dockerfile: docker/mongodb/Dockerfile
    container_name: crackmaster_mongodb
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo_dump:/tmp/mongo_dump:ro
    environment:
      - MONGO_INITDB_DATABASE=crackmaster_db
    networks:
      - crackmaster_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  # app:
  #   build:
  #     context: .
  #     dockerfile: docker/app/Dockerfile
  #   container_name: crackmaster_app
  #   ports:
  #     - "5050:5050"
  #   volumes:
  #     - .:/app
  #   environment:
  #     - MONGODB_URI=mongodb://mongodb:27017/crackmaster_db
  #     - FLASK_ENV=development
  #     - FLASK_DEBUG=1
  #   depends_on:
  #     mongodb:
  #       condition: service_healthy
  #   networks:
  #     - crackmaster_network
  #   restart: unless-stopped
  #   command: sh -c "sleep 10 && python3 run.py"

volumes:
  mongodb_data:
    name: crackmaster_mongodb_data

networks:
  crackmaster_network:
    name: crackmaster_network
    driver: bridge
