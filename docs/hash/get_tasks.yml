Get crack tasks
---
tags:
  - Hash
summary: "Get list of hash cracking tasks"
description: "Retrieves a list of hash cracking tasks with their status and results"
parameters:
  - name: limit
    in: query
    type: integer
    required: false
    default: 50
    description: "Maximum number of tasks to return"
  - name: status
    in: query
    type: string
    required: false
    description: "Filter tasks by status"
responses:
  200:
    description: Tasks retrieved successfully
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: true
        tasks:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: "60f1a5b3e4b0a1234567890"
              hash:
                type: string
                example: "5f4dcc3b5aa765d61d8327deb882cf99"
              hashType:
                type: string
                example: "md5"
              method:
                type: string
                example: "quick_search"
              status:
                type: string
                enum: [pending, running, completed, failed]
              password:
                type: string
                example: "password123"
              time:
                type: string
                example: "2.5s"
              created_at:
                type: string
                format: date-time
              updated_at:
                type: string
                format: date-time
  500:
    description: Server error
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string