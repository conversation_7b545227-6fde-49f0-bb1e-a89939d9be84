Identify hash type
---
tags:
  - Hash
parameters:
  - name: body
    in: body
    required: true
    schema:
      type: object
      properties:
        hash:
          type: string
          example: "5f4dcc3b5aa765d61d8327deb882cf99"
responses:
  200:
    description: Hash type identified successfully
    schema:
      type: object
      properties:
        possible_algorithms:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              hashcat_id:
                type: integer
              probability:
                type: number
  400:
    description: Invalid request
    schema:
      type: object
      properties:
        error:
          type: string
  500:
    description: Server error
    schema:
      type: object
      properties:
        error:
          type: string