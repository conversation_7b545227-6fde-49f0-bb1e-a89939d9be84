Lookup hash in database
---
tags:
  - Hash
summary: "Quick lookup of a hash in precomputed database"
description: "Performs a quick search for a hash value in precomputed databases. Only works with supported hash types that have precomputed tables."
parameters:
  - name: body
    in: body
    required: true
    schema:
      type: object
      required:
        - hash
        - algorithm
      properties:
        hash:
          type: string
          example: "5f4dcc3b5aa765d61d8327deb882cf99"
          description: "The hash to look up"
        algorithm:
          type: string
          example: "MD5"
          description: "The ID of the hash algorithm"
responses:
  200:
    description: Search completed successfully
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: true
        hash:
          type: string
          example: "5f4dcc3b5aa765d61d8327deb882cf99"
        algorithm:
          type: string
          example: "md5"
        result:
          type: string
          example: "password123"
        task_id:
          type: string
          example: "60f1a5b3e4b0a1234567890"
  400:
    description: Invalid request
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: "Missing required parameters (hash, algorithm)"
  503:
    description: Service unavailable
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: "Connection error"
        details:
          type: string
  500:
    description: Server error
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
        details:
          type: string