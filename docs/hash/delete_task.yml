Delete crack task
---
tags:
  - Hash
summary: "Delete a password cracking task"
description: "Remove a specific password cracking task by its ID"
parameters:
  - name: task_id
    in: path
    type: string
    required: true
    description: "ID of the task to delete"
    example: "507f1f77bcf86cd799439011"
responses:
  200:
    description: Task deleted successfully
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Task 507f1f77bcf86cd799439011 deleted successfully"
  404:
    description: Task not found
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: "Task not found"
  500:
    description: Server error
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: "Internal server error"
