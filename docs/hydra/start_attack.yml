Start a new Hydra attack
---
tags:
  - Hydra
parameters:
  - name: body
    in: body
    required: true
    schema:
      type: object
      properties:
        target:
          type: string
          example: "192.168.1.100"
        protocol:
          type: string
          example: "ssh"
        port:
          type: string
          example: "22"
        sources:
          type: object
          properties:
            combo:
              type: object
              properties:
                type:
                  type: string
                  enum: [wordlist, manual]
                info:
                  type: object
                  properties:
                    wordlistId:
                      type: string
                    list:
                      type: array
                      items:
                        type: string
            username:
              type: object
              properties:
                type:
                  type: string
                  enum: [wordlist, manual]
                info:
                  type: object
                  properties:
                    wordlistId:
                      type: string
                    list:
                      type: array
                      items:
                        type: string
            password:
              type: object
              properties:
                type:
                  type: string
                  enum: [wordlist, manual]
                info:
                  type: object
                  properties:
                    wordlistId:
                      type: string
                    list:
                      type: array
                      items:
                        type: string
responses:
  200:
    description: Attack started successfully
    schema:
      type: object
      properties:
        taskId:
          type: string
          example: "60f1a5b3e4b0a1234567890"
  400:
    description: Invalid request
    schema:
      type: object
      properties:
        error:
          type: string
  500:
    description: Server error
    schema:
      type: object
      properties:
        error:
          type: string