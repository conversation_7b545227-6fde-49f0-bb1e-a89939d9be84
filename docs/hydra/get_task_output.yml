Get task output
---
tags:
  - Hydra
summary: "Get detailed output of a Hydra task"
description: "Retrieves the complete output and results of a specific Hydra task"
parameters:
  - name: task_id
    in: path
    type: string
    required: true
    description: "ID of the task to get output for"
responses:
  200:
    description: Task output retrieved successfully
    schema:
      type: object
      properties:
        task_id:
          type: string
          example: "60f1a5b3e4b0a1234567890"
        command:
          type: string
          description: "The Hydra command that was executed"
        outputs:
          type: array
          items:
            type: string
          description: "Raw output lines from Hydra"
        username_source:
          type: object
          description: "Username source configuration used"
        password_source:
          type: object
          description: "Password source configuration used"
        combo_source:
          type: object
          description: "Combo source configuration used"
        results:
          type: array
          items:
            type: object
            properties:
              username:
                type: string
              password:
                type: string
              timestamp:
                type: string
                format: date-time
          description: "Successfully found credentials"
        status:
          type: string
          enum: [running, completed, stopped, failed]
  404:
    description: Task not found
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Task not found"
  500:
    description: Server error
    schema:
      type: object
      properties:
        error:
          type: string