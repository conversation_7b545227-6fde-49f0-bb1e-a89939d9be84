Get task status
---
tags:
  - Hydra
summary: "Get status of a Hydra task"
description: "Retrieves the current status and details of a specific Hydra task"
parameters:
  - name: task_id
    in: path
    type: string
    required: true
    description: "ID of the task to query"
responses:
  200:
    description: Task status retrieved successfully
    schema:
      type: object
      properties:
        _id:
          type: string
          example: "60f1a5b3e4b0a1234567890"
        target:
          type: string
          example: "*************"
        protocol:
          type: string
          example: "ssh"
        port:
          type: string
          example: "22"
        status:
          type: string
          enum: [running, completed, stopped, failed]
        command:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
  404:
    description: Task not found
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Task not found"