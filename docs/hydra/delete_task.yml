Delete Hydra task
---
tags:
  - Hydra
summary: "Delete a Hydra task"
description: "Removes a Hydra task and its associated data from the system"
parameters:
  - name: task_id
    in: path
    type: string
    required: true
    description: "ID of the task to delete"
responses:
  200:
    description: Task deleted successfully
    schema:
      type: object
      properties:
        message:
          type: string
          example: "Task 60f1a5b3e4b0a1234567890 deleted successfully"
  400:
    description: Invalid task ID format
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Invalid task ID format"
  404:
    description: Task not found
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Task not found"
  500:
    description: Server error
    schema:
      type: object
      properties:
        error:
          type: string