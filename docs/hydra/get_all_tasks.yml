Get all Hydra tasks
---
tags:
  - Hydra
summary: "Get list of all Hydra tasks"
description: "Retrieves a paginated list of all Hydra tasks with optional status filtering"
parameters:
  - name: status
    in: query
    type: string
    required: false
    description: "Filter tasks by status (comma-separated for multiple)"
  - name: page
    in: query
    type: integer
    required: false
    default: 1
    description: "Page number for pagination"
  - name: per_page
    in: query
    type: integer
    required: false
    default: 10
    description: "Number of items per page"
responses:
  200:
    description: Tasks retrieved successfully
    schema:
      type: object
      properties:
        tasks:
          type: array
          items:
            type: object
            properties:
              _id:
                type: string
              target:
                type: string
              protocol:
                type: string
              port:
                type: string
              status:
                type: string
              created_at:
                type: string
                format: date-time
              updated_at:
                type: string
                format: date-time
        total:
          type: integer
          example: 45
        page:
          type: integer
          example: 1
        per_page:
          type: integer
          example: 10
        total_pages:
          type: integer
          example: 5