Stop Hydra attack
---
tags:
  - Hydra
summary: "Stop a running Hydra attack"
description: "Terminates a running Hydra attack task"
parameters:
  - name: task_id
    in: path
    type: string
    required: true
    description: "ID of the task to stop"
responses:
  200:
    description: Attack stopped successfully
    schema:
      type: object
      properties:
        status:
          type: string
          example: "stopped"
        message:
          type: string
          example: "Hydra attack stopped successfully"
  404:
    description: Task not found
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Task not found or already completed"