Get wordlists by type
---
tags:
  - Wordlists
summary: "Get wordlists by type"
description: "Retrieve a list of wordlists filtered by type"
parameters:
  - name: type
    in: path
    type: string
    required: true
    enum: [usernames, passwords, combos]
    description: "Type of wordlists to retrieve"
responses:
  200:
    description: List of wordlists
    schema:
      type: array
      items:
        type: object
        properties:
          id:
            type: string
          name:
            type: string
          count:
            type: integer
          description:
            type: string
          type:
            type: string
          filepath:
            type: string
  400:
    description: Invalid type specified
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Invalid wordlist type"
  500:
    description: Server error
    schema:
      type: object
      properties:
        error:
          type: string