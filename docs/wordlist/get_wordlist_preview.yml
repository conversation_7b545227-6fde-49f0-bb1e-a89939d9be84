Get wordlist preview
---
tags:
  - Wordlists
summary: "Get preview of wordlist content"
description: "Retrieve the first 1000 lines of a wordlist file"
parameters:
  - name: type
    in: path
    type: string
    required: true
    enum: [usernames, passwords]
    description: "Type of wordlist"
  - name: wordlist_id
    in: path
    type: string
    required: true
    description: "ID of the wordlist"
responses:
  200:
    description: Wordlist preview
    schema:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        count:
          type: integer
        description:
          type: string
        type:
          type: string
        entries:
          type: array
          items:
            type: string
          description: "First 1000 lines of the wordlist"
  400:
    description: Invalid type or type mismatch
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Invalid wordlist type"
  404:
    description: Wordlist not found
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Wordlist not found"
  500:
    description: Server error
    schema:
      type: object
      properties:
        error:
          type: string