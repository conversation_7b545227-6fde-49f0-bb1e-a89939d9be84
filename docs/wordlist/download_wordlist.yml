Download wordlist
---
tags:
  - Wordlists
summary: "Download a wordlist file"
description: "Download the file content of a specific wordlist"
parameters:
  - name: wordlist_id
    in: path
    type: string
    required: true
    description: "ID of the wordlist to download"
produces:
  - application/octet-stream
responses:
  200:
    description: File download
    schema:
      type: file
  404:
    description: Wordlist or file not found
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Wordlist not found"