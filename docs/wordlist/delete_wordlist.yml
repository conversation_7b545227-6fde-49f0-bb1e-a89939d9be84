Delete wordlist
---
tags:
  - Wordlists
summary: "Delete a wordlist"
description: "Delete a wordlist and its associated file"
parameters:
  - name: wordlist_id
    in: path
    type: string
    required: true
    description: "ID of the wordlist to delete"
responses:
  200:
    description: Wordlist deleted successfully
    schema:
      type: object
      properties:
        message:
          type: string
          example: "Wordlist deleted successfully"
  404:
    description: Wordlist not found
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Wordlist not found"