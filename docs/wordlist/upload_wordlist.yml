Upload a new wordlist
---
tags:
  - Wordlists
summary: "Upload a new wordlist file"
description: "Upload a new wordlist file with type specification (password, username, or combo)"
consumes:
  - multipart/form-data
parameters:
  - name: wordlist
    in: formData
    type: file
    required: true
    description: "The wordlist file to upload"
  - name: type_wordlist
    in: formData
    type: string
    enum: [password, username, combo]
    required: true
    description: "Type of wordlist"
  - name: note
    in: formData
    type: string
    required: false
    description: "Additional notes about the wordlist"
responses:
  200:
    description: Wordlist uploaded successfully
    schema:
      type: object
      properties:
        message:
          type: string
          example: "Wordlist uploaded successfully"
        wordlist_id:
          type: string
          example: "60f1a5b3e4b0a1234567890"
  400:
    description: Invalid request
    schema:
      type: object
      properties:
        error:
          type: string
          example: "No file uploaded"
  409:
    description: Duplicate wordlist
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Duplicate wordlist"
        existing_wordlist:
          type: object
          properties:
            id:
              type: string
            filename:
              type: string
            type:
              type: string
  500:
    description: Server error
    schema:
      type: object
      properties:
        error:
          type: string
