<!doctype html>
<!--
* Tabler - Premium and Open Source dashboard template with responsive and high quality UI.
* @version 1.0.0
* @link https://tabler.io
* Copyright 2018-2025 The Tabler Authors
* Copyright 2018-2025 codecalm.net Paweł Kuna
* Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
-->
<html lang="en">

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
	<meta http-equiv="X-UA-Compatible" content="ie=edge" />
	<title>Crack Master</title>
	<!-- CSS files -->
	<link href="./static/dist/libs/dropzone/dist/dropzone.css?**********" rel="stylesheet" />
	<link href="./static/dist/css/tabler.min.css?**********" rel="stylesheet" />
	<link href="./static/dist/css/tabler-flags.min.css?**********" rel="stylesheet" />
	<link href="./static/dist/css/tabler-socials.min.css?**********" rel="stylesheet" />
	<link href="./static/dist/css/tabler-payments.min.css?**********" rel="stylesheet" />
	<link href="./static/dist/css/tabler-vendors.min.css?**********" rel="stylesheet" />
	<link href="./static/dist/css/tabler-marketing.min.css?**********" rel="stylesheet" />
	<link href="./static/dist/css/demo.min.css?**********" rel="stylesheet" />
	<style>
		@import url('https://rsms.me/inter/inter.css');
	</style>
	<!-- Thêm vào phần head, trước các script khác -->
	<script src="/static/js/notifications.js"></script>
</head>

<body>
	<script src="./static/dist/js/demo-theme.min.js?**********"></script>
	<div class="page">
		<!-- Navbar -->
		<header class="navbar navbar-expand-md d-print-none">
			<div class="container-xl">
				<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu"
					aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
					<span class="navbar-toggler-icon"></span>
				</button>
				<div class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
					<a href=".">
						<div class="d-flex align-items-center">
							<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="me-2">
								<path d="M12 3a12 12 0 0 0 8.5 3A12 12 0 0 1 12 21 12 12 0 0 1 3.5 6 12 12 0 0 0 12 3"/>
								<circle cx="12" cy="12" r="3"/>
								<path d="m19 19-3-3"/>
							</svg>
							<span class="h2 mb-0">CrackMaster</span>
						</div>
					</a>
				</div>
				<div class="navbar-nav flex-row order-md-last">
					<div class="d-none d-md-flex">
						<a href="?theme=dark" class="nav-link px-0 hide-theme-dark" title="Enable dark mode"
							data-bs-toggle="tooltip" data-bs-placement="bottom">
							<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
								fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
								stroke-linejoin="round" class="icon icon-1">
								<path
									d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
							</svg>
						</a>
						<a href="?theme=light" class="nav-link px-0 hide-theme-light" title="Enable light mode"
							data-bs-toggle="tooltip" data-bs-placement="bottom">
							<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
								fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
								stroke-linejoin="round" class="icon icon-1">
								<path d="M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" />
								<path
									d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7" />
							</svg>
						</a>
						<div class="nav-item dropdown d-none d-md-flex me-3">
							<a href="#" class="nav-link px-0" data-bs-toggle="dropdown" tabindex="-1"
								aria-label="Show notifications">
								<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
									fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
									stroke-linejoin="round" class="icon icon-1">
									<path
										d="M10 5a2 2 0 1 1 4 0a7 7 0 0 1 4 6v3a4 4 0 0 0 2 3h-16a4 4 0 0 0 2 -3v-3a7 7 0 0 1 4 -6" />
									<path d="M9 17v1a3 3 0 0 0 6 0v-1" />
								</svg>
								<span class="badge bg-red"></span>
							</a>
							<div class="dropdown-menu dropdown-menu-arrow dropdown-menu-end dropdown-menu-card">
								<div class="card">
									<div class="card-header">
										<h3 class="card-title">Last updates</h3>
									</div>
									<div class="list-group list-group-flush list-group-hoverable">
										<div class="list-group-item">
											<div class="row align-items-center">
												<div class="col-auto"><span
														class="status-dot status-dot-animated bg-red d-block"></span>
												</div>
												<div class="col text-truncate">
													<a href="#" class="text-body d-block">Example 1</a>
													<div class="d-block text-secondary text-truncate mt-n1">
														Change deprecated html tags to text decoration classes (#29604)
													</div>
												</div>
												<div class="col-auto">
													<a href="#" class="list-group-item-actions">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
															viewBox="0 0 24 24" fill="none" stroke="currentColor"
															stroke-width="2" stroke-linecap="round"
															stroke-linejoin="round" class="icon text-muted icon-2">
															<path
																d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z" />
														</svg>
													</a>
												</div>
											</div>
										</div>
										<div class="list-group-item">
											<div class="row align-items-center">
												<div class="col-auto"><span class="status-dot d-block"></span></div>
												<div class="col text-truncate">
													<a href="#" class="text-body d-block">Example 2</a>
													<div class="d-block text-secondary text-truncate mt-n1">
														justify-content:between ⇒ justify-content:space-between (#29734)
													</div>
												</div>
												<div class="col-auto">
													<a href="#" class="list-group-item-actions show">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
															viewBox="0 0 24 24" fill="none" stroke="currentColor"
															stroke-width="2" stroke-linecap="round"
															stroke-linejoin="round" class="icon text-yellow icon-2">
															<path
																d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z" />
														</svg>
													</a>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="nav-item">
						<a href="{{ url_for('static.hash_types_page') }}" class="nav-link">
							<span class="nav-link-icon d-md-none d-lg-inline-block">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
									<path stroke="none" d="M0 0h24v24H0z" fill="none"/>
									<path d="M4 4m0 1a1 1 0 0 1 1 -1h14a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-14a1 1 0 0 1 -1 -1z" />
									<path d="M4 8h16" />
									<path d="M8 4v16" />
								</svg>
							</span>
							<span class="nav-link-title">
								Hash Types
							</span>
						</a>
					</div>
					<div class="nav-item dropdown">
						<a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown"
							aria-label="Open user menu">
							<span class="avatar avatar-sm"
								style="background-image: url(./static/static/avatars/000m.jpg)"></span>
							<div class="d-none d-xl-block ps-2">
								<div>Admin</div>
								<div class="mt-1 small text-secondary">Administrator</div>
							</div>
						</a>
						<div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
							<a href="./profile.html" class="dropdown-item">Profile</a>
							<a href="./settings.html" class="dropdown-item">Settings</a>
							<div class="dropdown-divider"></div>
							<a href="./sign-in.html" class="dropdown-item">Logout</a>
						</div>
					</div>
				</div>
			</div>
		</header>
		<header class="navbar-expand-md">
			<div class="collapse navbar-collapse" id="navbar-menu">
				<div class="navbar">
					<div class="container-xl">
						<div class="row flex-fill align-items-center">
							<div class="col">
								<ul class="navbar-nav">
									<li class="nav-item">
										<a class="nav-link" href="#">
											<span class="nav-link-icon d-md-none d-lg-inline-block">
												<!-- Download SVG icon from http://tabler.io/icons/icon/home -->
												<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													viewBox="0 0 24 24" fill="none" stroke="currentColor"
													stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
													class="icon icon-1">
													<path d="M5 12l-2 0l9 -9l9 9l-2 0" />
													<path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" />
													<path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" />
												</svg>
											</span>
											<span class="nav-link-title">
												Home
											</span>
										</a>
									</li>
									<li class="nav-item">
										<a class="nav-link" href="/hydra.html">
											<span class="nav-link-icon d-md-none d-lg-inline-block">
												<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" 
													viewBox="0 0 24 24" fill="none" stroke="currentColor"
													stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
													class="icon icon-1">
													<path d="M12 6v12"/>
													<path d="M6 12h12"/>
												</svg>
											</span>
											<span class="nav-link-title">
												Online Attack
											</span>
										</a>
									</li>
									<li class="nav-item dropdown">
										<a class="nav-link dropdown-toggle" href="/hashcat.html" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false">
											<span class="nav-link-icon d-md-none d-lg-inline-block">
												<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													viewBox="0 0 24 24" fill="none" stroke="currentColor" 
													stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
													class="icon icon-1">
													<path d="M3 12h4l3 8l4 -16l3 8h4"/>
												</svg>
											</span>
											<span class="nav-link-title">
												Offline Attack
											</span>
										</a>
										<div class="dropdown-menu">
											<div class="dropdown-menu-columns">
												<div class="dropdown-menu-column">
													<a href="/hashcat.html" class="dropdown-item">Dashboard</a>
													<div class="dropend">
														<a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
															Tools
														</a>
														<div class="dropdown-menu">
															<a href="/tools/hash-identifier" class="dropdown-item">Hash Identifier</a>
															<a href="/tools/hash-verifier" class="dropdown-item">Hash Verifier</a>
															<a href="/tools/email-extractor" class="dropdown-item">Email Extractor</a>
															<a href="/tools/2john-extractor" class="dropdown-item">*2john Hash Extractor</a>
															<a href="/tools/hash-generator" class="dropdown-item">Hash Generator</a>
															<a href="/tools/list-matching" class="dropdown-item">List Matching</a>
															<a href="/tools/file-parser" class="dropdown-item">File Parser</a>
															<a href="/tools/list-management" class="dropdown-item">List Management</a>
															<a href="/tools/base64-encoder" class="dropdown-item">Base64 Encoder</a>
															<a href="/tools/base64-decoder" class="dropdown-item">Base64 Decoder</a>
														</div>
													</div>
													<div class="dropend">
														<a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown" data-bs-auto-close="false" role="button" aria-expanded="false">
															Decrypt
														</a>
														<div class="dropdown-menu">
															<a href="/decrypt/search-hash" class="dropdown-item">Search Hash Database</a>
															<a href="/decrypt/hashcat" class="dropdown-item">Crack with Hashcat</a>
														</div>
													</div>
													<a href="/offline/results" class="dropdown-item">View Results</a>
													<a href="/hash-types" class="dropdown-item">Supported Hash Types</a>	
												</div>
											</div>
										</div>
									</li>
									<li class="nav-item">
										<a class="nav-link" href="#">
											<span class="nav-link-icon d-md-none d-lg-inline-block">
												<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													viewBox="0 0 24 24" fill="none" stroke="currentColor"
													stroke-width="2" stroke-linecap="round" stroke-linejoin="round" 
													class="icon icon-1">
													<path d="M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0"/>
													<path d="M12 8v-2"/>
													<path d="M8.5 9.5l-1.5 -1.5"/>
													<path d="M15.5 9.5l1.5 -1.5"/>
													<path d="M12 16v2"/>
													<path d="M8.5 14.5l-1.5 1.5"/>
													<path d="M15.5 14.5l1.5 1.5"/>
												</svg>
											</span>
											<span class="nav-link-title">
												Wifi Attack
											</span>
										</a>
									</li>
									<li class="nav-item">
										<a class="nav-link" href="/wordlists.html">
											<span class="nav-link-icon d-md-none d-lg-inline-block">
												<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
													viewBox="0 0 24 24" fill="none" stroke="currentColor"
													stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
													class="icon icon-1">
													<path d="M14 3v4a1 1 0 0 0 1 1h4"/>
													<path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
													<path d="M9 17h6"/>
													<path d="M9 13h6"/>
												</svg>
											</span>
											<span class="nav-link-title">
												Wordlists
											</span>
										</a>
									</li>
								</ul>
							</div>
							
						</div>
					</div>
				</div>
			</div>
		</header>
        {% block content %}{% endblock %}


			<footer class="footer footer-transparent d-print-none">
				<div class="container-xl">
					<div class="row text-center align-items-center">
						<div class="row">
							<div class="col-6 text-start">
								<ul class="list-inline list-inline-dots mb-0">
									<li class="list-inline-item">
										<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-inline me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
											<path stroke="none" d="M0 0h24v24H0z" fill="none"/>
											<path d="M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3" />
											<path d="M12 11m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
											<path d="M12 12l0 2.5" />
										</svg>
										<strong>Crackmaster</strong> - Network Penetration Testing Tool
									</li>
								</ul>
							</div>
							<div class="col-6 text-end">
								<p class="text-muted mb-0">
									<small>© 2025 Crackmaster. All rights reserved.</small>
								</p>
							</div>
						</div>
					</div>
				</div>
			</footer>
		</div>
	</div>


	<script src="./static/dist/js/tabler.min.js?**********" defer></script>
	<script src="./static/dist/js/demo.min.js?**********" defer></script>
	<script src="./static/dist/libs/dropzone/dist/dropzone-min.js?**********" defer></script>
    {% block script %}{% endblock %}


	<script>
		
		// Hàm hiển thị loading
		function showLoading(message = 'Loading...') {
			// Disable nút Start Audit
			const startButton = document.querySelector('button[onclick="startHydraAttack()"]');
			startButton.disabled = true;
			startButton.innerHTML = `
			<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
			${message}
		`;
		}

		// Hàm ẩn loading
		function hideLoading() {
			// Enable lại nút Start Audit
			const startButton = document.querySelector('button[onclick="startHydraAttack()"]');
			startButton.disabled = false;
			startButton.innerHTML = `
			<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-player-play" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
				<path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
				<path d="M7 4v16l13 -8z"></path>
			</svg>
			Start Audit
		`;
		}

		// Hàm hiển thị thông báo thành công
		function showSuccess(message) {
			const toastContainer = document.querySelector('.toast-container');
			if (!toastContainer) {
				// Tạo container nếu chưa tồn tại
				const container = document.createElement('div'); 
				container.className = 'toast-container position-fixed top-0 end-0 p-3';
				container.style.zIndex = "1050"; // Đảm bảo hiển thị trên các phần tử khác
				document.body.appendChild(container);
			}

			const toast = document.createElement('div');
			toast.className = 'toast align-items-center text-bg-success border-0';
			toast.setAttribute('role', 'alert');
			toast.setAttribute('aria-live', 'assertive');
			toast.setAttribute('aria-atomic', 'true');
			toast.innerHTML = `
				<div class="d-flex">
					<div class="toast-body">
						${message}
					</div>
					<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
			`;

			document.querySelector('.toast-container').appendChild(toast);
			const bsToast = new bootstrap.Toast(toast);
			bsToast.show();

			// Tự động xóa toast sau khi ẩn
			toast.addEventListener('hidden.bs.toast', () => {
				toast.remove();
			});
		}

		// Hàm hiển thị thông báo lỗi
		function showError(message) {
			const toastContainer = document.querySelector('.toast-container');
			if (!toastContainer) {
				// Tạo container nếu chưa tồn tại
				const container = document.createElement('div');
				container.className = 'toast-container position-fixed top-0 end-0 p-3';
				container.style.zIndex = "1050"; // Đảm bảo hiển thị trên các phần tử khác
				document.body.appendChild(container);
			}

			const toast = document.createElement('div');
			toast.className = 'toast align-items-center text-bg-danger border-0';
			toast.setAttribute('role', 'alert');
			toast.setAttribute('aria-live', 'assertive');
			toast.setAttribute('aria-atomic', 'true');
			toast.innerHTML = `
				<div class="d-flex">
					<div class="toast-body">
						${message}
					</div>
					<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
			`;

			document.querySelector('.toast-container').appendChild(toast);
			const bsToast = new bootstrap.Toast(toast);
			bsToast.show();

			// Tự động xóa toast sau khi ẩn
			toast.addEventListener('hidden.bs.toast', () => {
				toast.remove();
			});
		}

		// Thêm container cho toasts khi trang load
		document.addEventListener('DOMContentLoaded', function() {
			if (!document.querySelector('.toast-container')) {
				const toastContainer = document.createElement('div');
				toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
				toastContainer.style.zIndex = "1050"; // Đảm bảo hiển thị trên các phần tử khác
				document.body.appendChild(toastContainer);
			}
		});

			
	</script>

	<style>
		/* Thêm CSS để làm đẹp table */
		.table {
			margin-bottom: 0;
		}

		.table td {
			padding: 0.75rem;
			vertical-align: middle;
		}

		.text-nowrap {
			white-space: nowrap;
		}

		.text-wrap {
			white-space: normal;
		}

		.badge {
			font-size: 0.875rem;
			padding: 0.4em 0.8em;
		}

		.btn-list {
			display: flex;
			gap: 0.5rem;
		}

		.text-muted {
			font-size: 0.875rem;
		}
	</style>

</body>

</html>