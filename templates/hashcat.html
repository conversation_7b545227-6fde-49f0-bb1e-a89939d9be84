{% extends "base.html" %}

{% block content %}
<div class="page-wrapper">
    <div class="container-xl">
        <!-- Page title -->
        <div class="page-header d-print-none">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="page-title">
                        Offline Attack
                    </h2>
                </div>
            </div>
        </div>
    </div>

    <div class="page-body">
        <div class="container-xl">
            <div class="card">
                <div class="card-body">
                    <!-- Tabs -->
                    <div class="d-flex mb-4">
                        <button id="hashTabBtn" class="btn btn-primary me-2" onclick="toggleTab('hash')">Enter Hash</button>
                        <button id="fileTabBtn" class="btn btn-secondary" onclick="toggleTab('file')">Upload File</button>
                    </div>

                    <!-- Hash Input Area -->
                    <div id="hashTab" class="block">
                        <div class="position-relative">
                            <textarea class="form-control" rows="4" placeholder="Enter hash here (supports multiple lines)"></textarea>
                            <button class="btn btn-link position-absolute top-0 end-0 text-muted">Clear</button>
                        </div>
                    </div>

                    <!-- File Upload Area -->
                    <div id="fileTab" class="d-none">
                        <div class="dropzone border-2 border-dashed rounded p-6 text-center">
                            <input type="file" class="d-none" id="file-upload" onchange="document.getElementById('file-info').textContent = `Uploaded: ${this.files[0].name} (10 hashes)`" />
                            <label for="file-upload" class="cursor-pointer d-flex flex-column align-items-center">
                                <i class="fas fa-upload fs-1 text-primary mb-4"></i>
                                <p>Drag and drop or select hash file</p>
                                <p id="file-info" class="text-muted small mt-2"></p>
                            </label>
                        </div>
                    </div>

                    <!-- Crack Now Button -->
                    <button class="btn btn-primary w-100 mt-4" onclick="openCrackDetailPopup()">Crack Now</button>

                    <!-- Results Table -->
                    
                </div>
            </div>
            <div class="card custom-card-spacing">
                <div class="card-body">
                    <h3>Result</h3>
                    <div class="mt-4">
                        <div class="d-flex justify-content-between mb-4">
                            <div class="d-flex align-items-center gap-3">
                                <input type="text" class="form-control" placeholder="Search by hash/password">
                                <select class="form-select">
                                    <option>All methods</option>
                                    <option>Rainbow Table</option>
                                    <option>Detailed Crack</option>
                                </select>
                                <label class="form-check">
                                    <input type="checkbox" class="form-check-input">
                                    <span class="form-check-label">Show only results</span>
                                </label>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-vcenter card-table">
                                <thead>
                                    <tr>
                                        <th>Hash</th>
                                        <th>Password</th>
                                        <th>Method</th>
                                        <th>Time</th>
                                        <th class="w-1">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td title="5f4dcc3b5aa765d61d8327deb882cf99">5f4dcc3b5aa765d61d83...</td>
                                        <td>password123</td>
                                        <td>Rainbow Table</td>
                                        <td>5 minutes</td>
                                        <td>
                                            <div class="btn-list">
                                                <button class="btn btn-icon btn-ghost-secondary">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button class="btn btn-icon btn-ghost-secondary">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="btn btn-icon btn-ghost-secondary">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Task Manager Button -->
    <button id="taskButton" class="btn btn-primary position-fixed bottom-0 end-0 m-4 rounded-pill" onclick="openTaskManagerPopup()">
        Task (2)
    </button>
</div>

<!-- Crack Detail Popup -->
<div id="crackDetailPopup" class="modal modal-blur fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Crack Password</h5>
                <button type="button" class="btn-close" onclick="closeCrackDetailPopup()"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Entered Hash</label>
                    <div id="hashDisplay" class="form-control"></div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Hash Type</label>
                    <div class="position-relative">
                        <div class="custom-select" id="hashTypeWrapper">
                            <div class="select-header form-select" id="selectedHash">
                                Select hash type
                            </div>
                            <div id="hashTypeMessage" class="mt-1 small"></div>
                            <div class="select-dropdown" style="display: none;">
                                <div class="search-box p-2 border-bottom">
                                    <input type="text" 
                                           class="form-control" 
                                           id="hashSearch" 
                                           placeholder="Search hash types...">
                                </div>
                                <div class="options-container" id="hashOptions">
                                    <!-- Options will be populated here -->
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="hashTypeSelect" name="hashType">
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Cracking Method</label>
                    <div class="form-selectgroup">
                        <label class="form-selectgroup-item opacity-50" id="quickSearchLabel" data-bs-toggle="tooltip">
                            <input type="radio" 
                                   name="crackMethod" 
                                   value="quick" 
                                   class="form-selectgroup-input"  
                                   onclick="toggleCrackMethod()" 
                                   id="quickSearchRadio"
                                   disabled>
                            <span class="form-selectgroup-label">Quick Search (Pre-computed Database)</span>
                        </label>
                        <label class="form-selectgroup-item">
                            <input type="radio" 
                                   name="crackMethod" 
                                   value="detailed" 
                                   class="form-selectgroup-input" 
                                   id="detailedCrack" 
                                   onclick="toggleCrackMethod()"
                                   checked>
                            <span class="form-selectgroup-label">Hashcat Crack</span>
                        </label>
                    </div>
                </div>
                <div id="detailedOptions" class="d-none">
                    <div class="mb-3">
                        <label class="form-label">Attack Type</label>
                        <select class="form-select">
                            <option value="0">Dictionary Attack (Straight)</option>
                            <option value="1">Combination Attack</option>
                            <option value="3">Brute-force Attack</option>
                            <option value="6">Hybrid Wordlist + Mask</option>
                            <option value="7">Hybrid Mask + Wordlist</option>
                            <option value="9">Rule-based Combination</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Wordlist</label>
                        <input type="text" class="form-control" placeholder="Enter or select wordlist from DB">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mask (for Brute-force/Hybrid)</label>
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="?l?l?d">
                            <button class="btn btn-secondary" title="?l: letter, ?d: digit">?</button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Rules</label>
                        <select class="form-select mb-2">
                            <option>Select available rules</option>
                            <option>rockyou</option>
                            <option>dive</option>
                        </select>
                        <input type="file" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-check">
                            <input type="checkbox" class="form-check-input">
                            <span class="form-check-label">Use transformation rules</span>
                        </label>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Performance</label>
                        <select class="form-select">
                            <option value="1">Low Performance</option>
                            <option value="2" selected>Default Performance</option>
                            <option value="3">High Performance</option>
                            <option value="4">Ultra High Performance</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeCrackDetailPopup()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="startCrack()">Start</button>
            </div>
        </div>
    </div>
</div>

<!-- Task Manager Popup -->
<div id="taskManagerPopup" class="modal modal-blur fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Task Manager</h5>
                <button type="button" class="btn-close" onclick="closeTaskManagerPopup()"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex gap-3 mb-4">
                    <select class="form-select w-auto">
                        <option>All statuses</option>
                        <option>Running</option>
                        <option>Completed</option>
                        <option>Error</option>
                    </select>
                    <input type="text" class="form-control" placeholder="Search by ID or hash">
                </div>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table">
                        <thead>
                            <tr>
                                <th>Task ID</th>
                                <th>Hash</th>
                                <th>Status</th>
                                <th>Start Time</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>T001</td>
                                <td>5f4dcc3b5a...</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="status status-blue me-2">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </span>
                                        Running
                                        <div class="progress ms-2 flex-grow-1" style="width: 100px">
                                            <div class="progress-bar" style="width: 50%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>04/03/2025 12:00</td>
                                <td>
                                    <div class="btn-list">
                                        <button class="btn btn-icon btn-ghost-danger">
                                            <i class="fas fa-times-circle"></i>
                                        </button>
                                        <button class="btn btn-icon btn-ghost-primary" onclick="openTaskDetailPopup()">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <button class="btn btn-icon btn-ghost-secondary">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeTaskManagerPopup()">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Task Detail Sub-Modal -->
<div id="taskDetailPopup" class="modal modal-blur fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Task Details</h5>
                <button type="button" class="btn-close" onclick="closeTaskDetailPopup()"></button>
            </div>
            <div class="modal-body">
                <p><strong>Hash:</strong> 5f4dcc3b5aa765d61d8327deb882cf99</p>
                <p><strong>Method:</strong> Rainbow Table</p>
                <p><strong>Progress:</strong> 50%</p>
                <p><strong>Result:</strong> password123</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeTaskDetailPopup()">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block script %}
<script>
    let allHashTypes = [];
    let selectedValue = '';

    document.addEventListener('DOMContentLoaded', async function() {
        const wrapper = document.getElementById('hashTypeWrapper');
        const selectedHash = document.getElementById('selectedHash');
        const dropdown = wrapper.querySelector('.select-dropdown');
        const searchInput = document.getElementById('hashSearch');
        const optionsContainer = document.getElementById('hashOptions');
        const hiddenInput = document.getElementById('hashTypeSelect');
        const quickSearchRadio = document.getElementById('quickSearchRadio');
        const quickSearchLabel = document.getElementById('quickSearchLabel');
        const detailedCrack = document.getElementById('detailedCrack');
        let supportedQuickSearchTypes = [];

        // Mặc định: disable Quick Search và chọn Detailed Crack
        quickSearchRadio.disabled = true;
        quickSearchLabel.classList.add('opacity-50');
        quickSearchLabel.style.cursor = 'not-allowed';
        detailedCrack.checked = true;

        // Load hash types và khởi tạo tooltip
        try {
            const response = await fetch('/api/hash_types');
            const data = await response.json();
            console.log('Loaded hash types:', data.hash_types); // Debug log
            
            if (data.success) {
                allHashTypes = data.hash_types;
                supportedQuickSearchTypes = allHashTypes
                    .filter(h => h.is_precomputed)
                    .map(h => h.name);
                    
                console.log('Supported quick search types:', supportedQuickSearchTypes); // Debug log
                    
                renderOptions(allHashTypes);
                
                new bootstrap.Tooltip(quickSearchLabel, {
                    title: `Supported hash types for Quick Search:<br>- ${supportedQuickSearchTypes.join('<br>- ')}`,
                    html: true,
                    placement: 'top'
                });
            }
        } catch (error) {
            console.error('Error loading hash types:', error);
        }

        // Toggle dropdown
        selectedHash.addEventListener('click', () => {
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            if (dropdown.style.display === 'block') {
                searchInput.focus();
            }
        });

        // Handle search
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const filteredTypes = allHashTypes.filter(hash => 
                hash.name.toLowerCase().includes(searchTerm) ||
                hash.hashcat_id.toString().includes(searchTerm)
            );
            renderOptions(filteredTypes);
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!wrapper.contains(e.target)) {
                dropdown.style.display = 'none';
            }
        });

        function renderOptions(hashTypes) {
            const sortedTypes = [...hashTypes].sort((a, b) => {
                if (a.is_popuplar && !b.is_popuplar) return -1;
                if (!a.is_popuplar && b.is_popuplar) return 1;
                return a.name.localeCompare(b.name);
            });

            optionsContainer.innerHTML = sortedTypes.map(hash => `
                <div class="option-item ${hash.is_popuplar ? 'popular' : ''}" 
                     data-value="${hash.hashcat_id}"
                     data-precomputed="${hash.is_precomputed}"
                     title="Hashcat Mode: ${hash.hashcat_id}${hash.example ? '\nExample: ' + hash.example : ''}">
                    ${hash.name}
                </div>
            `).join('');

            // Handle option selection
            optionsContainer.querySelectorAll('.option-item').forEach(option => {
                option.addEventListener('click', () => {
                    selectedHash.textContent = option.textContent.trim();
                    hiddenInput.value = option.dataset.value;
                    dropdown.style.display = 'none';
                    selectedHash.dispatchEvent(new Event('change'));
                });
            });
        }

        // Xử lý khi chọn hash type
        selectedHash.addEventListener('change', function() {
            const selectedHashId = document.getElementById('hashTypeSelect').value;
            console.log('Selected hash ID:', selectedHashId); // Debug log
            
            const selectedHashType = allHashTypes.find(h => h.hashcat_id.toString() === selectedHashId);
            console.log('Selected hash type:', selectedHashType); // Debug log
            
            if (selectedHashType) {
                console.log('Is precomputed:', selectedHashType.is_precomputed); // Debug log
                
                if (selectedHashType.is_precomputed === true) { // Thêm so sánh chặt chẽ
                    console.log('Enabling quick search'); // Debug log
                    quickSearchRadio.disabled = false;
                    quickSearchLabel.classList.remove('opacity-50');
                    quickSearchLabel.style.cursor = 'pointer';
                    quickSearchRadio.checked = true;
                    toggleCrackMethod();
                } else {
                    console.log('Disabling quick search'); // Debug log
                    quickSearchRadio.disabled = true;
                    quickSearchRadio.checked = false;
                    detailedCrack.checked = true;
                    quickSearchLabel.classList.add('opacity-50');
                    quickSearchLabel.style.cursor = 'not-allowed';
                    toggleCrackMethod();
                }
            } else {
                console.log('No hash type selected'); // Debug log
                quickSearchRadio.disabled = true;
                quickSearchRadio.checked = false;
                detailedCrack.checked = true;
                quickSearchLabel.classList.add('opacity-50');
                quickSearchLabel.style.cursor = 'not-allowed';
                toggleCrackMethod();
            }
        });
    });

    function toggleTab(tab) {
        document.getElementById('hashTab').classList.toggle('d-none', tab !== 'hash');
        document.getElementById('fileTab').classList.toggle('d-none', tab === 'hash');
        document.getElementById('hashTabBtn').classList.toggle('btn-primary', tab === 'hash');
        document.getElementById('hashTabBtn').classList.toggle('btn-secondary', tab !== 'hash');
        document.getElementById('fileTabBtn').classList.toggle('btn-primary', tab === 'file');
        document.getElementById('fileTabBtn').classList.toggle('btn-secondary', tab !== 'file');
    }

    async function openCrackDetailPopup() {
        const hashInput = document.querySelector('#hashTab textarea').value.trim();
        
        if (!hashInput) {
            showToast('error', 'Please enter a hash before continuing');
            return;
        }

        const modal = new bootstrap.Modal(document.getElementById('crackDetailPopup'));
        modal.show();

        document.getElementById('hashDisplay').textContent = hashInput;
        document.getElementById('hashDisplay').title = hashInput;

        // Thêm loading state
        const hashTypeMessage = document.getElementById('hashTypeMessage');
        const selectedHash = document.getElementById('selectedHash');
        const hiddenInput = document.getElementById('hashTypeSelect');

        hashTypeMessage.innerHTML = `
            <div class="d-flex align-items-center text-primary">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <span>Detecting hash type...</span>
            </div>
        `;

        try {
            const response = await fetch('/api/identify_hash', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ hash: hashInput })
            });

            const data = await response.json();
            console.log('API Response:', data); // Debug log

            if (data.success && data.algorithms && data.algorithms.length > 0) {
                const detectedType = data.algorithms[0];
                console.log('Detected type:', detectedType); // Debug log
                
                // Tìm hash type trong danh sách đã load
                const option = allHashTypes.find(h => h.name.toLowerCase() === detectedType.toLowerCase());
                console.log('Found option:', option); // Debug log
                
                if (option) {
                    selectedHash.textContent = option.name;
                    hiddenInput.value = option.hashcat_id;
                    
                    // Hiển thị thông báo thành công
                    hashTypeMessage.innerHTML = `
                        <span class="text-primary">
                            <i class="fas fa-check-circle me-1"></i>
                            Detected your hash is ${detectedType}. Do you want to change it?
                        </span>
                    `;
                } else {
                    // Không tìm thấy trong danh sách hash types
                    hashTypeMessage.innerHTML = `
                        <span class="text-warning">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            Hash type ${detectedType} detected but not supported. Please select manually.
                        </span>
                    `;
                }
            } else {
                hashTypeMessage.innerHTML = `
                    <span class="text-warning">
                        <i class="fas fa-exclamation-circle me-1"></i>
                        Unable to detect hash type. Please select manually.
                    </span>
                `;
            }
        } catch (error) {
            console.error('Error detecting hash type:', error);
            hashTypeMessage.innerHTML = `
                <span class="text-danger">
                    <i class="fas fa-times-circle me-1"></i>
                    Error detecting hash type. Please try again.
                </span>
            `;
        }
    }

    function closeCrackDetailPopup() {
        bootstrap.Modal.getInstance(document.getElementById('crackDetailPopup')).hide();
    }

    function toggleCrackMethod() {
        const detailedOptions = document.getElementById('detailedOptions');
        const detailedRadio = document.getElementById('detailedCrack');
        // detailedOptions.classList.toggle('d-none', !detailedRadio.checked);
        if (detailedRadio.checked) {
            detailedOptions.classList.remove('d-none');
        } else {
            detailedOptions.classList.add('d-none');
        }
    }

    function openTaskManagerPopup() {
        new bootstrap.Modal(document.getElementById('taskManagerPopup')).show();
        updateTaskButtonBlink();
    }

    function closeTaskManagerPopup() {
        bootstrap.Modal.getInstance(document.getElementById('taskManagerPopup')).hide();
    }

    function openTaskDetailPopup() {
        new bootstrap.Modal(document.getElementById('taskDetailPopup')).show();
    }

    function closeTaskDetailPopup() {
        bootstrap.Modal.getInstance(document.getElementById('taskDetailPopup')).hide();
    }

    function startCrack() {
        const hashInput = document.querySelector('#hashTab textarea').value || "No hash entered";
        if (hashInput === "No hash entered") {
            showError("Please enter a hash before starting!");
            return;
        }

        // Create moving hash effect
        const hashElement = document.createElement('div');
        hashElement.textContent = hashInput.length > 10 ? hashInput.substring(0, 10) + "..." : hashInput;
        hashElement.classList.add('hash-moving', 'bg-primary', 'text-white', 'px-2', 'py-1', 'rounded', 'shadow-md');
        document.body.appendChild(hashElement);

        // Starting position
        const popup = document.getElementById('crackDetailPopup').querySelector('.modal-content');
        const popupRect = popup.getBoundingClientRect();
        hashElement.style.left = `${popupRect.left + popupRect.width / 2 - 50}px`;
        hashElement.style.top = `${popupRect.top + popupRect.height / 2}px`;

        // Destination position
        const taskButton = document.getElementById('taskButton');
        const taskRect = taskButton.getBoundingClientRect();
        setTimeout(() => {
            hashElement.style.left = `${taskRect.left + 10}px`;
            hashElement.style.top = `${taskRect.top + 10}px`;
            hashElement.style.transform = "scale(0.5)";
            hashElement.style.opacity = "0";
        }, 50);

        setTimeout(() => {
            document.body.removeChild(hashElement);
            closeCrackDetailPopup();
            let currentTasks = parseInt(taskButton.textContent.match(/\d+/) || 0);
            currentTasks += 1;
            taskButton.textContent = `Task (${currentTasks})`;
            updateTaskButtonBlink();
        }, 600);
    }

    function updateTaskButtonBlink() {
        const taskButton = document.getElementById('taskButton');
        const taskCount = parseInt(taskButton.textContent.match(/\d+/) || 0);
        if (taskCount > 0) {
            taskButton.classList.add('active');
        } else {
            taskButton.classList.remove('active');
        }
    }

    // Hàm tiện ích hiển thị thông báo
    function showToast(type, message) {
        // Tạo toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0 position-fixed top-0 end-0 m-3`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Xóa toast sau khi ẩn
        toast.addEventListener('hidden.bs.toast', () => {
            document.body.removeChild(toast);
        });
    }

    // Thêm CSS cho loading spinner
    const style = document.createElement('style');
    style.textContent = `
        .spinner-border {
            width: 1rem;
            height: 1rem;
            border-width: 0.15em;
        }

        select:disabled {
            background-color: #e9ecef;
            cursor: not-allowed;
        }

        .text-primary {
            color: #0054a6 !important;
        }
    `;
    document.head.appendChild(style);

    document.addEventListener('DOMContentLoaded', function() {
        // Khởi tạo tooltip
        const quickSearchLabel = document.getElementById('quickSearchLabel');
        const tooltip = new bootstrap.Tooltip(quickSearchLabel, {
            title: 'Supported hash types for Quick Search',
            placement: 'top',
            html: true
        });

        // Cập nhật trạng thái Quick Search khi chọn hash type
        const selectedHash = document.getElementById('selectedHash');
        selectedHash.addEventListener('change', updateQuickSearchState);
    });

    function updateQuickSearchState() {
        const quickSearchRadio = document.getElementById('quickSearchRadio');
        const detailedCrack = document.getElementById('detailedCrack');
        const quickSearchLabel = document.getElementById('quickSearchLabel');
        const selectedHashId = document.getElementById('hashTypeSelect').value;
        
        // Tìm hash type được chọn
        const selectedHashType = allHashTypes.find(h => h.hashcat_id.toString() === selectedHashId);
        
        if (selectedHashType) {
            if (selectedHashType.is_precomputed) {
                // Enable Quick Search nếu hash type hỗ trợ
                quickSearchRadio.disabled = false;
                quickSearchLabel.classList.remove('opacity-50');
                quickSearchLabel.style.cursor = 'pointer';
                
                // Cập nhật tooltip với thông tin chi tiết
                const tooltip = bootstrap.Tooltip.getInstance(quickSearchLabel);
                tooltip.dispose(); // Xóa tooltip cũ
                new bootstrap.Tooltip(quickSearchLabel, {
                    title: 'This hash type supports Quick Search',
                    placement: 'top'
                });
            } else {
                // Disable Quick Search và chuyển sang Detailed Crack
                quickSearchRadio.disabled = true;
                quickSearchRadio.checked = false;
                detailedCrack.checked = true;
                quickSearchLabel.classList.add('opacity-50');
                quickSearchLabel.style.cursor = 'not-allowed';
                
                // Cập nhật tooltip với danh sách hash types hỗ trợ
                const supportedTypes = allHashTypes
                    .filter(h => h.is_precomputed)
                    .map(h => h.name)
                    .join('<br>');
                    
                const tooltip = bootstrap.Tooltip.getInstance(quickSearchLabel);
                tooltip.dispose(); // Xóa tooltip cũ
                new bootstrap.Tooltip(quickSearchLabel, {
                    title: `Quick Search is only available for:<br>${supportedTypes}`,
                    placement: 'top',
                    html: true
                });
                
                // Hiển thị detailed options
                toggleCrackMethod();
            }
        }
    }

    // Cập nhật hàm khi chọn hash type
    function selectHashType(hashType) {
        selectedHash.textContent = hashType.name;
        hiddenInput.value = hashType.hashcat_id;
        dropdown.style.display = 'none';
        
        // Cập nhật trạng thái Quick Search
        updateQuickSearchState();
    }
</script>

<style>
    .task-button.active {
        animation: blink 1s infinite;
    }
    @keyframes blink {
        50% { opacity: 0.6; }
    }
    .hash-moving {
        position: fixed;
        transition: all 0.5s ease;
        z-index: 100;
    }
    .custom-card-spacing {
        margin-top: 10px; /* Điều chỉnh giá trị theo ý muốn */
    }
    /* CSS cho input hash display */
    #hashDisplay {
        font-family: monospace;
        word-wrap: break-word;
        white-space: pre-wrap;
        min-height: 40px;
        max-height: 80px;
        overflow-y: auto;
        padding: 8px 12px;
        color: #e2e8f0;            /* Màu chữ sáng */
        border: 1px solid #2d3748;
        border-radius: 4px;
        font-size: 14px;
        line-height: 1.5;
        cursor: text;
        user-select: all;         /* Cho phép select toàn bộ text khi click */
    }

    #hashDisplay::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    #hashDisplay::-webkit-scrollbar-track {
        background: #2d3748;
        border-radius: 4px;
    }

    #hashDisplay::-webkit-scrollbar-thumb {
        background: #4a5568;
        border-radius: 4px;
    }

    #hashDisplay::-webkit-scrollbar-thumb:hover {
        background: #718096;
    }

    /* Thêm hiệu ứng hover */
    #hashDisplay:hover {
        border-color: #4299e1;
    }

    /* Thêm style cho search input trong select */
    #hashTypeSearch {
        padding-right: 35px;
    }

    select.form-select {
        width: 100%;
    }

    /* Tùy chỉnh màu nền cho các option phổ biến nếu muốn */
    option[data-precomputed="true"] {
        background-color: rgba(32, 107, 196, 0.06);
    }

    .input-icon {
        position: relative;
    }

    .input-icon .input-icon-addon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 10px;
        color: #6c757d;
        pointer-events: none;
    }

    .position-relative {
        position: relative;
    }

    /* Select styles */
    #hashTypeSelect {
        width: 100%;
        background-color: var(--tblr-bg-surface);
        border: 1px solid rgba(101, 109, 119, 0.16);
        color: #fff;
        padding: 0.5rem 2.25rem 0.5rem 0.75rem;
    }

    /* Style cho dropdown menu */
    select#hashTypeSelect option {
        background-color: #1d273b;
        color: #fff;
        padding: 8px 12px;
    }

    /* Style cho option được hover */
    select#hashTypeSelect option:hover,
    select#hashTypeSelect option:focus {
        background-color: #206bc4;
    }

    /* Style cho search box trong dropdown */
    select#hashTypeSelect:focus + .select-search-box {
        display: block;
    }

    .select-search-box {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background: #1d273b;
        padding: 8px;
        border-bottom: 1px solid rgba(101, 109, 119, 0.16);
    }

    .select-search-box input {
        width: 100%;
        padding: 6px 12px;
        border: 1px solid rgba(101, 109, 119, 0.16);
        background: transparent;
        color: #fff;
    }

    .select-search-box input:focus {
        outline: none;
        border-color: #206bc4;
    }

    .custom-select {
        position: relative;
        width: 100%;
    }

    .select-header {
        cursor: pointer;
        background-color: var(--tblr-bg-surface);
        border: 1px solid rgba(101, 109, 119, 0.16);
        color: #fff;
    }

    .select-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #1d273b;
        border: 1px solid rgba(101, 109, 119, 0.16);
        border-top: none;
        border-radius: 0 0 4px 4px;
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
    }

    .search-box {
        position: sticky;
        top: 0;
        background: #1d273b;
        z-index: 1;
    }

    .search-box input {
        background: transparent;
        color: #fff;
    }

    .search-box input::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .options-container {
        padding: 0.5rem 0;
    }

    .option-item {
        padding: 0.5rem 0.75rem;
        cursor: pointer;
        color: #fff;
    }

    .option-item:hover {
        background-color: #206bc4;
    }

    .option-item.popular {
        background-color: rgba(32, 107, 196, 0.1);
    }

    /* Thêm style cho disabled state */
    .form-selectgroup-item.opacity-50 {
        opacity: 0.5;
    }

    .form-selectgroup-item[style*="cursor: not-allowed"] .form-selectgroup-label {
        background-color: var(--tblr-bg-surface-secondary);
    }

    /* Style cho tooltip */
    .tooltip-inner {
        max-width: 300px;
        text-align: left;
        font-size: 0.875rem;
    }

    .tooltip-inner br {
        margin-bottom: 0.25rem;
    }
</style>
{% endblock %}