{% extends "base.html" %}
{% block content %}
<div class="page-wrapper">
	<div class="page-header d-print-none">
		<div class="container-xl">
			<div class="row g-2 align-items-center">
				<div class="col">
					<h2 class="page-title">
						Online Attack
					</h2>
				</div>

			</div>

		</div>
	</div>
	<div class="page-body">
		<div class="container-xl">
			<!-- Phần cấu hình audit -->


			<div class="card mb-4">
				<div class="card-header">
					<h3 class="card-title">Online Attack Spray Password</h3>
				</div>
				<div class="card-body">
					<!-- Row 1: Target Address, Protocol & Port -->
					<div class="row">
						<!-- Target Address -->
						<div class="col-md-4">
							<div class="mb-3">
								<label class="form-label">Target Address</label>
								<input type="text" class="form-control" name="target"
									placeholder="Enter IP address or domain">
							</div>
						</div>
						<!-- Protocol -->
						<div class="col-md-4">
							<div class="mb-3">
								<label class="form-label">Target Protocol</label>
								<select class="form-select" id="protocol" name="protocol" onchange="updateDefaultPort()">
									<!-- Các giao thức phổ biến -->
									<option value="ssh" data-port="22" selected>SSH</option>
									<option value="ftp" data-port="21">FTP</option>
									<option value="ftps" data-port="990">FTPS</option>
									<option value="rdp" data-port="3389">RDP</option>
									<option value="telnet" data-port="23">Telnet</option>
									<option value="telnets" data-port="992">Telnets</option>
									<option value="http-get" data-port="80">HTTP-GET</option>
									<option value="http-post" data-port="80">HTTP-POST</option>
									<option value="https-get" data-port="443">HTTPS-GET</option>
									<option value="https-post" data-port="443">HTTPS-POST</option>
									<option value="smb" data-port="445">SMB</option>
									<option value="mysql" data-port="3306">MySQL</option>
									<option value="mssql" data-port="1433">MSSQL</option>
									<option value="vnc" data-port="5900">VNC</option>
									
									<!-- Các giao thức ít phổ biến hơn -->
									<option value="adam6500" data-port="502">Adam6500</option>
									<option value="asterisk" data-port="5038">Asterisk</option>
									<option value="cisco" data-port="23">Cisco</option>
									<option value="cisco-enable" data-port="23">Cisco-Enable</option>
									<option value="cobaltstrike" data-port="50050">Cobaltstrike</option>
									<option value="cvs" data-port="2401">CVS</option>
									<option value="firebird" data-port="3050">Firebird</option>
									<option value="http-head" data-port="80">HTTP-HEAD</option>
									<option value="http-get-form" data-port="80">HTTP-GET-Form</option>
									<option value="http-post-form" data-port="80">HTTP-POST-Form</option>
									<option value="http-proxy" data-port="8080">HTTP-Proxy</option>
									<option value="http-proxy-urlenum" data-port="8080">HTTP-Proxy-Urlenum</option>
									<option value="https-head" data-port="443">HTTPS-HEAD</option>
									<option value="https-get-form" data-port="443">HTTPS-GET-Form</option>
									<option value="https-post-form" data-port="443">HTTPS-POST-Form</option>
									<option value="icq" data-port="5190">ICQ</option>
									<option value="imap" data-port="143">IMAP</option>
									<option value="imaps" data-port="993">IMAPS</option>
									<option value="irc" data-port="6667">IRC</option>
									<option value="ldap2" data-port="389">LDAP2</option>
									<option value="ldap2s" data-port="636">LDAP2S</option>
									<option value="ldap3" data-port="389">LDAP3</option>
									<option value="ldap3-crammd5" data-port="389">LDAP3-CRAMMD5</option>
									<option value="ldap3-digestmd5" data-port="389">LDAP3-DIGESTMD5</option>
									<option value="ldap3s" data-port="636">LDAP3S</option>
									<option value="memcached" data-port="11211">Memcached</option>
									<option value="mongodb" data-port="27017">MongoDB</option>
									<option value="nntp" data-port="119">NNTP</option>
									<option value="oracle-listener" data-port="1521">Oracle-Listener</option>
									<option value="oracle-sid" data-port="1521">Oracle-SID</option>
									<option value="pcanywhere" data-port="5631">PCAnywhere</option>
									<option value="pcnfs" data-port="2049">PCNFS</option>
									<option value="pop3" data-port="110">POP3</option>
									<option value="pop3s" data-port="995">POP3S</option>
									<option value="postgres" data-port="5432">PostgreSQL</option>
									<option value="radmin2" data-port="4899">Radmin2</option>
									<option value="redis" data-port="6379">Redis</option>
									<option value="rexec" data-port="512">RExec</option>
									<option value="rlogin" data-port="513">RLogin</option>
									<option value="rpcap" data-port="2002">RPCAP</option>
									<option value="rsh" data-port="514">RSH</option>
									<option value="rtsp" data-port="554">RTSP</option>
									<option value="s7-300" data-port="102">S7-300</option>
									<option value="sip" data-port="5060">SIP</option>
									<option value="smtp" data-port="25">SMTP</option>
									<option value="smtps" data-port="465">SMTPS</option>
									<option value="smtp-enum" data-port="25">SMTP-Enum</option>
									<option value="snmp" data-port="161">SNMP</option>
									<option value="socks5" data-port="1080">SOCKS5</option>
									<option value="sshkey" data-port="22">SSH-Key</option>
									<option value="svn" data-port="3690">SVN</option>
									<option value="teamspeak" data-port="8767">TeamSpeak</option>
									<option value="vmauthd" data-port="902">VMAuthd</option>
									<option value="xmpp" data-port="5222">XMPP</option>
								</select>
							</div>
						</div>
						<!-- Port -->
						<div class="col-md-4">
							<div class="mb-3">
								<label class="form-label">Port</label>
								<input type="number" class="form-control" name="port" id="port" value="22" placeholder="Port">
							</div>
						</div>
					</div>

					<!-- Row 2: Username and Password -->
					<div class="row">
						<!-- Username Section -->
						<div class="col-md-6">
							<div class="mb-3">
								<label class="form-label">Username/List</label>
								<!-- Tabs cho Username -->
								<ul class="nav nav-tabs" id="usernameTab" role="tablist">
									<li class="nav-item" role="presentation">
										<button class="nav-link active" id="manual-username-tab" data-bs-toggle="tab"
											data-bs-target="#manual-username" type="button" role="tab"
											aria-controls="manual-username" aria-selected="true">
											Manual Input
										</button>
									</li>
									<li class="nav-item" role="presentation">
										<button class="nav-link" id="select-username-tab" data-bs-toggle="tab"
											data-bs-target="#select-username" type="button" role="tab"
											aria-controls="select-username" aria-selected="false">
											From Wordlist
										</button>
									</li>
								</ul>
								<div class="tab-content pt-3" id="usernameTabContent">
									<!-- Manual Input -->
									<div class="tab-pane fade show active" id="manual-username" role="tabpanel"
										aria-labelledby="manual-username-tab">
										<textarea class="form-control" id="username-textarea" rows="3"
											placeholder="Enter usernames (separated by commas or new lines)"></textarea>
									</div>
									<!-- From Wordlist -->
									<div class="tab-pane fade" id="select-username" role="tabpanel"
										aria-labelledby="select-username-tab">
										<select class="form-select" id="username-wordlist-select">
											<option selected disabled>Select username wordlist</option>
										</select>
									</div>
								</div>
							</div>
						</div>

						<!-- Password Section -->
						<div class="col-md-6">
							<div class="mb-3">
								<label class="form-label">Password Dictionary</label>
								<!-- Tabs cho Password -->
								<ul class="nav nav-tabs" id="passwordTab" role="tablist">
									<li class="nav-item" role="presentation">
										<button class="nav-link active" id="manual-password-tab" data-bs-toggle="tab"
											data-bs-target="#manual-password" type="button" role="tab"
											aria-controls="manual-password" aria-selected="true">
											Manual Input
										</button>
									</li>
									<li class="nav-item" role="presentation">
										<button class="nav-link" id="select-password-tab" data-bs-toggle="tab"
											data-bs-target="#select-password" type="button" role="tab"
											aria-controls="select-password" aria-selected="false">
											From Wordlist
										</button>
									</li>
								</ul>
								<div class="tab-content pt-3" id="passwordTabContent">
									<!-- Manual Input -->
									<div class="tab-pane fade show active" id="manual-password" role="tabpanel"
										aria-labelledby="manual-password-tab">
										<textarea class="form-control" id="password-textarea" rows="3"
											placeholder="Enter passwords (separated by commas or new lines)"></textarea>
									</div>
									<!-- From Wordlist -->
									<div class="tab-pane fade" id="select-password" role="tabpanel"
										aria-labelledby="select-password-tab">
										<select class="form-select" id="password-wordlist-select">
											<option selected disabled>Select password wordlist</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Advanced Options -->
					<div class="accordion" id="advancedOptions">
						<!-- Credential Options -->
						<div class="accordion-item">
							<h2 class="accordion-header" id="headingCredOpts">
								<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
									data-bs-target="#collapseCredOpts" aria-expanded="false"
									aria-controls="collapseCredOpts">
									Credential Options
								</button>
							</h2>
							<div id="collapseCredOpts" class="accordion-collapse collapse"
								aria-labelledby="headingCredOpts" data-bs-parent="#advancedOptions">
								<div class="accordion-body">
									<div class="row mt-3">
										<!-- Combo File Section -->
										<div class="col-md-6">
											<div class="mb-3">
												<label class="form-label" data-bs-toggle="tooltip" 
													title="Load username:password pairs from a file, one pair per line">
													Combo File (-C)
												</label>
												<!-- Tabs cho Combo -->
												<ul class="nav nav-tabs" id="comboTab" role="tablist">
													<li class="nav-item" role="presentation">
														<button class="nav-link active" id="manual-combo-tab"
															data-bs-toggle="tab" data-bs-target="#manual-combo"
															type="button" role="tab" aria-controls="manual-combo"
															aria-selected="true">
															Manual Input
														</button>
													</li>
													<li class="nav-item" role="presentation">
														<button class="nav-link" id="select-combo-tab"
															data-bs-toggle="tab" data-bs-target="#select-combo"
															type="button" role="tab" aria-controls="select-combo"
															aria-selected="false">
															From Wordlist
														</button>
													</li>
												</ul>
												<div class="tab-content pt-3" id="comboTabContent">
													<!-- Manual Input -->
													<div class="tab-pane fade show active" id="manual-combo"
														role="tabpanel" aria-labelledby="manual-combo-tab">
														<textarea class="form-control" id="combo-textarea" rows="3"
															placeholder="Enter username:password pairs (one per line)&#10;Example:&#10;admin:password123&#10;user:pass123"></textarea>
													</div>
													<!-- From Wordlist -->
													<div class="tab-pane fade" id="select-combo" role="tabpanel"
														aria-labelledby="select-combo-tab">
														<select class="form-select" id="combo-wordlist-select">
															<option selected disabled>Select combo wordlist</option>
														</select>
													</div>
												</div>
											</div>
										</div>
										<!-- Dynamic Password -->
										<div class="col-md-6">
											<label class="form-label" data-bs-toggle="tooltip"
												title="Generate passwords automatically. Format: MIN:MAX:CHARSET&#10;CHARSET: a=lowercase, A=uppercase, 1=numbers, !=special chars&#10;Example: 6:8:a1! = 6-8 chars using lowercase, numbers and special chars">
												Dynamic Password (-x MIN:MAX:CHARSET)
											</label>
											<input type="text" class="form-control" id="dynamic-password"
												placeholder="e.g., 6:8:a1!">
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Extended Options -->
						<div class="accordion-item">
							<h2 class="accordion-header" id="headingExtendedOpts">
								<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
									data-bs-target="#collapseExtendedOpts" aria-expanded="false"
									aria-controls="collapseExtendedOpts">
									Extended Options
								</button>
							</h2>
							<div id="collapseExtendedOpts" class="accordion-collapse collapse"
								aria-labelledby="headingExtendedOpts" data-bs-parent="#advancedOptions">
								<div class="accordion-body">
									<div class="row">
										<!-- -e nsr -->
										<div class="col-md-4">
											<label class="form-label">Additional Password Checks (-e nsr)</label>
											<div class="form-check">
												<input class="form-check-input" type="checkbox" value="n"
													id="chkEmptyPass">
												<label class="form-check-label" for="chkEmptyPass"
													data-bs-toggle="tooltip" title="Try empty/null password">
													Try empty password (n)
												</label>
											</div>
											<div class="form-check">
												<input class="form-check-input" type="checkbox" value="s"
													id="chkUserAsPass">
												<label class="form-check-label" for="chkUserAsPass"
													data-bs-toggle="tooltip" title="Try using the username as password">
													Use username as password (s)
												</label>
											</div>
											<div class="form-check">
												<input class="form-check-input" type="checkbox" value="r"
													id="chkReverseUser">
												<label class="form-check-label" for="chkReverseUser"
													data-bs-toggle="tooltip" title="Try reversed username as password (e.g. admin -> nimda)">
													Try reversed username (r)
												</label>
											</div>
										</div>
										<!-- -u -->
										<div class="col-md-4">
											<label class="form-label">Loop Control (-u)</label>
											<div class="form-check mt-2">
												<input class="form-check-input" type="checkbox" id="chkLoopUsers">
												<label class="form-check-label" for="chkLoopUsers"
													data-bs-toggle="tooltip" title="Try each username with all passwords before moving to next username">
													Loop through users first
												</label>
											</div>
										</div>
										<!-- -y -->
										<div class="col-md-4">
											<label class="form-label">Character Set Control (-y)</label>
											<div class="form-check mt-2">
												<input class="form-check-input" type="checkbox" id="chkNoSpecialChars">
												<label class="form-check-label" for="chkNoSpecialChars"
													data-bs-toggle="tooltip" title="Disable special chars when using -x brute force">
													Disable special characters
												</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<!-- Speed Tuning -->
						<div class="accordion-item">
							<h2 class="accordion-header" id="headingSpeedTuning">
								<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
									data-bs-target="#collapseSpeedTuning" aria-expanded="false"
									aria-controls="collapseSpeedTuning">
									Speed Tuning
								</button>
							</h2>
							<div id="collapseSpeedTuning" class="accordion-collapse collapse"
								aria-labelledby="headingSpeedTuning" data-bs-parent="#advancedOptions">
								<div class="accordion-body">
									<div class="row g-3">
										<div class="col-md-4">
											<label class="form-label" data-bs-toggle="tooltip"
												title="Number of parallel connections per target (default: 16)">
												Parallel Tasks (-t)
											</label>
											<input type="number" class="form-control" id="parallel-tasks"
												placeholder="Default: 16">
										</div>
										<div class="col-md-4">
											<label class="form-label" data-bs-toggle="tooltip"
												title="Total number of parallel connections (default: 64)">
												Total Tasks (-T)
											</label>
											<input type="number" class="form-control" id="total-tasks"
												placeholder="Default: 64">
										</div>
										<div class="col-md-4">
											<label class="form-label" data-bs-toggle="tooltip"
												title="Wait time for responses (default: 32s)">
												Response Timeout (-w)
											</label>
											<input type="number" class="form-control" id="response-timeout"
												placeholder="Default: 32s">
										</div>
									</div>
									<div class="row g-3 mt-1">
										<div class="col-md-4">
											<label class="form-label" data-bs-toggle="tooltip"
												title="Wait time between connection attempts">
												Connection Delay (-W)
											</label>
											<input type="number" class="form-control" id="connection-delay"
												placeholder="Time in seconds">
										</div>
										<div class="col-md-4">
											<label class="form-label" data-bs-toggle="tooltip"
												title="Wait time per login attempt (forces -t 1)">
												Login Attempt Delay (-c)
											</label>
											<input type="number" class="form-control" id="login-delay"
												placeholder="Time in seconds">
										</div>
									</div>
								</div>
							</div>
						</div>

					</div> <!-- end accordion -->

					<!-- Start Button -->
					<div class="mt-3">
						<button type="button" class="btn btn-primary w-100" onclick="startHydraAttack()">
							<svg class="icon"><!-- Tabler Play Icon --></svg>
							Start Audit
						</button>
					</div>
				</div> <!-- end card-body -->
			</div> <!-- end card -->


			<!-- Active Tasks Section -->
			<div class="card mb-3">
				<div class="card-header">
					<h3 class="card-title">
						Active Tasks
						<span class="badge bg-blue ms-2" id="activeTaskCount">0</span>
					</h3>
				</div>
				<div class="card-body p-0">
					<div class="table-responsive">
						<table id="activeTasksTable" class="table table-vcenter table-bordered mb-0">
							<thead>
								<tr>
									<th>Start Time</th>
									<th>Target</th>
									<th>Status</th>
									<th class="w-1">Actions</th>
								</tr>
							</thead>
							<tbody>
								<!-- Active tasks will be added here dynamically -->
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<!-- Audit Results -->
			<div class="card">
				<div class="card-header d-flex justify-content-between align-items-center">
					<h3 class="card-title">Audit Results</h3>
					<div class="d-flex align-items-center gap-2">
						<select id="perPageSelect" class="form-select form-select-sm" style="width: auto;"
							onchange="changePerPage(this.value)">
							<option value="5">5 rows</option>
							<option value="10" selected>10 rows</option>
							<option value="25">25 rows</option>
							<option value="50">50 rows</option>
						</select>
					</div>
				</div>
				<div class="card-body p-0">
					<div class="table-responsive">
						<table id="resultsTable" class="table table-vcenter table-bordered mb-0">
							<thead>
								<tr>
									<th>Start Time</th>
									<th>Duration</th>
									<th>Target</th>
									<th>Status</th>
									<th>Results/Error</th>
									<th class="w-1">Actions</th>
								</tr>
							</thead>
							<tbody>
								<!-- Rows will be added here dynamically -->
							</tbody>
						</table>
					</div>
				</div>
				<div class="card-footer d-flex justify-content-between align-items-center">
					<div class="text-muted" id="tableInfo">
						Showing <span id="startRow">0</span> to <span id="endRow">0</span> of <span
							id="totalRows">0</span> entries
					</div>
					<div id="tasksPagination">
						<!-- Pagination will be added here dynamically -->
					</div>
				</div>
			</div>
		</div>
	</div>
	{% endblock %}


	{% block script %}


	<script>
		// Khởi tạo các biến toàn cục
		let activeTasksTable;
		let resultsTable;
		let currentTasks = new Map(); // Lưu trữ các task đang chạy

		// Khởi tạo biến cho tbody
		let resultsTableBody;

		// Hàm khởi tạo khi trang load
		document.addEventListener('DOMContentLoaded', function () {
			// Khởi tạo tham chiếu đến tbody của bảng
			resultsTableBody = document.querySelector('#resultsTable tbody');
			if (!resultsTableBody) {
				console.error('Table body not found');
				return;
			}

			// Khởi tạo bảng tasks và results
			initializeTables();

			// Load tasks và results ban đầu
			loadActiveTasks();
			loadAuditResults();

			// Set interval để cập nhật trạng thái
			setInterval(updateTaskStatuses, 5000);

			// Xử lý form submit
			const form = document.getElementById('hydraForm');
			if (form) {
				form.addEventListener('submit', function (e) {
					e.preventDefault();
					startHydraAttack(e);
				});
			}
		});

		// Khởi tạo các bảng
		function initializeTables() {
			activeTasksTable = document.querySelector('#activeTasksTable');
			resultsTable = document.querySelector('#resultsTable');
		}

		// Load danh sách task đang chạy
		async function loadActiveTasks() {
			try {
				const response = await fetch('/get_all_tasks?status=running');
				const data = await response.json();

				if (!data || !data.tasks) {
					console.error('Invalid response data');
					return;
				}

				const activeTasksTable = document.getElementById('activeTasksTable');
				if (!activeTasksTable) {
					console.error('Active tasks table not found');
					return;
				}

				const tbody = activeTasksTable.querySelector('tbody');
				if (!tbody) {
					console.error('Active tasks tbody not found');
					return;
				}

				// Xóa nội dung cũ
				tbody.innerHTML = '';

				// Thêm các task đang chạy
				data.tasks.forEach(task => {
					const row = document.createElement('tr');

					const startTime = new Date(task.start_time * 1000).toLocaleString('vi-VN', {
						hour: '2-digit',
						minute: '2-digit',
						second: '2-digit',
						day: '2-digit',
						month: '2-digit',
						year: 'numeric'
					});

					row.innerHTML = `
					<td class="text-nowrap">${startTime}</td>
					<td class="text-nowrap">${task.target}:${task.port} (${task.protocol})</td>
					<td>
						<span class="badge bg-blue">
							running
						</span>
					</td>
					<td>
						<div class="btn-list flex-nowrap">
							<button class="btn btn-sm btn-warning" onclick="stopTask('${task._id}')" title="Stop">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-player-stop" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none">
									<path stroke="none" d="M0 0h24v24H0z" fill="none"/>
									<path d="M5 5m0 2a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2z" />
								</svg>
							</button>
						</div>
					</td>
				`;

					tbody.appendChild(row);
				});

				// Cập nhật số lượng task đang chạy
				const activeTaskCount = document.getElementById('activeTaskCount');
				if (activeTaskCount) {
					activeTaskCount.textContent = data.total;
				}

			} catch (error) {
				console.error('Error loading active tasks:', error);
			}
		}

		let currentPage = 1;
		let perPage = 10;

		// Thay đổi số dòng trên mỗi trang
		function changePerPage(value) {
			perPage = parseInt(value);
			currentPage = 1; // Reset về trang đầu
			loadAuditResults(currentPage);
		}

		async function loadAuditResults(page = 1) {
			try {
				const response = await fetch(`/get_all_tasks?status=completed,failed&page=${page}&per_page=${perPage}`);
				const data = await response.json();
				console.log(data)

				if (!data || !data.tasks) {
					console.error('Invalid response data');
					return;
				}

				const tbody = document.querySelector('#resultsTable tbody');
				if (!tbody) {
					console.error('Table body not found');
					return;
				}

				// Xóa nội dung cũ
				tbody.innerHTML = '';

				// Thêm rows mới
				data.tasks.reverse().forEach(result => {
					addResultToTable(result, tbody);
				});

				// Cập nhật thông tin phân trang
				updateTableInfo(data);
				updatePagination(data);

			} catch (error) {
				console.error('Error loading audit results:', error);
			}
		}

		function updateTableInfo(data) {
			const start = (data.page - 1) * data.per_page + 1;
			const end = Math.min(start + data.per_page - 1, data.total);

			document.getElementById('startRow').textContent = data.total ? start : 0;
			document.getElementById('endRow').textContent = end;
			document.getElementById('totalRows').textContent = data.total;
		}

		function updatePagination(data) {
			const paginationElement = document.getElementById('tasksPagination');
			if (!paginationElement) return;

			const totalPages = data.total_pages;
			currentPage = data.page;

			// Nếu không có dữ liệu hoặc chỉ có 1 trang
			if (totalPages <= 1) {
				paginationElement.innerHTML = '';
				return;
			}

			let paginationHtml = `
			<nav aria-label="Tasks navigation">
				<ul class="pagination mb-0">
					<!-- Nút Previous -->
					<li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
						<a class="page-link" href="#" onclick="event.preventDefault(); ${currentPage > 1 ? `loadAuditResults(${currentPage - 1})` : ''}" tabindex="-1">
							<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
								<path stroke="none" d="M0 0h24v24H0z" fill="none"/>
								<path d="M15 6l-6 6l6 6" />
							</svg>
							prev
						</a>
					</li>
		`;

			// Logic hiển thị số trang
			let startPage = Math.max(1, currentPage - 2);
			let endPage = Math.min(totalPages, currentPage + 2);

			// Điều chỉnh nếu ở gần đầu hoặc cuối
			if (startPage > 1) {
				paginationHtml += `
				<li class="page-item">
					<a class="page-link" href="#" onclick="event.preventDefault(); loadAuditResults(1)">1</a>
				</li>
			`;
				if (startPage > 2) {
					paginationHtml += `
					<li class="page-item disabled">
						<span class="page-link">...</span>
					</li>
				`;
				}
			}

			// Hiển thị các trang chính
			for (let i = startPage; i <= endPage; i++) {
				paginationHtml += `
				<li class="page-item ${i === currentPage ? 'active' : ''}">
					<a class="page-link" href="#" onclick="event.preventDefault(); loadAuditResults(${i})">${i}</a>
				</li>
			`;
			}

			// Hiển thị phần cuối
			if (endPage < totalPages) {
				if (endPage < totalPages - 1) {
					paginationHtml += `
					<li class="page-item disabled">
						<span class="page-link">...</span>
					</li>
				`;
				}
				paginationHtml += `
				<li class="page-item">
					<a class="page-link" href="#" onclick="event.preventDefault(); loadAuditResults(${totalPages})">${totalPages}</a>
				</li>
			`;
			}

			// Nút Next
			paginationHtml += `
			<li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
				<a class="page-link" href="#" onclick="event.preventDefault(); ${currentPage < totalPages ? `loadAuditResults(${currentPage + 1})` : ''}" tabindex="-1">
					next
					<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
						<path stroke="none" d="M0 0h24v24H0z" fill="none"/>
						<path d="M9 6l6 6l-6 6" />
					</svg>
				</a>
			</li>
		</ul>
	</nav>
		`;

			paginationElement.innerHTML = paginationHtml;
		}

		// Khởi tạo khi trang load
		document.addEventListener('DOMContentLoaded', function () {
			loadAuditResults(1);
		});

		// Cập nhật trạng thái các task
		async function updateTaskStatuses() {
			try {
				const response = await fetch('/get_all_tasks?status=running');
				const data = await response.json();

				if (!data || !data.tasks) {
					console.error('Invalid response data');
					return;
				}

				// Cập nhật số lượng task đang chạy
				const activeTaskCount = document.getElementById('activeTaskCount');
				if (activeTaskCount) {
					activeTaskCount.textContent = data.total;
				}

				// Nếu không còn task đang chạy, reload lại bảng kết quả
				if (data.total === 0) {
					loadAuditResults(currentPage);
				}

			} catch (error) {
				console.error('Error updating task statuses:', error);
			}
		}

		// Thêm task vào bảng
		function addTaskToTable(task) {
			const row = document.createElement('tr');
			row.id = `task-${task._id}`;
			row.innerHTML = `
			<td>${task.target}:${task.port} (${task.protocol})</td>
			<td>
				<div class="progress">
					<div class="progress-bar" role="progressbar" style="width: 0%" 
						 aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
				</div>
			</td>
			<td><span class="badge bg-blue">${task.status}</span></td>
			<td>
				<button class="btn btn-sm btn-danger" onclick="stopTask('${task._id}')">
					Stop
				</button>
			</td>
		`;
			activeTasksTable.appendChild(row);
		}

		// Thêm kết quả vào bảng
		function addResultToTable(result, tbody) {
			const row = document.createElement('tr');

			// Xác định màu sắc dựa trên status
			const statusColor = {
				'completed': 'success',
				'failed': 'danger',
				'stopped': 'warning'
			}[result.status] || 'secondary';

			// Format thời gian
			const startTime = new Date(result.start_time * 1000).toLocaleString('vi-VN', {
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit',
				day: '2-digit',
				month: '2-digit',
				year: 'numeric'
			});

			const endTime = result.end_time
				? new Date(result.end_time * 1000).toLocaleString('vi-VN', {
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit',
					day: '2-digit',
					month: '2-digit',
					year: 'numeric'
				})
				: '-';

			// Xác định nội dung hiển thị
			const resultContent = result.status === 'completed'
				? (result.results && result.results.length > 0 
					? result.results.map(r => `Username: ${r.username}, Password: ${r.password}`).join('<br>')
					: 'No credentials found')
				: (result.status === 'failed'
					? 'Attack failed' 
					: 'Attack stopped by user');

			row.innerHTML = `
			<td class="text-nowrap">${startTime}</td>
			<td class="text-nowrap">
				${result.end_time ? 
					((result.end_time - result.start_time) > 3600 ? 
						(Math.floor((result.end_time - result.start_time) / 3600) + 'h ' + Math.round(((result.end_time - result.start_time) % 3600) / 60) + 'm') : 
						((result.end_time - result.start_time) > 60 ? 
							(Math.round((result.end_time - result.start_time) / 60) + 'm') : 
							(Math.round((result.end_time - result.start_time)) + 's'))) 
					: '-'}
			</td>
			<td class="text-nowrap">${result.target}:${result.port} (${result.protocol})</td>
			<td>
				<span class="badge bg-${statusColor}">
					${result.status}
				</span>
			</td>
			<td class="text-wrap" style="max-width: 300px;">
				${result.status === 'completed' 
					? (result.results && result.results.length > 0
						? result.results.map(r => `<div class="mb-1">
							<span class="fw-bold text-success">Username:</span> <span class="fw-bold">${r.username}</span>, 
							<span class="fw-bold text-success">Password:</span> <span class="fw-bold">${r.password}</span>
							</div>`).join('')
						: 'No credentials found')
					: (result.status === 'failed'
						? 'Attack failed'
						: 'Attack stopped by user')
				}
				${result.error ? `<div class="text-danger small mt-1">Error: ${result.error}</div>` : ''}
			</td>
			<td>
				<div class="btn-list flex-nowrap">
					<!-- Nút View Output -->
					<button class="btn btn-sm btn-primary" 
							onclick="viewOutput('${result._id}')" 
							title="View Output">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-terminal" 
							 width="24" height="24" viewBox="0 0 24 24" stroke-width="2" 
							 stroke="currentColor" fill="none">
							<path stroke="none" d="M0 0h24v24H0z" fill="none"/>
							<path d="M5 7l5 5l-5 5"/>
							<path d="M12 19l7 0"/>
						</svg>
					</button>
					<!-- Nút Delete -->
					<button class="btn btn-sm btn-danger" 
							onclick="deleteTask('${result._id}')" 
							title="Delete">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-trash" 
							 width="24" height="24" viewBox="0 0 24 24" stroke-width="2" 
							 stroke="currentColor" fill="none">
							<path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
							<path d="M4 7l16 0"></path>
							<path d="M10 11l0 6"></path>
							<path d="M14 11l0 6"></path>
							<path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"></path>
							<path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"></path>
						</svg>
					</button>
				</div>
			</td>
		`;

			// Thêm row vào đầu tbody
			tbody.insertBefore(row, tbody.firstChild);
		}

		// Cập nhật UI của task
		function updateTaskUI(task) {
			const row = document.querySelector(`#task-${task._id}`);
			if (!row) return;

			// Cập nhật progress (giả định)
			const progressBar = row.querySelector('.progress-bar');
			if (progressBar) {
				const progress = estimateProgress(task);
				progressBar.style.width = `${progress}%`;
				progressBar.setAttribute('aria-valuenow', progress);
			}

			// Cập nhật status
			const statusBadge = row.querySelector('.badge');
			if (statusBadge) {
				statusBadge.className = `badge bg-${getStatusColor(task.status)}`;
				statusBadge.textContent = task.status;
			}
		}

		// Ước tính tiến độ (cần điều chỉnh theo logic thực tế)
		function estimateProgress(task) {
			// Implement logic để ước tính tiến độ dựa trên output của task
			return 50; // Placeholder
		}

		// Lấy màu cho status
		function getStatusColor(status) {
			const colors = {
				'running': 'blue',
				'completed': 'success',
				'failed': 'danger',
				'stopped': 'warning'
			};
			return colors[status] || 'secondary';
		}

		// Hàm lấy thông tin username source và ID wordlist (nếu có)
		function getUsernameInfo() {
			const isManualActive = document.getElementById('manual-username').classList.contains('active');
			let source = {
				type: isManualActive ? 'manual' : 'wordlist',
				info: null
			};

			if (isManualActive) {
				// Lấy từ manual input
				const textarea = document.getElementById('username-textarea');
				const usernames = textarea.value
					.split(/[\n,]/)
					.map(item => item.trim())
					.filter(item => item.length > 0);
				source.info = {
					list: usernames,
					description: usernames.length === 1 ? 'Single username' : `Manual list (${usernames.length} entries)`
				};
			} else {
				// Lấy ID của wordlist đã chọn
				const select = document.getElementById('username-wordlist-select');
				const selectedOption = select.options[select.selectedIndex];
				if (selectedOption && selectedOption.value !== 'disabled') {
					source.info = {
						wordlistId: selectedOption.value,
						wordlistName: selectedOption.text
					};
				}
			}

			return source;
		}

		// Hàm lấy thông tin password source và ID wordlist (nếu có)
		function getPasswordInfo() {
			const isManualActive = document.getElementById('manual-password').classList.contains('active');
			let source = {
				type: isManualActive ? 'manual' : 'wordlist',
				info: null
			};

			if (isManualActive) {
				// Lấy từ manual input
				const textarea = document.getElementById('password-textarea');
				const passwords = textarea.value
					.split(/[\n,]/)
					.map(item => item.trim())
					.filter(item => item.length > 0);
				source.info = {
					list: passwords,
					description: passwords.length === 1 ? 'Single password' : `Manual list (${passwords.length} entries)`
				};
			} else {
				// Lấy ID của wordlist đã chọn
				const select = document.getElementById('password-wordlist-select');
				const selectedOption = select.options[select.selectedIndex];
				if (selectedOption && selectedOption.value !== 'disabled') {
					source.info = {
						wordlistId: selectedOption.value,
						wordlistName: selectedOption.text
					};
				}
			}

			return source;
		}

		// Hàm bắt đầu Hydra attack
		async function startHydraAttack() {
			try {
				const target = document.querySelector('input[name="target"]').value.trim();
				const protocol = document.getElementById('protocol').value;
				const port = document.querySelector('input[name="port"]').value.trim();

				// Validate target và port
				validateTarget(target);
				if (!isValidPort(port)) {
					throw new Error('Port phải là số từ 0 đến 65535');
				}

				showLoading('Preparing attack...');

				const usernameSource = getUsernameInfo();
				const passwordSource = getPasswordInfo();
				const advancedOptions = getAdvancedOptions();
				const comboSource = advancedOptions.credentialOptions.comboFile;

				// Validate credentials
				validateCredentials(usernameSource, passwordSource, comboSource);

				// Tạo payload
				const payload = {
					target: target,
					protocol: protocol,
					port: port,
					sources: {
						username: usernameSource,
						password: passwordSource,
						combo: comboSource
					},
					options: {
						dynamic: advancedOptions.credentialOptions.dynamicPassword,
						extended: advancedOptions.extendedOptions,
						speed: advancedOptions.speedTuning
					}
				};

				const response = await fetch('/api/hydra/start', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(payload)
				});

				const data = await response.json();
				hideLoading();

				if (response.ok) {
					showSuccess('Attack started successfully');
					addActiveTask(data.taskId, {
						target: target,
						protocol: protocol,
						port: port,
						usernameSource: usernameSource,
						passwordSource: passwordSource,
						comboSource: comboSource,
						options: advancedOptions
					});
				} else {
					showError(data.error || 'Failed to start attack');
				}
			} catch (error) {
				hideLoading();
				console.error('Error starting attack:', error);
				showError(error.message || 'Failed to start attack');
			}
		}

		// Hàm hiển thị task trong bảng Active Tasks
		function addActiveTask(taskId, taskInfo) {
			const table = document.getElementById('activeTasksTable').getElementsByTagName('tbody')[0];
			const row = table.insertRow();

			// Format source info
			const formatSourceInfo = (source) => {
				if (source.type === 'manual') {
					return source.info.description;
				} else {
					return `Wordlist: ${source.info.wordlistName}`;
				}
			};

			row.innerHTML = `
			<td>${new Date().toLocaleString()}</td>
			<td>
				${taskInfo.target}:${taskInfo.port} (${taskInfo.protocol})<br>
				<small class="text-muted">
					Username: ${formatSourceInfo(taskInfo.usernameSource)}<br>
					Password: ${formatSourceInfo(taskInfo.passwordSource)}
				</small>
			</td>
			<td><span class="badge bg-blue">Running</span></td>
			<td>
				<button class="btn btn-sm btn-danger" onclick="stopTask('${taskId}')">Stop</button>
			</td>
		`;
		}

		// Dừng task
		async function stopTask(taskId) {
			try {
				const response = await fetch(`/stop_hydra/${taskId}`, {
					method: 'POST'
				});

				const result = await response.json();

				if (result.status === 'stopped') {
					// Cập nhật UI
					currentTasks.delete(taskId);
					loadActiveTasks();
				}
			} catch (error) {
				console.error('Error stopping task:', error);
				showError('Failed to stop task. Please try again.');
			}
		}

		// Thêm hàm xóa task
		async function deleteTask(taskId) {
			if (!confirm('Are you sure you want to delete this task?')) {
				return;
			}

			try {
				const response = await fetch(`/delete_task/${taskId}`, {
					method: 'DELETE'
				});

				if (response.ok) {
					// Xóa row khỏi table
					const row = document.querySelector(`tr[data-task-id="${taskId}"]`);
					if (row) {
						row.remove();
					}
					showSuccess('Success delete task.');
					// Reload lại danh sách
					loadAuditResults();
				} else {
					showError('Failed to delete task');
				}
			} catch (error) {
				console.error('Error deleting task:', error);
				showError('Error deleting task');
			}
		}

		async function viewRawLog(taskId) {
			try {
				const response = await fetch(`/get_task_log/${taskId}`);
				const data = await response.json();

				if (response.ok) {
					// Hiển thị modal với nội dung log
					const modal = document.createElement('div');
					modal.className = 'modal fade';
					modal.id = 'logModal';
					modal.innerHTML = `
					<div class="modal-dialog modal-lg">
						<div class="modal-content">
							<div class="modal-header">
								<h5 class="modal-title">Hydra Output Log</h5>
								<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
							</div>
							<div class="modal-body">
								<pre class="bg-dark text-light p-3 rounded" style="max-height: 500px; overflow-y: auto; font-family: monospace;">
${data.log_content}
								</pre>
							</div>
							<div class="modal-footer">
								<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
							</div>
						</div>
					</div>
				`;

					document.body.appendChild(modal);
					const modalInstance = new bootstrap.Modal(modal);
					modalInstance.show();

					// Cleanup khi modal đóng
					modal.addEventListener('hidden.bs.modal', function () {
						document.body.removeChild(modal);
					});
				} else {
					throw new Error(data.error || 'Failed to load log');
				}
			} catch (error) {
				console.error('Error viewing raw log:', error);
				showError('Failed to load log: ' + error.message);
			}
		}

		// Thêm hàm xem output
		async function viewOutput(taskId) {
			try {
				const response = await fetch(`/get_task_output/${taskId}`);
				const data = await response.json();

				if (response.ok) {
					const modal = document.createElement('div');
					modal.className = 'modal fade';
					modal.id = 'outputModal';

					// Format output với timestamp
					const formattedOutput = data.outputs.map(output => {
						const time = new Date(output.timestamp * 1000).toLocaleTimeString();
						return `[${time}] ${output.content}`;
					}).join('\n');

					modal.innerHTML = `
					<div class="modal-dialog modal-lg">
						<div class="modal-content">
							<div class="modal-header">
								<h5 class="modal-title">Command Output</h5>
								<button type="button" class="btn-close" data-bs-dismiss="modal"></button>
							</div>
							<div class="modal-body">
								<div class="mb-3">
									<strong>Command:</strong>
									<pre class="bg-light p-2 rounded text-dark">${data.command || 'N/A'}</pre>
								</div>
								<div class="mb-3">
									<strong>Thông tin đầu vào:</strong>
									<div class="bg-light p-2 rounded text-dark">
										${data.combo_source && data.combo_source.type === 'manual' && data.combo_source.info.list.length > 0 ? 
											`<div>Combo list: ${data.combo_source.info.list.join(', ')}</div>` :
											data.combo_source && data.combo_source.type === 'wordlist' && data.combo_source.info.wordlistName ?
											`<div>Combo wordlist: ${data.combo_source.info.wordlistName}</div>` : ''
										}
										${data.username_source ? 
											`<div>Username: ${data.username_source.type === 'manual' && data.username_source.info.list.length > 0 ? 
												data.username_source.info.list.join(', ') :
												data.username_source.info.wordlistName ? 
												`Wordlist: ${data.username_source.info.wordlistName}` : ''
											}</div>` : ''
										}
										${data.password_source ?
											`<div>Password: ${data.password_source.type === 'manual' && data.password_source.info.list.length > 0 ?
												data.password_source.info.list.join(', ') :
												data.password_source.info.wordlistName ?
												`Wordlist: ${data.password_source.info.wordlistName}` : ''
											}</div>` : ''
										}
									</div>
								</div>
								<div class="mb-3">
									<strong>Kết quả:</strong>
									<div class="bg-light p-2 rounded text-dark">
										${data.status === 'completed' ?
											(data.results && data.results.length > 0 ?
												data.results.map(result => 
													`<div>Username: ${result.username}, Password: ${result.password}</div>`
												).join('') :
												'Không tìm thấy thông tin đăng nhập'
											) :
											(data.status === 'failed' ? 
												'Tấn công thất bại' :
												'Tấn công đã bị dừng bởi người dùng'
											)
										}
										${data.error ? `<div class="text-danger small mt-1">Lỗi: ${data.error}</div>` : ''}
									</div>
								</div>
								<div>
									<strong>Output:</strong>
									<pre class="bg-dark text-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-family: monospace;">
${formattedOutput}
									</pre>
								</div>
							</div>
							<div class="modal-footer">
								<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
							</div>
						</div>
					</div>
				`;

					document.body.appendChild(modal);
					const modalInstance = new bootstrap.Modal(modal);
					modalInstance.show();

					// Cleanup khi modal đóng
					modal.addEventListener('hidden.bs.modal', function () {
						document.body.removeChild(modal);
					});
				} else {
					throw new Error(data.error || 'Failed to load output');
				}
			} catch (error) {
				console.error('Error viewing output:', error);
				showError('Failed to load output: ' + error.message);
			}
		}

		// Hàm load danh sách wordlist username từ backend
		async function loadUsernameWordlists() {
			try {
				const response = await fetch('/api/wordlists/usernames');
				const data = await response.json();

				const select = document.getElementById('username-wordlist-select');
				// Xóa tất cả options cũ trừ option đầu tiên (placeholder)
				while (select.options.length > 1) {
					select.remove(1);
				}

				// Thêm các options mới từ backend
				data.forEach(wordlist => {
					const option = document.createElement('option');
					option.value = wordlist.id;
					option.textContent = `${wordlist.name} (${wordlist.count.toLocaleString()} entries)`;
					if (wordlist.description) {
						option.title = wordlist.description; // Thêm tooltip
					}
					select.appendChild(option);
				});
			} catch (error) {
				console.error('Error loading username wordlists:', error);
				showError('Không thể tải danh sách username');
			}
		}

		// Xử lý khi chọn wordlist
		document.getElementById('username-wordlist-select').addEventListener('change', async function (e) {
			const wordlistId = e.target.value;
			if (!wordlistId) return;

			try {
				const response = await fetch(`/api/wordlists/usernames/${wordlistId}/preview`);
				const data = await response.json();

				// Cập nhật textarea với nội dung từ wordlist đã chọn
				const textarea = document.querySelector('#manual-username textarea');
				textarea.value = data.entries.join('\n');

				showSuccess(`Đã tải ${data.entries.length.toLocaleString()} username từ ${data.name}`);
			} catch (error) {
				console.error('Error loading wordlist content:', error);
				showError('Không thể tải nội dung wordlist');
			}
		});

		// Load danh sách wordlist khi tab được active
		document.getElementById('select-username-tab').addEventListener('shown.bs.tab', function (e) {
			loadUsernameWordlists();
		});


		// Hàm load danh sách wordlist password từ backend
		async function loadPasswordWordlists() {
			try {
				const response = await fetch('/api/wordlists/passwords');
				const data = await response.json();

				const select = document.getElementById('password-wordlist-select');
				// Xóa tất cả options cũ trừ option đầu tiên (placeholder)
				while (select.options.length > 1) {
					select.remove(1);
				}

				// Thêm các options mới từ backend
				data.forEach(wordlist => {
					const option = document.createElement('option');
					option.value = wordlist.id;
					option.textContent = `${wordlist.name} (${wordlist.count.toLocaleString()} entries)`;
					if (wordlist.description) {
						option.title = wordlist.description; // Thêm tooltip
					}
					select.appendChild(option);
				});
			} catch (error) {
				console.error('Error loading password wordlists:', error);
				showError('Không thể tải danh sách password');
			}
		}

		// Xử lý khi chọn password wordlist
		document.getElementById('password-wordlist-select').addEventListener('change', async function (e) {
			const wordlistId = e.target.value;
			if (!wordlistId) return;

			try {
				const response = await fetch(`/api/wordlists/passwords/${wordlistId}/preview`);
				const data = await response.json();

				// Cập nhật hidden textarea để lưu dữ liệu
				const textarea = document.getElementById('password-textarea');
				textarea.value = data.entries.join('\n');

				showSuccess(`Loaded ${data.entries.length.toLocaleString()} passwords from ${data.name}`);
			} catch (error) {
				console.error('Error loading wordlist content:', error);
				showError('Could not load wordlist content');
			}
		});

		// Load danh sách password wordlist khi tab được active
		document.getElementById('select-password-tab').addEventListener('shown.bs.tab', function (e) {
			loadPasswordWordlists();
		});



		// Hàm lấy danh sách username dựa vào tab đang active
		function getSelectedUsernames() {
			// Kiểm tra tab nào đang active
			const isManualActive = document.getElementById('manual-username').classList.contains('active');

			if (isManualActive) {
				// Lấy từ manual input
				const textarea = document.getElementById('username-textarea');
				return textarea.value
					.split(/[\n,]/)
					.map(item => item.trim())
					.filter(item => item.length > 0);
			} else {
				// Lấy từ wordlist đã chọn
				const select = document.getElementById('username-wordlist-select');
				const selectedWordlist = select.value;
				if (!selectedWordlist || selectedWordlist === 'disabled') {
					return [];
				}
				// Trả về danh sách từ wordlist đã load
				const textarea = document.getElementById('username-textarea');
				return textarea.value
					.split(/[\n,]/)
					.map(item => item.trim())
					.filter(item => item.length > 0);
			}
		}

		// Hàm helper để lấy danh sách password đã chọn
		function getSelectedPasswords() {
			const textarea = document.getElementById('password-textarea');
			return textarea.value
				.split(/[\n,]/)
				.map(item => item.trim())
				.filter(item => item.length > 0);
		}

		// Hàm hiển thị loading
		function showLoading(message = 'Loading...') {
			// Disable nút Start Audit
			const startButton = document.querySelector('button[onclick="startHydraAttack()"]');
			startButton.disabled = true;
			startButton.innerHTML = `
			<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
			${message}
		`;
		}

		// Hàm ẩn loading
		function hideLoading() {
			// Enable lại nút Start Audit
			const startButton = document.querySelector('button[onclick="startHydraAttack()"]');
			startButton.disabled = false;
			startButton.innerHTML = `
			<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-player-play" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
				<path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
				<path d="M7 4v16l13 -8z"></path>
			</svg>
			Start Audit
		`;
		}

		// Hàm hiển thị thông báo thành công
		function showSuccess(message) {
			const toastContainer = document.querySelector('.toast-container');
			if (!toastContainer) {
				// Tạo container nếu chưa tồn tại
				const container = document.createElement('div');
				container.className = 'toast-container position-fixed top-0 end-0 p-3';
				container.style.zIndex = "1050"; // Đảm bảo hiển thị trên các phần tử khác
				document.body.appendChild(container);
			}

			const toast = document.createElement('div');
			toast.className = 'toast align-items-center text-bg-success border-0';
			toast.setAttribute('role', 'alert');
			toast.setAttribute('aria-live', 'assertive');
			toast.setAttribute('aria-atomic', 'true');
			toast.innerHTML = `
				<div class="d-flex">
					<div class="toast-body">
						${message}
					</div>
					<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
			`;

			document.querySelector('.toast-container').appendChild(toast);
			const bsToast = new bootstrap.Toast(toast);
			bsToast.show();

			// Tự động xóa toast sau khi ẩn
			toast.addEventListener('hidden.bs.toast', () => {
				toast.remove();
			});
		}

		// Hàm hiển thị thông báo lỗi
		function showError(message) {
			const toastContainer = document.querySelector('.toast-container');
			if (!toastContainer) {
				// Tạo container nếu chưa tồn tại
				const container = document.createElement('div');
				container.className = 'toast-container position-fixed top-0 end-0 p-3';
				container.style.zIndex = "1050"; // Đảm bảo hiển thị trên các phần tử khác
				document.body.appendChild(container);
			}

			const toast = document.createElement('div');
			toast.className = 'toast align-items-center text-bg-danger border-0';
			toast.setAttribute('role', 'alert');
			toast.setAttribute('aria-live', 'assertive');
			toast.setAttribute('aria-atomic', 'true');
			toast.innerHTML = `
				<div class="d-flex">
					<div class="toast-body">
						${message}
					</div>
					<button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
			`;

			document.querySelector('.toast-container').appendChild(toast);
			const bsToast = new bootstrap.Toast(toast);
			bsToast.show();

			// Tự động xóa toast sau khi ẩn
			toast.addEventListener('hidden.bs.toast', () => {
				toast.remove();
			});
		}

		// Thêm container cho toasts khi trang load
		document.addEventListener('DOMContentLoaded', function () {
			if (!document.querySelector('.toast-container')) {
				const toastContainer = document.createElement('div');
				toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
				toastContainer.style.zIndex = "1050"; // Đảm bảo hiển thị trên các phần tử khác
				document.body.appendChild(toastContainer);
			}
		});

		// Hàm lấy thông tin combo file
		function getComboInfo() {
			const isManualActive = document.getElementById('manual-combo').classList.contains('active');
			let source = {
				type: isManualActive ? 'manual' : 'wordlist',
				info: null
			};

			if (isManualActive) {
				const textarea = document.getElementById('combo-textarea');
				const combos = textarea.value
					.split('\n')
					.map(item => item.trim())
					.filter(item => item.length > 0);
				source.info = {
					list: combos,
					description: `Manual combo list (${combos.length} entries)`
				};
			} else {
				const select = document.getElementById('combo-wordlist-select');
				const selectedOption = select.options[select.selectedIndex];
				if (selectedOption && selectedOption.value !== 'disabled') {
					source.info = {
						wordlistId: selectedOption.value,
						wordlistName: selectedOption.text
					};
				}
			}
			return source;
		}

		// Hàm lấy các tham số nâng cao
		function getAdvancedOptions() {
			return {
				credentialOptions: {
					comboFile: getComboInfo(),
					dynamicPassword: document.getElementById('dynamic-password').value
				},
				extendedOptions: {
					emptyPass: document.getElementById('chkEmptyPass')?.checked || false,
					userAsPass: document.getElementById('chkUserAsPass')?.checked || false, 
					reverseUser: document.getElementById('chkReverseUser')?.checked || false,
					loopUsers: document.getElementById('chkLoopUsers')?.checked || false,
					noSpecialChars: document.getElementById('chkNoSpecialChars')?.checked || false
				},
				speedTuning: {
					tasks: document.getElementById('parallel-tasks').value,
					totalTasks: document.getElementById('total-tasks').value, 
					waitTime: document.getElementById('response-timeout').value,
					waitBetween: document.getElementById('connection-delay').value,
					waitPerTry: document.getElementById('login-delay').value
				}
			};
		}

		// Cập nhật hàm startHydraAttack
		async function startHydraAttack() {
			try {
				const target = document.querySelector('input[name="target"]').value.trim();
				const protocol = document.getElementById('protocol').value;
				const port = document.querySelector('input[name="port"]').value.trim();

				// Validate target và port
				validateTarget(target);
				if (!isValidPort(port)) {
					throw new Error('Port phải là số từ 0 đến 65535');
				}

				showLoading('Preparing attack...');

				const usernameSource = getUsernameInfo();
				const passwordSource = getPasswordInfo();
				const advancedOptions = getAdvancedOptions();
				const comboSource = advancedOptions.credentialOptions.comboFile;

				// Validate credentials
				validateCredentials(usernameSource, passwordSource, comboSource);

				// Tạo payload
				const payload = {
					target: target,
					protocol: protocol,
					port: port,
					sources: {
						username: usernameSource,
						password: passwordSource,
						combo: comboSource
					},
					options: {
						dynamic: advancedOptions.credentialOptions.dynamicPassword,
						extended: advancedOptions.extendedOptions,
						speed: advancedOptions.speedTuning
					}
				};

				const response = await fetch('/api/hydra/start', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify(payload)
				});

				const data = await response.json();
				hideLoading();

				if (response.ok) {
					showSuccess('Attack started successfully');
					addActiveTask(data.taskId, {
						target: target,
						protocol: protocol,
						port: port,
						usernameSource: usernameSource,
						passwordSource: passwordSource,
						comboSource: comboSource,
						options: advancedOptions
					});
				} else {
					showError(data.error || 'Failed to start attack');
				}
			} catch (error) {
				hideLoading();
				console.error('Error starting attack:', error);
				showError(error.message || 'Failed to start attack');
			}
		}

		// Cập nhật hàm hiển thị task
		function addActiveTask(taskId, taskInfo) {
			const table = document.getElementById('activeTasksTable').getElementsByTagName('tbody')[0];
			const row = table.insertRow();

			const formatSourceInfo = (source) => {
				if (!source || source.type === 'none') return 'N/A';
				if (source.type === 'manual') {
					return source.info.description;
				} else {
					return `Wordlist: ${source.info.wordlistName}`;
				}
			};

			// Format options string
			const formatOptions = (options) => {
				const parts = [];
				if (!options) return 'Default options';
				
				if (options.dynamic) parts.push(`Dynamic: ${options.dynamic}`);
				
				if (options.extended) {
					if (options.extended.emptyPass) parts.push('Empty Pass');
					if (options.extended.userAsPass) parts.push('User as Pass'); 
					if (options.extended.reverseUser) parts.push('Reverse User');
				}
				
				if (options.speed && options.speed.tasks) {
					parts.push(`Tasks: ${options.speed.tasks}`);
				}
				
				return parts.join(', ') || 'Default options';
			};

			row.innerHTML = `
			<td>${new Date().toLocaleString()}</td>
			<td>
				${taskInfo.target}:${taskInfo.port} (${taskInfo.protocol})<br>
				<small class="text-muted">
					${taskInfo.comboSource.info ?
					`Combo: ${formatSourceInfo(taskInfo.comboSource)}` :
					`Username: ${formatSourceInfo(taskInfo.usernameSource)}<br>
						 Password: ${formatSourceInfo(taskInfo.passwordSource)}`
				}<br>
					Options: ${formatOptions(taskInfo.options)}
				</small>
			</td>
			<td><span class="badge bg-blue">Running</span></td>
			<td>
				<button class="btn btn-sm btn-danger" onclick="stopTask('${taskId}')">Stop</button>
			</td>
		`;
		}



		// Hàm validate địa chỉ IP
		function isValidIP(ip) {
			const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
			if (!ipRegex.test(ip)) return false;
			const parts = ip.split('.');
			return parts.every(part => {
				const num = parseInt(part, 10);
				return num >= 0 && num <= 255;
			});
		}

		// Hàm validate domain
		function isValidDomain(domain) {
			const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/;
			return domainRegex.test(domain);
		}

		// Hàm validate port
		function isValidPort(port) {
			const portNum = parseInt(port, 10);
			return !isNaN(portNum) && portNum >= 0 && portNum <= 65535;
		}

		// Hàm validate target
		function validateTarget(target) {
			if (!target) {
				throw new Error('Target Address không được để trống');
			}
			if (!isValidIP(target) && !isValidDomain(target)) {
				throw new Error('Target Address phải là địa chỉ IP hoặc tên miền hợp lệ');
			}
			return true;
		}

		// Hàm validate credentials
		function validateCredentials(usernameSource, passwordSource, comboSource) {
			// Kiểm tra xem có sử dụng combo file không
			const hasCombo = comboSource && comboSource.info && comboSource.info.list && comboSource.info.list.length > 0;

			// Kiểm tra xem có sử dụng username/password không
			const hasUsername = usernameSource && usernameSource.info;
			const hasPassword = passwordSource && passwordSource.info;

			// Kiểm tra dữ liệu username/password nếu được sử dụng
			const hasValidUsername = hasUsername && (
				usernameSource.type === 'wordlist' ||
				(usernameSource.type === 'manual' && usernameSource.info.list && usernameSource.info.list.length > 0)
			);
			const hasValidPassword = hasPassword && (
				passwordSource.type === 'wordlist' ||
				(passwordSource.type === 'manual' && passwordSource.info.list && passwordSource.info.list.length > 0)
			);

			// Nếu có combo file
			if (hasCombo) {
				return true;
			}

			// Nếu có username/password
			if (hasValidUsername && hasValidPassword) {
				return true;
			}

			// Nếu không có cả combo và username/password
			if (!hasCombo && !hasValidUsername && !hasValidPassword) {
				throw new Error('Vui lòng nhập combo file HOẶC cặp username/password');
			}

			// Nếu chỉ có username mà không có password
			if (hasValidUsername && !hasValidPassword) {
				throw new Error('Đã nhập username, vui lòng nhập thêm password');
			}

			// Nếu chỉ có password mà không có username
			if (!hasValidUsername && hasValidPassword) {
				throw new Error('Đã nhập password, vui lòng nhập thêm username');
			}

			throw new Error('Vui lòng kiểm tra lại thông tin đăng nhập');
		}

		// Hàm load danh sách combo wordlist
		async function loadComboWordlists() {
			try {
				const response = await fetch('/api/wordlists/combos');
				const data = await response.json();

				const select = document.getElementById('combo-wordlist-select');
				// Xóa options cũ
				while (select.options.length > 1) {
					select.remove(1);
				}

				// Thêm options mới
				data.forEach(wordlist => {
					const option = document.createElement('option');
					option.value = wordlist.id;
					option.textContent = `${wordlist.name} (${wordlist.count ? wordlist.count.toLocaleString() : 0} pairs)`;
					if (wordlist.description) {
						option.title = wordlist.description;
					}
					select.appendChild(option);
				});
			} catch (error) {
				console.error('Error loading combo wordlists:', error);
				showError('Could not load combo lists');
			}
		}

		function updateDefaultPort() {
    // Lấy protocol select element
    const protocolSelect = document.getElementById('protocol');
    // Lấy port input element
    const portInput = document.getElementById('port');
    
    // Lấy option được chọn
    const selectedOption = protocolSelect.options[protocolSelect.selectedIndex];
    
    // Lấy giá trị port mặc định từ data-port attribute
    const defaultPort = selectedOption.getAttribute('data-port');
    
    // Cập nhật giá trị port
    if (defaultPort) {
        portInput.value = defaultPort;
    }
}

		// Event listeners cho các tab
		document.getElementById('select-username-tab').addEventListener('shown.bs.tab', function (e) {
			console.log("loadUsernameWordlists select tab");
			loadUsernameWordlists();
		});

		document.getElementById('select-password-tab').addEventListener('shown.bs.tab', function (e) {
			loadPasswordWordlists();
		});

		document.getElementById('select-combo-tab').addEventListener('shown.bs.tab', function (e) {
			loadComboWordlists();
		});

		document.addEventListener('DOMContentLoaded', function() {
    updateDefaultPort();
});


	</script>
	{% endblock %}


	<style>
		/* Thêm CSS để làm đẹp table */
		.table {
			margin-bottom: 0;
		}

		.table td {
			padding: 0.75rem;
			vertical-align: middle;
		}

		.text-nowrap {
			white-space: nowrap;
		}

		.text-wrap {
			white-space: normal;
		}

		.badge {
			font-size: 0.875rem;
			padding: 0.4em 0.8em;
		}

		.btn-list {
			display: flex;
			gap: 0.5rem;
		}

		.text-muted {
			font-size: 0.875rem;
		}
	</style>