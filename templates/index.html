<!doctype html>
<!--
* Tabler - Premium and Open Source dashboard template with responsive and high quality UI.
* @version 1.0.0
* @link https://tabler.io
* Copyright 2018-2025 The Tabler Authors
* Copyright 2018-2025 codecalm.net Paweł Kuna
* Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
-->
<html lang="en">
<head>
	<meta charset="utf-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
	<meta http-equiv="X-UA-Compatible" content="ie=edge"/>
	<title>Tabler - Premium and Open Source dashboard template with responsive and high quality UI.</title>
	<!-- CSS files -->
<link href="./static/dist/css/tabler.min.css?**********" rel="stylesheet"/>
	<link href="./static/dist/css/tabler-flags.min.css?**********" rel="stylesheet"/>
	<link href="./static/dist/css/tabler-socials.min.css?**********" rel="stylesheet"/>
	<link href="./static/dist/css/tabler-payments.min.css?**********" rel="stylesheet"/>
	<link href="./static/dist/css/tabler-vendors.min.css?**********" rel="stylesheet"/>
	<link href="./static/dist/css/tabler-marketing.min.css?**********" rel="stylesheet"/>
<link href="./static/dist/css/demo.min.css?**********" rel="stylesheet"/>
	<style>
		@import url('https://rsms.me/inter/inter.css');
	</style>
</head>
<body>
<script src="./static/dist/js/demo-theme.js"></script>
<div class="page">
		<!-- Navbar -->
	<header class="navbar navbar-expand-md d-print-none" >
		<div class="container-xl">
			<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
	<span class="navbar-toggler-icon"></span>
</button>
<div class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
	<a href=".">
	<svg xmlns="http://www.w3.org/2000/svg" width="110" height="32" viewBox="0 0 232 68" class="navbar-brand-image">
		<path d="M64.6 16.2C63 9.9 58.1 5 51.8 3.4 40 1.5 28 1.5 16.2 3.4 9.9 5 5 9.9 3.4 16.2 1.5 28 1.5 40 3.4 51.8 5 58.1 9.9 63 16.2 64.6c11.8 1.9 23.8 1.9 35.6 0C58.1 63 63 58.1 64.6 51.8c1.9-11.8 1.9-23.8 0-35.6zM33.3 36.3c-2.8 4.4-6.6 8.2-11.1 11-1.5.9-3.3.9-4.8.1s-2.4-2.3-2.5-4c0-1.7.9-3.3 2.4-4.1 2.3-1.4 4.4-3.2 6.1-5.3-1.8-2.1-3.8-3.8-6.1-5.3-2.3-1.3-3-4.2-1.7-6.4s4.3-2.9 6.5-1.6c4.5 2.8 8.2 6.5 11.1 10.9 1 1.4 1 3.3.1 4.7zM49.2 46H37.8c-2.1 0-3.8-1-3.8-3s1.7-3 3.8-3h11.4c2.1 0 3.8 1 3.8 3s-1.7 3-3.8 3z" fill="#066fd1" style="fill: var(--tblr-primary, #066fd1)" />
		<path d="M105.8 46.1c.4 0 .9.2 1.2.6s.6 1 .6 1.7c0 .9-.5 1.6-1.4 2.2s-2 .9-3.2.9c-2 0-3.7-.4-5-1.3s-2-2.6-2-5.4V31.6h-2.2c-.8 0-1.4-.3-1.9-.8s-.9-1.1-.9-1.9c0-.7.3-1.4.8-1.8s1.2-.7 1.9-.7h2.2v-3.1c0-.8.3-1.5.8-2.1s1.3-.8 2.1-.8 1.5.3 2 .8.8 1.3.8 2.1v3.1h3.4c.8 0 1.4.3 1.9.8s.8 1.2.8 1.9-.3 1.4-.8 1.8-1.2.7-1.9.7h-3.4v13c0 .7.2 1.2.5 1.5s.8.5 1.4.5c.3 0 .6-.1 1.1-.2.5-.2.8-.3 1.2-.3zm28-20.7c.8 0 1.5.3 2.1.8.5.5.8 1.2.8 2.1v20.3c0 .8-.3 1.5-.8 2.1-.5.6-1.2.8-2.1.8s-1.5-.3-2-.8-.8-1.2-.8-2.1c-.8.9-1.9 1.7-3.2 2.4-1.3.7-2.8 1-4.3 1-2.2 0-4.2-.6-6-1.7-1.8-1.1-3.2-2.7-4.2-4.7s-1.6-4.3-1.6-6.9c0-2.6.5-4.9 1.5-6.9s2.4-3.6 4.2-4.8c1.8-1.1 3.7-1.7 5.9-1.7 1.5 0 3 .3 4.3.8 1.3.6 2.5 1.3 3.4 2.1 0-.8.3-1.5.8-2.1.5-.5 1.2-.7 2-.7zm-9.7 21.3c2.1 0 3.8-.8 5.1-2.3s2-3.4 2-5.7-.7-4.2-2-5.8c-1.3-1.5-3-2.3-5.1-2.3-2 0-3.7.8-5 2.3-1.3 1.5-2 3.5-2 5.8s.6 4.2 1.9 5.7 3 2.3 5.1 2.3zm32.1-21.3c2.2 0 4.2.6 6 1.7 1.8 1.1 3.2 2.7 4.2 4.7s1.6 4.3 1.6 6.9-.5 4.9-1.5 6.9-2.4 3.6-4.2 4.8c-1.8 1.1-3.7 1.7-5.9 1.7-1.5 0-3-.3-4.3-.9s-2.5-1.4-3.4-2.3v.3c0 .8-.3 1.5-.8 2.1-.5.6-1.2.8-2.1.8s-1.5-.3-2.1-.8c-.5-.5-.8-1.2-.8-2.1V18.9c0-.8.3-1.5.8-2.1.5-.6 1.2-.8 2.1-.8s1.5.3 2.1.8c.5.6.8 1.3.8 2.1v10c.8-1 1.8-1.8 3.2-2.5 1.3-.7 2.8-1 4.3-1zm-.7 21.3c2 0 3.7-.8 5-2.3s2-3.5 2-5.8-.6-4.2-1.9-5.7-3-2.3-5.1-2.3-3.8.8-5.1 2.3-2 3.4-2 5.7.7 4.2 2 5.8c1.3 1.6 3 2.3 5.1 2.3zm23.6 1.9c0 .8-.3 1.5-.8 2.1s-1.3.8-2.1.8-1.5-.3-2-.8-.8-1.3-.8-2.1V18.9c0-.8.3-1.5.8-2.1s1.3-.8 2.1-.8 1.5.3 2 .8.8 1.3.8 2.1v29.7zm29.3-10.5c0 .8-.3 1.4-.9 1.9-.6.5-1.2.7-2 .7h-15.8c.4 1.9 1.3 3.4 2.6 4.4 1.4 1.1 2.9 1.6 4.7 1.6 1.3 0 2.3-.1 3.1-.4.7-.2 1.3-.5 1.8-.8.4-.3.7-.5.9-.6.6-.3 1.1-.4 1.6-.4.7 0 1.2.2 1.7.7s.7 1 .7 1.7c0 .9-.4 1.6-1.3 2.4-.9.7-2.1 1.4-3.6 1.9s-3 .8-4.6.8c-2.7 0-5-.6-7-1.7s-3.5-2.7-4.6-4.6-1.6-4.2-1.6-6.6c0-2.8.6-5.2 1.7-7.2s2.7-3.7 4.6-4.8 3.9-1.7 6-1.7 4.1.6 6 1.7 3.4 2.7 4.5 4.7c.9 1.9 1.5 4.1 1.5 6.3zm-12.2-7.5c-3.7 0-5.9 1.7-6.6 5.2h12.6v-.3c-.1-1.3-.8-2.5-2-3.5s-2.5-1.4-4-1.4zm30.3-5.2c1 0 1.8.3 *******.5 1 1.2 1 1.9 0 1-.3 1.7-.8 2.2-.5.5-1.1.8-1.8.7-.5 0-1-.1-1.6-.3-.2-.1-.4-.1-.6-.2-.4-.1-.7-.1-1.1-.1-.8 0-1.6.3-2.4.8s-1.4 1.3-1.9 2.3-.7 2.3-.7 3.7v11.4c0 .8-.3 1.5-.8 2.1-.5.6-1.2.8-2.1.8s-1.5-.3-2.1-.8c-.5-.6-.8-1.3-.8-2.1V28.8c0-.8.3-1.5.8-2.1.5-.6 1.2-.8 2.1-.8s1.5.3 2.1.8c.5.6.8 1.3.8 2.1v.6c.7-1.3 1.8-2.3 3.2-3 1.3-.7 2.8-1 4.3-1z" fill-rule="evenodd" clip-rule="evenodd" fill="#4a4a4a"/>
	</svg>
	</a>
</div>
<div class="navbar-nav flex-row order-md-last">
	<div class="nav-item d-none d-md-flex me-3">
		<div class="btn-list">
<a href="https://github.com/tabler/tabler" class="btn btn-5" target="_blank" rel="noreferrer">
	<!-- Download SVG icon from http://tabler.io/icons/icon/brand-github -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2"><path d="M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5" /></svg>
Source code
</a>
<a href="https://github.com/sponsors/codecalm" class="btn btn-6" target="_blank" rel="noreferrer">
	<!-- Download SVG icon from http://tabler.io/icons/icon/heart -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-pink icon-2"><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
Sponsor
</a>
		</div>
	</div>
	<div class="d-none d-md-flex">
		<a href="?theme=dark" class="nav-link px-0 hide-theme-dark" title="Enable dark mode" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
	<!-- Download SVG icon from http://tabler.io/icons/icon/moon -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" /></svg>
		</a>
		<a href="?theme=light" class="nav-link px-0 hide-theme-light" title="Enable light mode" data-bs-toggle="tooltip"
		   data-bs-placement="bottom">
	<!-- Download SVG icon from http://tabler.io/icons/icon/sun -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" /><path d="M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7" /></svg>
		</a>
		<div class="nav-item dropdown d-none d-md-flex me-3">
			<a href="#" class="nav-link px-0" data-bs-toggle="dropdown" tabindex="-1" aria-label="Show notifications">
	<!-- Download SVG icon from http://tabler.io/icons/icon/bell -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M10 5a2 2 0 1 1 4 0a7 7 0 0 1 4 6v3a4 4 0 0 0 2 3h-16a4 4 0 0 0 2 -3v-3a7 7 0 0 1 4 -6" /><path d="M9 17v1a3 3 0 0 0 6 0v-1" /></svg>
				<span class="badge bg-red"></span>
			</a>
			<div class="dropdown-menu dropdown-menu-arrow dropdown-menu-end dropdown-menu-card">
				<div class="card">
	<div class="card-header">
		<h3 class="card-title">Last updates</h3>
	</div>
	<div class="list-group list-group-flush list-group-hoverable">
		<div class="list-group-item">
			<div class="row align-items-center">
				<div class="col-auto"><span class="status-dot status-dot-animated bg-red d-block"></span></div>
				<div class="col text-truncate">
					<a href="#" class="text-body d-block">Example 1</a>
					<div class="d-block text-secondary text-truncate mt-n1">
						Change deprecated html tags to text decoration classes (#29604)
					</div>
				</div>
				<div class="col-auto">
					<a href="#" class="list-group-item-actions">
	<!-- Download SVG icon from http://tabler.io/icons/icon/star -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-muted icon-2"><path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z" /></svg>
					</a>
				</div>
			</div>
		</div>
		<div class="list-group-item">
			<div class="row align-items-center">
				<div class="col-auto"><span class="status-dot d-block"></span></div>
				<div class="col text-truncate">
					<a href="#" class="text-body d-block">Example 2</a>
					<div class="d-block text-secondary text-truncate mt-n1">
						justify-content:between ⇒ justify-content:space-between (#29734)
					</div>
				</div>
				<div class="col-auto">
					<a href="#" class="list-group-item-actions show">
	<!-- Download SVG icon from http://tabler.io/icons/icon/star -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-yellow icon-2"><path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z" /></svg>
					</a>
				</div>
			</div>
		</div>
		<div class="list-group-item">
			<div class="row align-items-center">
				<div class="col-auto"><span class="status-dot d-block"></span></div>
				<div class="col text-truncate">
					<a href="#" class="text-body d-block">Example 3</a>
					<div class="d-block text-secondary text-truncate mt-n1">
						Update change-version.js (#29736)
					</div>
				</div>
				<div class="col-auto">
					<a href="#" class="list-group-item-actions">
	<!-- Download SVG icon from http://tabler.io/icons/icon/star -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-muted icon-2"><path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z" /></svg>
					</a>
				</div>
			</div>
		</div>
		<div class="list-group-item">
			<div class="row align-items-center">
				<div class="col-auto"><span class="status-dot status-dot-animated bg-green d-block"></span></div>
				<div class="col text-truncate">
					<a href="#" class="text-body d-block">Example 4</a>
					<div class="d-block text-secondary text-truncate mt-n1">
						Regenerate package-lock.json (#29730)
					</div>
				</div>
				<div class="col-auto">
					<a href="#" class="list-group-item-actions">
	<!-- Download SVG icon from http://tabler.io/icons/icon/star -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-muted icon-2"><path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z" /></svg>
					</a>
				</div>
			</div>
		</div>
	</div>
</div>
			</div>
		</div>
	</div>
	<div class="nav-item dropdown">
		<a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/000m.jpg)"></span>
			<div class="d-none d-xl-block ps-2">
				<div>Paweł Kuna</div>
				<div class="mt-1 small text-secondary">UI Designer</div>
			</div>
		</a>
		<div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
			<a href="#" class="dropdown-item">Status</a>
			<a href="./profile.html" class="dropdown-item">Profile</a>
			<a href="#" class="dropdown-item">Feedback</a>
			<div class="dropdown-divider"></div>
			<a href="./settings.html" class="dropdown-item">Settings</a>
			<a href="./sign-in.html" class="dropdown-item">Logout</a>
		</div>
	</div>
</div>
		</div>
	</header>
	<header class="navbar-expand-md">
		<div class="collapse navbar-collapse" id="navbar-menu">
			<div class="navbar">
				<div class="container-xl">
					<div class="row flex-fill align-items-center">
						<div class="col">
<ul class="navbar-nav">
		<li class="nav-item active">
			<a class="nav-link" href="./" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/home -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>
</span>
				<span class="nav-link-title">
					Home
				</span>
			</a>
		</li>
		<li class="nav-item dropdown">
			<a class="nav-link dropdown-toggle" href="#navbar-base" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/package -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M12 3l8 4.5l0 9l-8 4.5l-8 -4.5l0 -9l8 -4.5" /><path d="M12 12l8 -4.5" /><path d="M12 12l0 9" /><path d="M12 12l-8 -4.5" /><path d="M16 5.25l-8 4.5" /></svg>
</span>
				<span class="nav-link-title">
					Interface
				</span>
			</a>
			<div class="dropdown-menu">
					<div class="dropdown-menu-columns">
					<div class="dropdown-menu-column">
					<a class="dropdown-item" href="./accordion.html">
						Accordion
					</a>
					<a class="dropdown-item" href="./alerts.html">
						Alerts
					</a>
					<div class="dropend">
					<a class="dropdown-item dropdown-toggle" href="#sidebar-authentication" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false" >
						Authentication
					</a>
					<div class="dropdown-menu">
						<a href="./sign-in.html" class="dropdown-item">
							Sign in
						</a>
						<a href="./sign-in-link.html" class="dropdown-item">
							Sign in link
						</a>
						<a href="./sign-in-illustration.html" class="dropdown-item">
							Sign in with illustration
						</a>
						<a href="./sign-in-cover.html" class="dropdown-item">
							Sign in with cover
						</a>
						<a href="./sign-up.html" class="dropdown-item">
							Sign up
						</a>
						<a href="./forgot-password.html" class="dropdown-item">
							Forgot password
						</a>
						<a href="./terms-of-service.html" class="dropdown-item">
							Terms of service
						</a>
						<a href="./auth-lock.html" class="dropdown-item">
							Lock screen
						</a>
						<a href="./2-step-verification.html" class="dropdown-item">
							2 step verification
						</a>
						<a href="./2-step-verification-code.html" class="dropdown-item">
							2 step verification code
						</a>
					</div>
					</div>
					<a class="dropdown-item" href="./badges.html">
						Badges
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./blank.html">
						Blank page
					</a>
					<a class="dropdown-item" href="./buttons.html">
						Buttons
					</a>
					<div class="dropend">
					<a class="dropdown-item dropdown-toggle" href="#sidebar-cards" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false" >
						Cards
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<div class="dropdown-menu">
						<a href="./cards.html" class="dropdown-item">
							Sample cards
						</a>
						<a href="./card-actions.html" class="dropdown-item">
							Card actions
							<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
						</a>
						<a href="./cards-masonry.html" class="dropdown-item">
							Cards Masonry
						</a>
					</div>
					</div>
					<a class="dropdown-item" href="./carousel.html">
						Carousel
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./charts.html">
						Charts
					</a>
					<a class="dropdown-item" href="./colorpicker.html">
						Color picker
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./colors.html">
						Colors
					</a>
					<a class="dropdown-item" href="./datagrid.html">
						Data grid
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./datatables.html">
						Datatables
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./dropdowns.html">
						Dropdowns
					</a>
					<a class="dropdown-item" href="./dropzone.html">
						Dropzone
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<div class="dropend">
					<a class="dropdown-item dropdown-toggle" href="#sidebar-error" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false" >
						Error pages
					</a>
					<div class="dropdown-menu">
						<a href="./error-404.html" class="dropdown-item">
							404 page
						</a>
						<a href="./error-500.html" class="dropdown-item">
							500 page
						</a>
						<a href="./error-maintenance.html" class="dropdown-item">
							Maintenance page
						</a>
					</div>
					</div>
					<a class="dropdown-item" href="./flags.html">
						Flags
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./inline-player.html">
						Inline player
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./lightbox.html">
						Lightbox
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
						</div><div class="dropdown-menu-column">
					<a class="dropdown-item" href="./lists.html">
						Lists
					</a>
					<a class="dropdown-item" href="./modals.html">
						Modal
					</a>
					<a class="dropdown-item" href="./maps.html">
						Map
					</a>
					<a class="dropdown-item" href="./map-fullsize.html">
						Map fullsize
					</a>
					<a class="dropdown-item" href="./maps-vector.html">
						Map vector
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./markdown.html">
						Markdown
					</a>
					<a class="dropdown-item" href="./navigation.html">
						Navigation
					</a>
					<a class="dropdown-item" href="./offcanvas.html">
						Offcanvas
					</a>
					<a class="dropdown-item" href="./pagination.html">
						Pagination
					</a>
					<a class="dropdown-item" href="./placeholder.html">
						Placeholder
					</a>
					<a class="dropdown-item" href="./social-icons.html">
						Social icons
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./stars-rating.html">
						Stars rating
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./steps.html">
						Steps
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./tables.html">
						Tables
					</a>
					<a class="dropdown-item" href="./tabs.html">
						Tabs
					</a>
					<a class="dropdown-item" href="./tags.html">
						Tags
					</a>
					<a class="dropdown-item" href="./tinymce.html">
						TinyMCE
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./toasts.html">
						Toasts
					</a>
					<a class="dropdown-item" href="./typography.html">
						Typography
					</a>
				</div></div>
				</div>
		</li>
		<li class="nav-item">
			<a class="nav-link" href="./form-elements.html" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/checkbox -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 11l3 3l8 -8" /><path d="M20 12v6a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2h9" /></svg>
</span>
				<span class="nav-link-title">
					Form elements
				</span>
			</a>
		</li>
		<li class="nav-item dropdown">
			<a class="nav-link dropdown-toggle" href="#navbar-extra" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/star -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M12 17.75l-6.172 3.245l1.179 -6.873l-5 -4.867l6.9 -1l3.086 -6.253l3.086 6.253l6.9 1l-5 4.867l1.179 6.873z" /></svg>
</span>
				<span class="nav-link-title">
					Extra
				</span>
			</a>
			<div class="dropdown-menu">
					<div class="dropdown-menu-columns">
					<div class="dropdown-menu-column">
					<a class="dropdown-item" href="./activity.html">
						Activity
					</a>
					<a class="dropdown-item" href="./chat.html">
						Chat
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./cookie-banner.html">
						Cookie banner
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./empty.html">
						Empty page
					</a>
					<a class="dropdown-item" href="./faq.html">
						FAQ
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./gallery.html">
						Gallery
					</a>
					<a class="dropdown-item" href="./invoice.html">
						Invoice
					</a>
					<a class="dropdown-item" href="./job-listing.html">
						Job listing
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./license.html">
						License
					</a>
					<a class="dropdown-item" href="./logs.html">
						Logs
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./marketing/index.html">
						Marketing
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./music.html">
						Music
					</a>
					<a class="dropdown-item" href="./page-loader.html">
						Page loader
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
						</div><div class="dropdown-menu-column">
					<a class="dropdown-item" href="./payment-providers.html">
						Payment providers
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./photogrid.html">
						Photogrid
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./pricing.html">
						Pricing cards
					</a>
					<a class="dropdown-item" href="./pricing-table.html">
						Pricing table
					</a>
					<a class="dropdown-item" href="./search-results.html">
						Search results
					</a>
					<a class="dropdown-item" href="./settings.html">
						Settings
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./tasks.html">
						Tasks
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./trial-ended.html">
						Trial ended
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./uptime.html">
						Uptime monitor
					</a>
					<a class="dropdown-item" href="./users.html">
						Users
					</a>
					<a class="dropdown-item" href="./widgets.html">
						Widgets
					</a>
					<a class="dropdown-item" href="./wizard.html">
						Wizard
					</a>
				</div></div>
				</div>
		</li>
		<li class="nav-item dropdown">
			<a class="nav-link dropdown-toggle" href="#navbar-layout" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/layout-2 -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
</span>
				<span class="nav-link-title">
					Layout
				</span>
			</a>
			<div class="dropdown-menu">
					<div class="dropdown-menu-columns">
					<div class="dropdown-menu-column">
					<a class="dropdown-item" href="./layout-boxed.html">
						Boxed
						<span class="badge badge-sm bg-green-lt text-uppercase ms-auto">New</span>
					</a>
					<a class="dropdown-item" href="./layout-combo.html">
						Combined
					</a>
					<a class="dropdown-item" href="./layout-condensed.html">
						Condensed
					</a>
					<a class="dropdown-item" href="./layout-fluid.html">
						Fluid
					</a>
					<a class="dropdown-item" href="./layout-fluid-vertical.html">
						Fluid vertical
					</a>
					<a class="dropdown-item" href="./layout-horizontal.html">
						Horizontal
					</a>
					<a class="dropdown-item" href="./layout-navbar-dark.html">
						Navbar dark
					</a>
						</div><div class="dropdown-menu-column">
					<a class="dropdown-item" href="./layout-navbar-overlap.html">
						Navbar overlap
					</a>
					<a class="dropdown-item" href="./layout-navbar-sticky.html">
						Navbar sticky
					</a>
					<a class="dropdown-item" href="./layout-vertical-right.html">
						Right vertical
					</a>
					<a class="dropdown-item" href="./layout-rtl.html">
						RTL mode
					</a>
					<a class="dropdown-item" href="./layout-vertical.html">
						Vertical
					</a>
					<a class="dropdown-item" href="./layout-vertical-transparent.html">
						Vertical transparent
					</a>
				</div></div>
				</div>
		</li>
		<li class="nav-item">
			<a class="nav-link" href="./icons.html" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/ghost -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 11a7 7 0 0 1 14 0v7a1.78 1.78 0 0 1 -3.1 1.4a1.65 1.65 0 0 0 -2.6 0a1.65 1.65 0 0 1 -2.6 0a1.65 1.65 0 0 0 -2.6 0a1.78 1.78 0 0 1 -3.1 -1.4v-7" /><path d="M10 10l.01 0" /><path d="M14 10l.01 0" /><path d="M10 14a3.5 3.5 0 0 0 4 0" /></svg>
</span>
				<span class="nav-link-title">
					5844 icons
				</span>
			</a>
		</li>
		<li class="nav-item">
			<a class="nav-link" href="./emails.html" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/mail-opened -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M3 9l9 6l9 -6l-9 -6l-9 6" /><path d="M21 9v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10" /><path d="M3 19l6 -6" /><path d="M15 13l6 6" /></svg>
</span>
				<span class="nav-link-title">
					Emails
				</span>
			</a>
		</li>
		<li class="nav-item">
			<a class="nav-link" href="./illustrations.html" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/brand-figma -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M15 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" /><path d="M6 3m0 3a3 3 0 0 1 3 -3h6a3 3 0 0 1 3 3v0a3 3 0 0 1 -3 3h-6a3 3 0 0 1 -3 -3z" /><path d="M9 9a3 3 0 0 0 0 6h3m-3 0a3 3 0 1 0 3 3v-15" /></svg>
</span>
				<span class="nav-link-title">
					Illustrations
				</span>
			</a>
		</li>
		<li class="nav-item dropdown">
			<a class="nav-link dropdown-toggle" href="#navbar-help" data-bs-toggle="dropdown" data-bs-auto-close="outside" role="button" aria-expanded="false" >
				<span class="nav-link-icon d-md-none d-lg-inline-block">
	<!-- Download SVG icon from http://tabler.io/icons/icon/lifebuoy -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" /><path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0" /><path d="M15 15l3.35 3.35" /><path d="M9 15l-3.35 3.35" /><path d="M5.65 5.65l3.35 3.35" /><path d="M18.35 5.65l-3.35 3.35" /></svg>
</span>
				<span class="nav-link-title">
					Help
				</span>
			</a>
			<div class="dropdown-menu">
					<a class="dropdown-item" href="https://tabler.io/docs" target="_blank" rel="noopener">
						Documentation
					</a>
					<a class="dropdown-item" href="./changelog.html">
						Changelog
					</a>
					<a class="dropdown-item" href="https://github.com/tabler/tabler" target="_blank" rel="noopener">
						Source code
					</a>
					<a class="dropdown-item text-pink" href="https://github.com/sponsors/codecalm" target="_blank" rel="noopener">
	<!-- Download SVG icon from http://tabler.io/icons/icon/heart -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-inline me-1 icon-2"><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
						Sponsor project!
					</a>
				</div>
		</li>
</ul>
						</div>
						<div class="col-2 d-none d-xxl-block">
<div class="my-2 my-md-0 flex-grow-1 flex-md-grow-0 order-first order-md-last">
	<form action="./" method="get" autocomplete="off" novalidate>
<div class="input-icon">
		<span class="input-icon-addon">
	<!-- Download SVG icon from http://tabler.io/icons/icon/search -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0" /><path d="M21 21l-6 -6" /></svg>
		</span>
	<input type="text" value="" class="form-control" placeholder="Search…" aria-label="Search in website">
</div>
	</form>
</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</header>
	<div class="page-wrapper">
<!-- Page header -->
<div class="page-header d-print-none">
	<div class="container-xl">
		<div class="row g-2 align-items-center">
			<div class="col">
				<!-- Page pre-title -->
				<div class="page-pretitle">
					Overview
				</div>
				<h2 class="page-title">
					Dashboard
				</h2>
			</div>
				<!-- Page title actions -->
				<div class="col-auto ms-auto d-print-none">
					<div class="btn-list">
	<span class="d-none d-sm-inline">
<a href="#" class="btn btn-1">
New view
</a>
	</span>
<a href="#" class="btn btn-primary btn-5 d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#modal-report">
	<!-- Download SVG icon from http://tabler.io/icons/icon/plus -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2"><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
Create new report
</a>
<a href="#" class="btn btn-primary btn-6 d-sm-none btn-icon" data-bs-toggle="modal" data-bs-target="#modal-report" aria-label="Create new report">
	<!-- Download SVG icon from http://tabler.io/icons/icon/plus -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2"><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
</a>
</div>
<div class="modal modal-blur fade" id="modal-report" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
	<h5 class="modal-title">New report</h5>
	<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>
<div class="modal-body">
	<div class="mb-3">
		<label class="form-label">Name</label>
		<input type="text" class="form-control" name="example-text-input" placeholder="Your report name">
	</div>
	<label class="form-label">Report type</label>
	<div class="form-selectgroup-boxes row mb-3">
		<div class="col-lg-6">
			<label class="form-selectgroup-item">
				<input type="radio" name="report-type" value="1" class="form-selectgroup-input" checked>
				<span class="form-selectgroup-label d-flex align-items-center p-3">
					<span class="me-3">
						<span class="form-selectgroup-check"></span>
					</span>
					<span class="form-selectgroup-label-content">
						<span class="form-selectgroup-title strong mb-1">Simple</span>
						<span class="d-block text-secondary">Provide only basic data needed for the report</span>
					</span>
				</span>
			</label>
		</div>
		<div class="col-lg-6">
			<label class="form-selectgroup-item">
				<input type="radio" name="report-type" value="1" class="form-selectgroup-input">
				<span class="form-selectgroup-label d-flex align-items-center p-3">
					<span class="me-3">
						<span class="form-selectgroup-check"></span>
					</span>
					<span class="form-selectgroup-label-content">
						<span class="form-selectgroup-title strong mb-1">Advanced</span>
						<span class="d-block text-secondary">Insert charts and additional advanced analyses to be inserted in the report</span>
					</span>
				</span>
			</label>
		</div>
	</div>
	<div class="row">
		<div class="col-lg-8">
			<div class="mb-3">
				<label class="form-label">Report url</label>
				<div class="input-group input-group-flat">
	<span class="input-group-text">
			https://tabler.io/reports/
	</span>
	<input type="text" class="form-control ps-0"  value="report-01" autocomplete="off">
</div>
			</div>
		</div>
		<div class="col-lg-4">
			<div class="mb-3">
				<label class="form-label">Visibility</label>
				<select class="form-select">
					<option value="1" selected>Private</option>
					<option value="2">Public</option>
					<option value="3">Hidden</option>
				</select>
			</div>
		</div>
	</div>
</div>
<div class="modal-body">
	<div class="row">
		<div class="col-lg-6">
			<div class="mb-3">
				<label class="form-label">Client name</label>
				<input type="text" class="form-control">
			</div>
		</div>
		<div class="col-lg-6">
			<div class="mb-3">
				<label class="form-label">Reporting period</label>
				<input type="date" class="form-control">
			</div>
		</div>
		<div class="col-lg-12">
			<div>
				<label class="form-label">Additional information</label>
				<textarea class="form-control" rows="3"></textarea>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
<a href="#" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal">
Cancel
</a>
<a href="#" class="btn btn-primary btn-5 ms-auto" data-bs-dismiss="modal">
	<!-- Download SVG icon from http://tabler.io/icons/icon/plus -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-2"><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
Create new report
</a>
</div>
		</div>
	</div>
</div>
				</div>
		</div>
	</div>
</div>
		<!-- Page body -->
		<div class="page-body">
			<div class="container-xl">
<div class="row row-deck row-cards">
	<div class="col-sm-6 col-lg-3">
		<div class="card">
	<div class="card-body">
		<div class="d-flex align-items-center">
			<div class="subheader">Sales</div>
			<div class="ms-auto lh-1">
				<div class="dropdown">
   <a class="dropdown-toggle text-secondary" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Last 7 days</a>
   <div class="dropdown-menu dropdown-menu-end">
      <a class="dropdown-item active" href="#">Last 7 days</a>
      <a class="dropdown-item" href="#">Last 30 days</a>
      <a class="dropdown-item" href="#">Last 3 months</a>
   </div>
</div>
			</div>
		</div>
		<div class="h1 mb-3">75%</div>
		<div class="d-flex mb-2">
			<div>Conversion rate</div>
			<div class="ms-auto">
<span class="text-green d-inline-flex align-items-center lh-1">
   7% 
	<!-- Download SVG icon from http://tabler.io/icons/icon/trending-up -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon ms-1 icon-2"><path d="M3 17l6 -6l4 4l8 -8" /><path d="M14 7l7 0l0 7" /></svg>
</span>
</div>
		</div>
<div class="progress progress-sm">
      <div class="progress-bar bg-primary" style="width: 75%" role="progressbar" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" aria-label="75% Complete">
            <span class="visually-hidden">75% Complete</span>
      </div>
</div>
	</div>
</div>
	</div>
	<div class="col-sm-6 col-lg-3">
		<div class="card">
	<div class="card-body">
		<div class="d-flex align-items-center">
			<div class="subheader">Revenue</div>
			<div class="ms-auto lh-1">
				<div class="dropdown">
   <a class="dropdown-toggle text-secondary" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Last 7 days</a>
   <div class="dropdown-menu dropdown-menu-end">
      <a class="dropdown-item active" href="#">Last 7 days</a>
      <a class="dropdown-item" href="#">Last 30 days</a>
      <a class="dropdown-item" href="#">Last 3 months</a>
   </div>
</div>
			</div>
		</div>
		<div class="d-flex align-items-baseline">
			<div class="h1 mb-0 me-2">$4,300</div>
			<div class="me-auto">
<span class="text-green d-inline-flex align-items-center lh-1">
   8% 
	<!-- Download SVG icon from http://tabler.io/icons/icon/trending-up -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon ms-1 icon-2"><path d="M3 17l6 -6l4 4l8 -8" /><path d="M14 7l7 0l0 7" /></svg>
</span>
			</div>
		</div>
	</div>
<div id="chart-revenue-bg" class="chart-sm"></div>
<script>
		document.addEventListener("DOMContentLoaded", function () {
		window.ApexCharts && (new ApexCharts(document.getElementById('chart-revenue-bg'), {
			chart: {
				type: "area",
				fontFamily: 'inherit',
				height: 40,
				sparkline: {
					enabled: true
				},
				animations: {
					enabled: false
				},
			},
			dataLabels: {
				enabled: false,
			},
			fill: {
				opacity: .16,
				type: 'solid'
			},
			stroke: {
				width: 2,
				lineCap: "round",
				curve: "smooth",
			},
			series: [{
				name: "Profits",
				data: [37, 35, 44, 28, 36, 24, 65, 31, 37, 39, 62, 51, 35, 41, 35, 27, 93, 53, 61, 27, 54, 43, 19, 46, 39, 62, 51, 35, 41, 67]
			}],
			tooltip: {
				theme: 'dark'
			},
			grid: {
				strokeDashArray: 4,
			},
			xaxis: {
				labels: {
					padding: 0,
				},
				tooltip: {
					enabled: false
				},
				axisBorder: {
					show: false,
				},
				type: 'datetime',
			},
			yaxis: {
				labels: {
					padding: 4
				},
			},
			labels: [
				'2020-06-20', '2020-06-21', '2020-06-22', '2020-06-23', '2020-06-24', '2020-06-25', '2020-06-26', '2020-06-27', '2020-06-28', '2020-06-29', '2020-06-30', '2020-07-01', '2020-07-02', '2020-07-03', '2020-07-04', '2020-07-05', '2020-07-06', '2020-07-07', '2020-07-08', '2020-07-09', '2020-07-10', '2020-07-11', '2020-07-12', '2020-07-13', '2020-07-14', '2020-07-15', '2020-07-16', '2020-07-17', '2020-07-18', '2020-07-19'
			],
			colors: [tabler.getColor("primary")],
			legend: {
				show: false,
			},
		})).render();
	});
	</script>
</div>
	</div>
	<div class="col-sm-6 col-lg-3">
		<div class="card">
	<div class="card-body">
		<div class="d-flex align-items-center">
			<div class="subheader">New clients</div>
			<div class="ms-auto lh-1">
				<div class="dropdown">
   <a class="dropdown-toggle text-secondary" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Last 7 days</a>
   <div class="dropdown-menu dropdown-menu-end">
      <a class="dropdown-item active" href="#">Last 7 days</a>
      <a class="dropdown-item" href="#">Last 30 days</a>
      <a class="dropdown-item" href="#">Last 3 months</a>
   </div>
</div>
			</div>
		</div>
		<div class="d-flex align-items-baseline">
			<div class="h1 mb-3 me-2">6,782</div>
			<div class="me-auto">
<span class="text-yellow d-inline-flex align-items-center lh-1">
   0% 
	<!-- Download SVG icon from http://tabler.io/icons/icon/minus -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon ms-1 icon-2"><path d="M5 12l14 0" /></svg>
</span>
			</div>
		</div>
<div id="chart-new-clients" class="chart-sm"></div>
<script>
		document.addEventListener("DOMContentLoaded", function () {
		window.ApexCharts && (new ApexCharts(document.getElementById('chart-new-clients'), {
			chart: {
				type: "line",
				fontFamily: 'inherit',
				height: 40,
				sparkline: {
					enabled: true
				},
				animations: {
					enabled: false
				},
			},
			fill: {
				opacity: 1,
			},
			stroke: {
				width: [2, 1],
				dashArray: [0, 3],
				lineCap: "round",
				curve: "smooth",
			},
			series: [{
				name: "May",
				data: [37, 35, 44, 28, 36, 24, 65, 31, 37, 39, 62, 51, 35, 41, 35, 27, 93, 53, 61, 27, 54, 43, 4, 46, 39, 62, 51, 35, 41, 67]
			},{
				name: "April",
				data: [93, 54, 51, 24, 35, 35, 31, 67, 19, 43, 28, 36, 62, 61, 27, 39, 35, 41, 27, 35, 51, 46, 62, 37, 44, 53, 41, 65, 39, 37]
			}],
			tooltip: {
				theme: 'dark'
			},
			grid: {
				strokeDashArray: 4,
			},
			xaxis: {
				labels: {
					padding: 0,
				},
				tooltip: {
					enabled: false
				},
				type: 'datetime',
			},
			yaxis: {
				labels: {
					padding: 4
				},
			},
			labels: [
				'2020-06-20', '2020-06-21', '2020-06-22', '2020-06-23', '2020-06-24', '2020-06-25', '2020-06-26', '2020-06-27', '2020-06-28', '2020-06-29', '2020-06-30', '2020-07-01', '2020-07-02', '2020-07-03', '2020-07-04', '2020-07-05', '2020-07-06', '2020-07-07', '2020-07-08', '2020-07-09', '2020-07-10', '2020-07-11', '2020-07-12', '2020-07-13', '2020-07-14', '2020-07-15', '2020-07-16', '2020-07-17', '2020-07-18', '2020-07-19'
			],
			colors: [tabler.getColor("primary"), tabler.getColor("gray-600")],
			legend: {
				show: false,
			},
		})).render();
	});
	</script>
	</div>
</div>
	</div>
	<div class="col-sm-6 col-lg-3">
	  <div class="card">
	<div class="card-body">
		<div class="d-flex align-items-center">
			<div class="subheader">Active users</div>
			<div class="ms-auto lh-1">
				<div class="dropdown">
   <a class="dropdown-toggle text-secondary" href="#" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Last 7 days</a>
   <div class="dropdown-menu dropdown-menu-end">
      <a class="dropdown-item active" href="#">Last 7 days</a>
      <a class="dropdown-item" href="#">Last 30 days</a>
      <a class="dropdown-item" href="#">Last 3 months</a>
   </div>
</div>
			</div>
		</div>
		<div class="d-flex align-items-baseline">
			<div class="h1 mb-3 me-2">2,986</div>
			<div class="me-auto">
<span class="text-green d-inline-flex align-items-center lh-1">
   4% 
	<!-- Download SVG icon from http://tabler.io/icons/icon/trending-up -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon ms-1 icon-2"><path d="M3 17l6 -6l4 4l8 -8" /><path d="M14 7l7 0l0 7" /></svg>
</span>
			</div>
		</div>
<div id="chart-active-users" class="chart-sm"></div>
<script>
		document.addEventListener("DOMContentLoaded", function () {
		window.ApexCharts && (new ApexCharts(document.getElementById('chart-active-users'), {
			chart: {
				type: "bar",
				fontFamily: 'inherit',
				height: 40,
				sparkline: {
					enabled: true
				},
				animations: {
					enabled: false
				},
			},
			plotOptions: {
				bar: {
					columnWidth: '50%',
				}
			},
			dataLabels: {
				enabled: false,
			},
			fill: {
				opacity: 1,
			},
			series: [{
				name: "Profits",
				data: [37, 35, 44, 28, 36, 24, 65, 31, 37, 39, 62, 51, 35, 41, 35, 27, 93, 53, 61, 27, 54, 43, 19, 46, 39, 62, 51, 35, 41, 67]
			}],
			tooltip: {
				theme: 'dark'
			},
			grid: {
				strokeDashArray: 4,
			},
			xaxis: {
				labels: {
					padding: 0,
				},
				tooltip: {
					enabled: false
				},
				axisBorder: {
					show: false,
				},
				type: 'datetime',
			},
			yaxis: {
				labels: {
					padding: 4
				},
			},
			labels: [
				'2020-06-20', '2020-06-21', '2020-06-22', '2020-06-23', '2020-06-24', '2020-06-25', '2020-06-26', '2020-06-27', '2020-06-28', '2020-06-29', '2020-06-30', '2020-07-01', '2020-07-02', '2020-07-03', '2020-07-04', '2020-07-05', '2020-07-06', '2020-07-07', '2020-07-08', '2020-07-09', '2020-07-10', '2020-07-11', '2020-07-12', '2020-07-13', '2020-07-14', '2020-07-15', '2020-07-16', '2020-07-17', '2020-07-18', '2020-07-19'
			],
			colors: [tabler.getColor("primary")],
			legend: {
				show: false,
			},
		})).render();
	});
	</script>
	</div>
</div>
	</div>
	<div class="col-12">
		<div class="row row-cards">
			<div class="col-sm-6 col-lg-3">
<div class="card card-sm">
	<div class="card-body">
		<div class="row align-items-center">
				<div class="col-auto">
					<span class="bg-primary text-white avatar">
	<!-- Download SVG icon from http://tabler.io/icons/icon/currency-dollar -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M16.7 8a3 3 0 0 0 -2.7 -2h-4a3 3 0 0 0 0 6h4a3 3 0 0 1 0 6h-4a3 3 0 0 1 -2.7 -2" /><path d="M12 3v3m0 12v3" /></svg>
</span>
				</div>
			<div class="col">
				<div class="font-weight-medium">
					132 Sales
				</div>
				<div class="text-secondary">
					12 waiting payments
				</div>
			</div>
		</div>
	</div>
</div>
			</div>
			<div class="col-sm-6 col-lg-3">
<div class="card card-sm">
	<div class="card-body">
		<div class="row align-items-center">
				<div class="col-auto">
					<span class="bg-green text-white avatar">
	<!-- Download SVG icon from http://tabler.io/icons/icon/shopping-cart -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M6 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M17 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M17 17h-11v-14h-2" /><path d="M6 5l14 1l-1 7h-13" /></svg>
</span>
				</div>
			<div class="col">
				<div class="font-weight-medium">
					78 Orders
				</div>
				<div class="text-secondary">
					32 shipped
				</div>
			</div>
		</div>
	</div>
</div>
			</div>
			<div class="col-sm-6 col-lg-3">
<div class="card card-sm">
	<div class="card-body">
		<div class="row align-items-center">
				<div class="col-auto">
					<span class="bg-x text-white avatar">
	<!-- Download SVG icon from http://tabler.io/icons/icon/brand-x -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 4l11.733 16h4.267l-11.733 -16z" /><path d="M4 20l6.768 -6.768m2.46 -2.46l6.772 -6.772" /></svg>
</span>
				</div>
			<div class="col">
				<div class="font-weight-medium">
					623 Shares
				</div>
				<div class="text-secondary">
					16 today
				</div>
			</div>
		</div>
	</div>
</div>
			</div>
			<div class="col-sm-6 col-lg-3">
<div class="card card-sm">
	<div class="card-body">
		<div class="row align-items-center">
				<div class="col-auto">
					<span class="bg-facebook text-white avatar">
	<!-- Download SVG icon from http://tabler.io/icons/icon/brand-facebook -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M7 10v4h3v7h4v-7h3l1 -4h-4v-2a1 1 0 0 1 1 -1h3v-4h-3a5 5 0 0 0 -5 5v2h-3" /></svg>
</span>
				</div>
			<div class="col">
				<div class="font-weight-medium">
					132 Likes
				</div>
				<div class="text-secondary">
					21 today
				</div>
			</div>
		</div>
	</div>
</div>
			</div>
		</div>
	</div>
	<div class="col-lg-6">
		<div class="card">
			<div class="card-body">
				<h3 class="card-title">Traffic summary</h3>
<div id="chart-mentions" class="chart-lg"></div>
<script>
		document.addEventListener("DOMContentLoaded", function () {
		window.ApexCharts && (new ApexCharts(document.getElementById('chart-mentions'), {
			chart: {
				type: "bar",
				fontFamily: 'inherit',
				height: 240,
				parentHeightOffset: 0,
				toolbar: {
					show: false,
				},
				animations: {
					enabled: false
				},
				stacked: true,
			},
			plotOptions: {
				bar: {
					columnWidth: '50%',
				}
			},
			dataLabels: {
				enabled: false,
			},
			fill: {
				opacity: 1,
			},
			series: [{
				name: "Web",
				data: [1, 0, 0, 0, 0, 1, 1, 0, 0, 0, 2, 12, 5, 8, 22, 6, 8, 6, 4, 1, 8, 24, 29, 51, 40, 47, 23, 26, 50, 26, 41, 22, 46, 47, 81, 46, 6]
			},{
				name: "Social",
				data: [2, 5, 4, 3, 3, 1, 4, 7, 5, 1, 2, 5, 3, 2, 6, 7, 7, 1, 5, 5, 2, 12, 4, 6, 18, 3, 5, 2, 13, 15, 20, 47, 18, 15, 11, 10, 0]
			},{
				name: "Other",
				data: [2, 9, 1, 7, 8, 3, 6, 5, 5, 4, 6, 4, 1, 9, 3, 6, 7, 5, 2, 8, 4, 9, 1, 2, 6, 7, 5, 1, 8, 3, 2, 3, 4, 9, 7, 1, 6]
			}],
			tooltip: {
				theme: 'dark'
			},
			grid: {
				padding: {
					top: -20,
					right: 0,
					left: -4,
					bottom: -4
				},
				strokeDashArray: 4,
				xaxis: {
					lines: {
						show: true
					}
				},
			},
			xaxis: {
				labels: {
					padding: 0,
				},
				tooltip: {
					enabled: false
				},
				axisBorder: {
					show: false,
				},
				type: 'datetime',
			},
			yaxis: {
				labels: {
					padding: 4
				},
			},
			labels: [
				'2020-06-20', '2020-06-21', '2020-06-22', '2020-06-23', '2020-06-24', '2020-06-25', '2020-06-26', '2020-06-27', '2020-06-28', '2020-06-29', '2020-06-30', '2020-07-01', '2020-07-02', '2020-07-03', '2020-07-04', '2020-07-05', '2020-07-06', '2020-07-07', '2020-07-08', '2020-07-09', '2020-07-10', '2020-07-11', '2020-07-12', '2020-07-13', '2020-07-14', '2020-07-15', '2020-07-16', '2020-07-17', '2020-07-18', '2020-07-19', '2020-07-20', '2020-07-21', '2020-07-22', '2020-07-23', '2020-07-24', '2020-07-25', '2020-07-26'
			],
			colors: [tabler.getColor("primary"), tabler.getColor("primary", 0.8), tabler.getColor("green", 0.8)],
			legend: {
				show: false,
			},
		})).render();
	});
	</script>
			</div>
		</div>
	</div>
	<div class="col-lg-6">
<div class="card">
	<div class="card-body">
			<h3 class="card-title">Locations</h3>
	<div class="ratio ratio-21x9">
		<div>
			<div id="map-world" class="w-100 h-100"></div>
		</div>
	</div>
	<script>
		document.addEventListener("DOMContentLoaded", function() {
			const map = new jsVectorMap({
				selector: '#map-world',
				map: 'world',
				backgroundColor: 'transparent',
				regionStyle: {
					initial: {
						fill: tabler.getColor('body-bg'),
						stroke: tabler.getColor('border-color'),
						strokeWidth: 2,
					}
				},
				zoomOnScroll: false,
				zoomButtons: false,
				// -------- Series --------
				visualizeData: {
					scale: [tabler.getColor('bg-surface'), tabler.getColor('primary')],
					values: { "AF": 16, "AL": 11, "DZ": 158, "AO": 85, "AG": 1, "AR": 351, "AM": 8, "AU": 1219, "AT": 366, "AZ": 52, "BS": 7, "BH": 21, "BD": 105, "BB": 3, "BY": 52, "BE": 461, "BZ": 1, "BJ": 6, "BT": 1, "BO": 19, "BA": 16, "BW": 12, "BR": 2023, "BN": 11, "BG": 44, "BF": 8, "BI": 1, "KH": 11, "CM": 21, "CA": 1563, "CV": 1, "CF": 2, "TD": 7, "CL": 199, "CN": 5745, "CO": 283, "KM": 0, "CD": 12, "CG": 11, "CR": 35, "CI": 22, "HR": 59, "CY": 22, "CZ": 195, "DK": 304, "DJ": 1, "DM": 0, "DO": 50, "EC": 61, "EG": 216, "SV": 21, "GQ": 14, "ER": 2, "EE": 19, "ET": 30, "FJ": 3, "FI": 231, "FR": 2555, "GA": 12, "GM": 1, "GE": 11, "DE": 3305, "GH": 18, "GR": 305, "GD": 0, "GT": 40, "GN": 4, "GW": 0, "GY": 2, "HT": 6, "HN": 15, "HK": 226, "HU": 132, "IS": 12, "IN": 1430, "ID": 695, "IR": 337, "IQ": 84, "IE": 204, "IL": 201, "IT": 2036, "JM": 13, "JP": 5390, "JO": 27, "KZ": 129, "KE": 32, "KI": 0, "KR": 986, "KW": 117, "KG": 4, "LA": 6, "LV": 23, "LB": 39, "LS": 1, "LR": 0, "LY": 77, "LT": 35, "LU": 52, "MK": 9, "MG": 8, "MW": 5, "MY": 218, "MV": 1, "ML": 9, "MT": 7, "MR": 3, "MU": 9, "MX": 1004, "MD": 5, "MN": 5, "ME": 3, "MA": 91, "MZ": 10, "MM": 35, "NA": 11, "NP": 15, "NL": 770, "NZ": 138, "NI": 6, "NE": 5, "NG": 206, "NO": 413, "OM": 53, "PK": 174, "PA": 27, "PG": 8, "PY": 17, "PE": 153, "PH": 189, "PL": 438, "PT": 223, "QA": 126, "RO": 158, "RU": 1476, "RW": 5, "WS": 0, "ST": 0, "SA": 434, "SN": 12, "RS": 38, "SC": 0, "SL": 1, "SG": 217, "SK": 86, "SI": 46, "SB": 0, "ZA": 354, "ES": 1374, "LK": 48, "KN": 0, "LC": 1, "VC": 0, "SD": 65, "SR": 3, "SZ": 3, "SE": 444, "CH": 522, "SY": 59, "TW": 426, "TJ": 5, "TZ": 22, "TH": 312, "TL": 0, "TG": 3, "TO": 0, "TT": 21, "TN": 43, "TR": 729, "TM": 0, "UG": 17, "UA": 136, "AE": 239, "GB": 2258, "US": 4624, "UY": 40, "UZ": 37, "VU": 0, "VE": 285, "VN": 101, "YE": 30, "ZM": 15, "ZW": 5 },
				},
			});
			window.addEventListener("resize", () => {
				map.updateSize();
			});
		});
			</script>
	</div>
</div>
	</div>
	<div class="col-lg-6">
		<div class="row row-cards">
			<div class="col-12">
				<div class="card">
					<div class="card-body">
						<p class="mb-3">Using Storage <strong>6854.45 MB </strong>of 8 GB</p>
						<div class="progress progress-separated mb-3">
							<div class="progress-bar bg-primary" role="progressbar" style="width: 44%" aria-label="Regular"></div>
							<div class="progress-bar bg-info" role="progressbar" style="width: 19%" aria-label="System"></div>
							<div class="progress-bar bg-success" role="progressbar" style="width: 9%" aria-label="Shared"></div>
						</div>
						<div class="row">
							<div class="col-auto d-flex align-items-center pe-2">
								<span class="legend me-2 bg-primary"></span>
								<span>Regular</span>
								<span class="d-none d-md-inline d-lg-none d-xxl-inline ms-2 text-secondary">915MB</span>
							</div>
							<div class="col-auto d-flex align-items-center px-2">
								<span class="legend me-2 bg-info"></span>
								<span>System</span>
								<span class="d-none d-md-inline d-lg-none d-xxl-inline ms-2 text-secondary">415MB</span>
							</div>
							<div class="col-auto d-flex align-items-center px-2">
								<span class="legend me-2 bg-success"></span>
								<span>Shared</span>
								<span class="d-none d-md-inline d-lg-none d-xxl-inline ms-2 text-secondary">201MB</span>
							</div>
							<div class="col-auto d-flex align-items-center ps-2">
								<span class="legend me-2"></span>
								<span>Free</span>
								<span class="d-none d-md-inline d-lg-none d-xxl-inline ms-2 text-secondary">612MB</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-12">
				<div class="card" style="height: 28rem">
	<div class="card-body card-body-scrollable card-body-scrollable-shadow">
<div class="divide-y">
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1">JL</span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Jeffie Lewzey</strong> commented on your <strong>"I'm not a witch."</strong> post.
					</div>
					<div class="text-secondary">24 hours ago</div>
				</div>
				<div class="col-auto align-self-center">
					<div class="badge bg-primary"></div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/002m.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						It's <strong>Mallory Hulme</strong>'s birthday. Wish him well!
					</div>
					<div class="text-secondary">now</div>
				</div>
				<div class="col-auto align-self-center">
					<div class="badge bg-primary"></div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/003m.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Dunn Slane</strong> posted <strong>"Well, what do you want?"</strong>.
					</div>
					<div class="text-secondary">now</div>
				</div>
				<div class="col-auto align-self-center">
					<div class="badge bg-primary"></div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/000f.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Emmy Levet</strong> created a new project <strong>Morning alarm clock</strong>.
					</div>
					<div class="text-secondary">4 days ago</div>
				</div>
				<div class="col-auto align-self-center">
					<div class="badge bg-primary"></div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/001f.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Maryjo Lebarree</strong> liked your photo.
					</div>
					<div class="text-secondary">now</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1">EP</span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Egan Poetz</strong> registered new client as <strong>Trilia</strong>.
					</div>
					<div class="text-secondary">24 hours ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/002f.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Kellie Skingley</strong> closed a new deal on project <strong>Pen Pineapple Apple Pen</strong>.
					</div>
					<div class="text-secondary">2 days ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/003f.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Christabel Charlwood</strong> created a new project for <strong>Wikibox</strong>.
					</div>
					<div class="text-secondary">4 days ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1">HS</span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Haskel Shelper</strong> change status of <strong>Tabler Icons</strong> from <strong>open</strong> to <strong>closed</strong>.
					</div>
					<div class="text-secondary">now</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/006m.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Lorry Mion</strong> liked <strong>Tabler UI Kit</strong>.
					</div>
					<div class="text-secondary">now</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/004f.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Leesa Beaty</strong> posted new video.
					</div>
					<div class="text-secondary">2 days ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/007m.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Perren Keemar</strong> and 3 others followed you.
					</div>
					<div class="text-secondary">now</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1">SA</span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Sunny Airey</strong> upload 3 new photos to category <strong>Inspirations</strong>.
					</div>
					<div class="text-secondary">2 days ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/009m.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Geoffry Flaunders</strong> made a <strong>$10</strong> donation.
					</div>
					<div class="text-secondary">2 days ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/010m.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Thatcher Keel</strong> created a profile.
					</div>
					<div class="text-secondary">3 days ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/005f.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Dyann Escala</strong> hosted the event <strong>Tabler UI Birthday</strong>.
					</div>
					<div class="text-secondary">4 days ago</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1" style="background-image: url(./static/static/avatars/006f.jpg)"></span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Avivah Mugleston</strong> mentioned you on <strong>Best of 2020</strong>.
					</div>
					<div class="text-secondary">now</div>
				</div>
			</div>
		</div>
		<div>
			<div class="row">
				<div class="col-auto">
<span class="avatar avatar-1">AA</span>
				</div>
				<div class="col">
					<div class="text-truncate">
						<strong>Arlie Armstead</strong> sent a Review Request to <strong>Amanda Blake</strong>.
					</div>
					<div class="text-secondary">2 days ago</div>
				</div>
			</div>
		</div>
</div>
	</div>
</div>
			</div>
		</div>
	</div>
	<div class="col-lg-6">
		<div class="card">
	<div class="card-header border-0">
		<div class="card-title">Development activity</div>
	</div>
	<div class="position-relative">
		<div class="position-absolute top-0 left-0 px-3 mt-1 w-75">
			<div class="row g-2">
				<div class="col-auto">
	<div class="chart-sparkline chart-sparkline-square" id="sparkline-activity"></div>
	<script>
				document.addEventListener("DOMContentLoaded", function () {
			window.ApexCharts && (new ApexCharts(document.getElementById('sparkline-activity'), {
				chart: {
					type: "radialBar",
					fontFamily: 'inherit',
					height: 40,
					width: 40,
					animations: {
						enabled: false
					},
					sparkline: {
						enabled: true
					},
				},
				tooltip: {
					enabled: false,
				},
				plotOptions: {
					radialBar: {
						hollow: {
							margin: 0,
							size: '75%'
						},
						track: {
							margin: 0
						},
						dataLabels: {
							show: false
						}
					}
				},
				colors: [tabler.getColor("blue")],
				series: [35],
			})).render();
		});
			</script>
</div>
				<div class="col">
					<div>Today's Earning: $4,262.40</div>
					<div class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/trending-up -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-inline text-green icon-3"><path d="M3 17l6 -6l4 4l8 -8" /><path d="M14 7l7 0l0 7" /></svg>
 +5% more than yesterday</div>
				</div>
			</div>
		</div>
<div id="chart-development-activity"></div>
<script>
		document.addEventListener("DOMContentLoaded", function () {
		window.ApexCharts && (new ApexCharts(document.getElementById('chart-development-activity'), {
			chart: {
				type: "area",
				fontFamily: 'inherit',
				height: 192,
				sparkline: {
					enabled: true
				},
				animations: {
					enabled: false
				},
			},
			dataLabels: {
				enabled: false,
			},
			fill: {
				opacity: .16,
				type: 'solid'
			},
			stroke: {
				width: 2,
				lineCap: "round",
				curve: "smooth",
			},
			series: [{
				name: "Purchases",
				data: [3, 5, 4, 6, 7, 5, 6, 8, 24, 7, 12, 5, 6, 3, 8, 4, 14, 30, 17, 19, 15, 14, 25, 32, 40, 55, 60, 48, 52, 70]
			}],
			tooltip: {
				theme: 'dark'
			},
			grid: {
				strokeDashArray: 4,
			},
			xaxis: {
				labels: {
					padding: 0,
				},
				tooltip: {
					enabled: false
				},
				axisBorder: {
					show: false,
				},
				type: 'datetime',
			},
			yaxis: {
				labels: {
					padding: 4
				},
			},
			labels: [
				'2020-06-20', '2020-06-21', '2020-06-22', '2020-06-23', '2020-06-24', '2020-06-25', '2020-06-26', '2020-06-27', '2020-06-28', '2020-06-29', '2020-06-30', '2020-07-01', '2020-07-02', '2020-07-03', '2020-07-04', '2020-07-05', '2020-07-06', '2020-07-07', '2020-07-08', '2020-07-09', '2020-07-10', '2020-07-11', '2020-07-12', '2020-07-13', '2020-07-14', '2020-07-15', '2020-07-16', '2020-07-17', '2020-07-18', '2020-07-19'
			],
			colors: [tabler.getColor("primary")],
			legend: {
				show: false,
			},
			point: {
				show: false
			},
		})).render();
	});
	</script>
	</div>
	<div class="card-table table-responsive">
		<table class="table table-vcenter">
			<thead>
			<tr>
				<th>User</th>
				<th>Commit</th>
				<th>Date</th>
			</tr>
			</thead>
			<tbody>
			<tr>
				<td class="w-1">
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/000m.jpg)"></span>
				</td>
				<td class="td-truncate">
					<div class="text-truncate">
						Fix dart Sass compatibility (#29755)
					</div>
				</td>
				<td class="text-nowrap text-secondary">28 Nov 2019</td>
			</tr>
			<tr>
				<td class="w-1">
<span class="avatar avatar-sm">JL</span>
				</td>
				<td class="td-truncate">
					<div class="text-truncate">
						Change deprecated html tags to text decoration classes (#29604)
					</div>
				</td>
				<td class="text-nowrap text-secondary">27 Nov 2019</td>
			</tr>
			<tr>
				<td class="w-1">
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/002m.jpg)"></span>
				</td>
				<td class="td-truncate">
					<div class="text-truncate">
						justify-content:between ⇒ justify-content:space-between (#29734)
					</div>
				</td>
				<td class="text-nowrap text-secondary">26 Nov 2019</td>
			</tr>
			<tr>
				<td class="w-1">
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/003m.jpg)"></span>
				</td>
				<td class="td-truncate">
					<div class="text-truncate">
						Update change-version.js (#29736)
					</div>
				</td>
				<td class="text-nowrap text-secondary">26 Nov 2019</td>
			</tr>
			<tr>
				<td class="w-1">
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/000f.jpg)"></span>
				</td>
				<td class="td-truncate">
					<div class="text-truncate">
						Regenerate package-lock.json (#29730)
					</div>
				</td>
				<td class="text-nowrap text-secondary">25 Nov 2019</td>
			</tr>
			</tbody>
		</table>
	</div>
</div>
	</div>
	<div class="col-12">
<div class="card card-md sticky-top">
	<div class="card-stamp card-stamp-lg">
		<div class="card-stamp-icon bg-primary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/ghost -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 11a7 7 0 0 1 14 0v7a1.78 1.78 0 0 1 -3.1 1.4a1.65 1.65 0 0 0 -2.6 0a1.65 1.65 0 0 1 -2.6 0a1.65 1.65 0 0 0 -2.6 0a1.78 1.78 0 0 1 -3.1 -1.4v-7" /><path d="M10 10l.01 0" /><path d="M14 10l.01 0" /><path d="M10 14a3.5 3.5 0 0 0 4 0" /></svg>
		</div>
	</div>
	<div class="card-body">
		<div class="row align-items-center">
			<div class="col-10">
				<h3 class="h1">Tabler Icons</h3>
				<div class="markdown text-secondary">
					All icons come from the Tabler Icons set and are MIT-licensed. Visit
					<a href="https://tabler.io/icons" target="_blank" rel="noopener">tabler.io/icons</a>,
					download any of the 5844 icons in SVG, PNG or&nbsp;React and use them in your favourite design tools.
				</div>
				<div class="mt-3">
					<a href="https://tabler.io/icons" class="btn btn-primary" target="_blank" rel="noopener">
	<!-- Download SVG icon from http://tabler.io/icons/icon/download -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2" /><path d="M7 11l5 5l5 -5" /><path d="M12 4l0 12" /></svg>
						Download icons
					</a>
				</div>
			</div>
		</div>
	</div>
</div>
	</div>
	<div class="col-md-12 col-lg-8">
		<div class="card">
	<div class="card-header">
		<h3 class="card-title">Most Visited Pages</h3>
	</div>
	<div class="card-table table-responsive">
		<table class="table table-vcenter">
			<thead>
			<tr>
				<th>Page name</th>
				<th>Visitors</th>
				<th>Unique</th>
				<th colspan="2">Bounce rate</th>
			</tr>
			</thead>
			<tr>
				<td>
					/
					<a href="#" class="ms-1" aria-label="Open website">
	<!-- Download SVG icon from http://tabler.io/icons/icon/link -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>
</a>
				</td>
				<td class="text-secondary">4,896</td>
				<td class="text-secondary">3,654</td>
				<td class="text-secondary">82.54%</td>
				<td class="text-end w-1">
	<div class="chart-sparkline chart-sparkline-sm" id="sparkline-bounce-rate-1"></div>
	<script>
				document.addEventListener("DOMContentLoaded", function () {
			window.ApexCharts && (new ApexCharts(document.getElementById('sparkline-bounce-rate-1'), {
				chart: {
					type: "line",
					fontFamily: 'inherit',
					height: 24,
					animations: {
						enabled: false
					},
					sparkline: {
						enabled: true
					},
				},
				tooltip: {
					enabled: false,
				},
				stroke: {
					width: 2,
					lineCap: "round",
				},
				series: [{
					color: tabler.getColor("primary"),
					data: [17, 24, 20, 10, 5, 1, 4, 18, 13]
				}],
			})).render();
		});
			</script>
				</td>
			</tr>
			<tr>
				<td>
					/form-elements.html
					<a href="#" class="ms-1" aria-label="Open website">
	<!-- Download SVG icon from http://tabler.io/icons/icon/link -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>
</a>
				</td>
				<td class="text-secondary">3,652</td>
				<td class="text-secondary">3,215</td>
				<td class="text-secondary">76.29%</td>
				<td class="text-end w-1">
	<div class="chart-sparkline chart-sparkline-sm" id="sparkline-bounce-rate-2"></div>
	<script>
				document.addEventListener("DOMContentLoaded", function () {
			window.ApexCharts && (new ApexCharts(document.getElementById('sparkline-bounce-rate-2'), {
				chart: {
					type: "line",
					fontFamily: 'inherit',
					height: 24,
					animations: {
						enabled: false
					},
					sparkline: {
						enabled: true
					},
				},
				tooltip: {
					enabled: false,
				},
				stroke: {
					width: 2,
					lineCap: "round",
				},
				series: [{
					color: tabler.getColor("primary"),
					data: [13, 11, 19, 22, 12, 7, 14, 3, 21]
				}],
			})).render();
		});
			</script>
				</td>
			</tr>
			<tr>
				<td>
					/index.html
					<a href="#" class="ms-1" aria-label="Open website">
	<!-- Download SVG icon from http://tabler.io/icons/icon/link -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>
</a>
				</td>
				<td class="text-secondary">3,256</td>
				<td class="text-secondary">2,865</td>
				<td class="text-secondary">72.65%</td>
				<td class="text-end w-1">
	<div class="chart-sparkline chart-sparkline-sm" id="sparkline-bounce-rate-3"></div>
	<script>
				document.addEventListener("DOMContentLoaded", function () {
			window.ApexCharts && (new ApexCharts(document.getElementById('sparkline-bounce-rate-3'), {
				chart: {
					type: "line",
					fontFamily: 'inherit',
					height: 24,
					animations: {
						enabled: false
					},
					sparkline: {
						enabled: true
					},
				},
				tooltip: {
					enabled: false,
				},
				stroke: {
					width: 2,
					lineCap: "round",
				},
				series: [{
					color: tabler.getColor("primary"),
					data: [10, 13, 10, 4, 17, 3, 23, 22, 19]
				}],
			})).render();
		});
			</script>
				</td>
			</tr>
			<tr>
				<td>
					/icons.html
					<a href="#" class="ms-1" aria-label="Open website">
	<!-- Download SVG icon from http://tabler.io/icons/icon/link -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>
</a>
				</td>
				<td class="text-secondary">986</td>
				<td class="text-secondary">865</td>
				<td class="text-secondary">44.89%</td>
				<td class="text-end w-1">
	<div class="chart-sparkline chart-sparkline-sm" id="sparkline-bounce-rate-4"></div>
	<script>
				document.addEventListener("DOMContentLoaded", function () {
			window.ApexCharts && (new ApexCharts(document.getElementById('sparkline-bounce-rate-4'), {
				chart: {
					type: "line",
					fontFamily: 'inherit',
					height: 24,
					animations: {
						enabled: false
					},
					sparkline: {
						enabled: true
					},
				},
				tooltip: {
					enabled: false,
				},
				stroke: {
					width: 2,
					lineCap: "round",
				},
				series: [{
					color: tabler.getColor("primary"),
					data: [6, 15, 13, 13, 5, 7, 17, 20, 19]
				}],
			})).render();
		});
			</script>
				</td>
			</tr>
			<tr>
				<td>
					/docs/
					<a href="#" class="ms-1" aria-label="Open website">
	<!-- Download SVG icon from http://tabler.io/icons/icon/link -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>
</a>
				</td>
				<td class="text-secondary">912</td>
				<td class="text-secondary">822</td>
				<td class="text-secondary">41.12%</td>
				<td class="text-end w-1">
	<div class="chart-sparkline chart-sparkline-sm" id="sparkline-bounce-rate-5"></div>
	<script>
				document.addEventListener("DOMContentLoaded", function () {
			window.ApexCharts && (new ApexCharts(document.getElementById('sparkline-bounce-rate-5'), {
				chart: {
					type: "line",
					fontFamily: 'inherit',
					height: 24,
					animations: {
						enabled: false
					},
					sparkline: {
						enabled: true
					},
				},
				tooltip: {
					enabled: false,
				},
				stroke: {
					width: 2,
					lineCap: "round",
				},
				series: [{
					color: tabler.getColor("primary"),
					data: [2, 11, 15, 14, 21, 20, 8, 23, 18, 14]
				}],
			})).render();
		});
			</script>
				</td>
			</tr>
			<tr>
				<td>
					/accordion.html
					<a href="#" class="ms-1" aria-label="Open website">
	<!-- Download SVG icon from http://tabler.io/icons/icon/link -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 15l6 -6" /><path d="M11 6l.463 -.536a5 5 0 0 1 7.071 7.072l-.534 .464" /><path d="M13 18l-.397 .534a5.068 5.068 0 0 1 -7.127 0a4.972 4.972 0 0 1 0 -7.071l.524 -.463" /></svg>
</a>
				</td>
				<td class="text-secondary">855</td>
				<td class="text-secondary">798</td>
				<td class="text-secondary">32.65%</td>
				<td class="text-end w-1">
	<div class="chart-sparkline chart-sparkline-sm" id="sparkline-bounce-rate-6"></div>
	<script>
				document.addEventListener("DOMContentLoaded", function () {
			window.ApexCharts && (new ApexCharts(document.getElementById('sparkline-bounce-rate-6'), {
				chart: {
					type: "line",
					fontFamily: 'inherit',
					height: 24,
					animations: {
						enabled: false
					},
					sparkline: {
						enabled: true
					},
				},
				tooltip: {
					enabled: false,
				},
				stroke: {
					width: 2,
					lineCap: "round",
				},
				series: [{
					color: tabler.getColor("primary"),
					data: [22, 12, 7, 14, 3, 21, 8, 23, 18, 14]
				}],
			})).render();
		});
			</script>
				</td>
			</tr>
		</table>
	</div>
</div>
	</div>
	<div class="col-md-6 col-lg-4">
		<a href="https://github.com/sponsors/codecalm" class="card card-sponsor" target="_blank" rel="noopener" style="background-image: url(./static/static/sponsor-banner-homepage.svg)" aria-label="Sponsor Tabler!">
	<div class="card-body"></div>
</a>
	</div>
	<div class="col-md-6 col-lg-4">
<div class="card">
	<div class="card-header">
		<h3 class="card-title">Social Media Traffic</h3>
	</div>
	<table class="table card-table table-vcenter">
		<thead>
		<tr>
			<th>Network</th>
			<th colspan="2">Visitors</th>
		</tr>
		</thead>
		<tbody>
		<tr>
			<td>Instagram</td>
			<td>3,550</td>
			<td class="w-50">
				<div class="progress progress-xs">
					<div class="progress-bar bg-primary" style="width: 71%"></div>
				</div>
			</td>
		</tr>
		<tr>
			<td>Twitter</td>
			<td>1,798</td>
			<td class="w-50">
				<div class="progress progress-xs">
					<div class="progress-bar bg-primary" style="width: 35.96%"></div>
				</div>
			</td>
		</tr>
		<tr>
			<td>Facebook</td>
			<td>1,245</td>
			<td class="w-50">
				<div class="progress progress-xs">
					<div class="progress-bar bg-primary" style="width: 24.9%"></div>
				</div>
			</td>
		</tr>
		<tr>
			<td>TikTok</td>
			<td>986</td>
			<td class="w-50">
				<div class="progress progress-xs">
					<div class="progress-bar bg-primary" style="width: 19.72%"></div>
				</div>
			</td>
		</tr>
		<tr>
			<td>Pinterest</td>
			<td>854</td>
			<td class="w-50">
				<div class="progress progress-xs">
					<div class="progress-bar bg-primary" style="width: 17.080000000000002%"></div>
				</div>
			</td>
		</tr>
		<tr>
			<td>VK</td>
			<td>650</td>
			<td class="w-50">
				<div class="progress progress-xs">
					<div class="progress-bar bg-primary" style="width: 13%"></div>
				</div>
			</td>
		</tr>
		<tr>
			<td>Pinterest</td>
			<td>420</td>
			<td class="w-50">
				<div class="progress progress-xs">
					<div class="progress-bar bg-primary" style="width: 8.4%"></div>
				</div>
			</td>
		</tr>
		</tbody>
	</table>
</div>
	</div>
	<div class="col-md-12 col-lg-8">
		<div class="card">
	<div class="card-header">
		<h3 class="card-title">Tasks</h3>
	</div>
	<div class="table-responsive">
		<table class="table card-table table-vcenter">
			<tr>
				<td class="w-1 pe-0">
					<input type="checkbox" class="form-check-input m-0 align-middle" aria-label="Select task" checked >
				</td>
				<td class="w-100">
					<a href="#" class="text-reset">Extend the data model.</a>
				</td>
				<td class="text-nowrap text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/calendar -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" /><path d="M16 3v4" /><path d="M8 3v4" /><path d="M4 11h16" /><path d="M11 15h1" /><path d="M12 15v3" /></svg>
					December 11, 2024
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/check -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 12l5 5l10 -10" /></svg>
						2/7
					</a>
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/message -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
 3</a>
				</td>
				<td>
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/000m.jpg)"></span>
				</td>
			</tr>
			<tr>
				<td class="w-1 pe-0">
					<input type="checkbox" class="form-check-input m-0 align-middle" aria-label="Select task" >
				</td>
				<td class="w-100">
					<a href="#" class="text-reset">Verify the event flow.</a>
				</td>
				<td class="text-nowrap text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/calendar -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" /><path d="M16 3v4" /><path d="M8 3v4" /><path d="M4 11h16" /><path d="M11 15h1" /><path d="M12 15v3" /></svg>
					October 20, 2024
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/check -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 12l5 5l10 -10" /></svg>
						0/5
					</a>
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/message -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
 0</a>
				</td>
				<td>
<span class="avatar avatar-sm">JL</span>
				</td>
			</tr>
			<tr>
				<td class="w-1 pe-0">
					<input type="checkbox" class="form-check-input m-0 align-middle" aria-label="Select task" >
				</td>
				<td class="w-100">
					<a href="#" class="text-reset">Database backup and maintenance</a>
				</td>
				<td class="text-nowrap text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/calendar -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" /><path d="M16 3v4" /><path d="M8 3v4" /><path d="M4 11h16" /><path d="M11 15h1" /><path d="M12 15v3" /></svg>
					October 20, 2024
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/check -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 12l5 5l10 -10" /></svg>
						0/5
					</a>
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/message -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
 0</a>
				</td>
				<td>
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/002m.jpg)"></span>
				</td>
			</tr>
			<tr>
				<td class="w-1 pe-0">
					<input type="checkbox" class="form-check-input m-0 align-middle" aria-label="Select task" checked >
				</td>
				<td class="w-100">
					<a href="#" class="text-reset">Identify the implementation team.</a>
				</td>
				<td class="text-nowrap text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/calendar -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" /><path d="M16 3v4" /><path d="M8 3v4" /><path d="M4 11h16" /><path d="M11 15h1" /><path d="M12 15v3" /></svg>
					January 14, 2025
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/check -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 12l5 5l10 -10" /></svg>
						6/10
					</a>
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/message -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
 12</a>
				</td>
				<td>
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/003m.jpg)"></span>
				</td>
			</tr>
			<tr>
				<td class="w-1 pe-0">
					<input type="checkbox" class="form-check-input m-0 align-middle" aria-label="Select task" >
				</td>
				<td class="w-100">
					<a href="#" class="text-reset">Define users and workflow</a>
				</td>
				<td class="text-nowrap text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/calendar -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" /><path d="M16 3v4" /><path d="M8 3v4" /><path d="M4 11h16" /><path d="M11 15h1" /><path d="M12 15v3" /></svg>
					October 20, 2024
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/check -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 12l5 5l10 -10" /></svg>
						0/5
					</a>
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/message -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
 0</a>
				</td>
				<td>
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/000f.jpg)"></span>
				</td>
			</tr>
			<tr>
				<td class="w-1 pe-0">
					<input type="checkbox" class="form-check-input m-0 align-middle" aria-label="Select task" checked >
				</td>
				<td class="w-100">
					<a href="#" class="text-reset">Check Pull Requests</a>
				</td>
				<td class="text-nowrap text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/calendar -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" /><path d="M16 3v4" /><path d="M8 3v4" /><path d="M4 11h16" /><path d="M11 15h1" /><path d="M12 15v3" /></svg>
					January 16, 2025
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/check -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M5 12l5 5l10 -10" /></svg>
						2/9
					</a>
				</td>
				<td class="text-nowrap">
					<a href="#" class="text-secondary">
	<!-- Download SVG icon from http://tabler.io/icons/icon/message -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M8 9h8" /><path d="M8 13h6" /><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" /></svg>
 3</a>
				</td>
				<td>
<span class="avatar avatar-sm" style="background-image: url(./static/static/avatars/001f.jpg)"></span>
				</td>
			</tr>
		</table>
	</div>
</div>
	</div>
	<div class="col-12">
		<div class="card">
	<div class="card-header">
		<h3 class="card-title">Invoices</h3>
	</div>
	<div class="card-body border-bottom py-3">
		<div class="d-flex">
			<div class="text-secondary">
				Show
				<div class="mx-2 d-inline-block">
					<input type="text" class="form-control form-control-sm" value="8" size="3" aria-label="Invoices count">
				</div>
				entries
			</div>
			<div class="ms-auto text-secondary">
				Search:
				<div class="ms-2 d-inline-block">
					<input type="text" class="form-control form-control-sm" aria-label="Search invoice">
				</div>
			</div>
		</div>
	</div>
	<div class="table-responsive">
		<table class="table card-table table-vcenter text-nowrap datatable">
			<thead>
			<tr>
				<th class="w-1"><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select all invoices"></th>
				<th class="w-1">No. 
	<!-- Download SVG icon from http://tabler.io/icons/icon/chevron-up -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-sm icon-thick icon-2"><path d="M6 15l6 -6l6 6" /></svg>
</th>
				<th>Invoice Subject</th>
				<th>Client</th>
				<th>VAT No.</th>
				<th>Created</th>
				<th>Status</th>
				<th>Price</th>
				<th></th>
			</tr>
			</thead>
			<tbody>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001401</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">Design Works</a></td>
				<td>
					<span class="flag flag-xs flag-country-us me-2"></span>
Carlson Limited
				</td>
				<td>
					87956621
				</td>
				<td>
					15 Dec 2017
				</td>
				<td>
					<span class="badge bg-success me-1"></span> Paid
				</td>
				<td>$887</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001402</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">UX Wireframes</a></td>
				<td>
					<span class="flag flag-xs flag-country-gb me-2"></span>
Adobe
				</td>
				<td>
					87956421
				</td>
				<td>
					12 Apr 2017
				</td>
				<td>
					<span class="badge bg-warning me-1"></span> Pending
				</td>
				<td>$1200</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001403</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">New Dashboard</a></td>
				<td>
					<span class="flag flag-xs flag-country-de me-2"></span>
Bluewolf
				</td>
				<td>
					87952621
				</td>
				<td>
					23 Oct 2017
				</td>
				<td>
					<span class="badge bg-warning me-1"></span> Pending
				</td>
				<td>$534</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001404</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">Landing Page</a></td>
				<td>
					<span class="flag flag-xs flag-country-br me-2"></span>
Salesforce
				</td>
				<td>
					87953421
				</td>
				<td>
					2 Sep 2017
				</td>
				<td>
					<span class="badge bg-secondary me-1"></span> Due in 2 Weeks
				</td>
				<td>$1500</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001405</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">Marketing Templates</a></td>
				<td>
					<span class="flag flag-xs flag-country-pl me-2"></span>
Printic
				</td>
				<td>
					87956621
				</td>
				<td>
					29 Jan 2018
				</td>
				<td>
					<span class="badge bg-danger me-1"></span> Paid Today
				</td>
				<td>$648</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001406</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">Sales Presentation</a></td>
				<td>
					<span class="flag flag-xs flag-country-br me-2"></span>
Tabdaq
				</td>
				<td>
					87956621
				</td>
				<td>
					4 Feb 2018
				</td>
				<td>
					<span class="badge bg-secondary me-1"></span> Due in 3 Weeks
				</td>
				<td>$300</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001407</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">Logo & Print</a></td>
				<td>
					<span class="flag flag-xs flag-country-us me-2"></span>
Apple
				</td>
				<td>
					87956621
				</td>
				<td>
					22 Mar 2018
				</td>
				<td>
					<span class="badge bg-success me-1"></span> Paid Today
				</td>
				<td>$2500</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			<tr>
				<td><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select invoice"></td>
				<td><span class="text-secondary">001408</span></td>
				<td><a href="invoice.html" class="text-reset" tabindex="-1">Icons</a></td>
				<td>
					<span class="flag flag-xs flag-country-pl me-2"></span>
Tookapic
				</td>
				<td>
					87956621
				</td>
				<td>
					13 May 2018
				</td>
				<td>
					<span class="badge bg-success me-1"></span> Paid Today
				</td>
				<td>$940</td>
				<td class="text-end">
					<span class="dropdown">
						<button class="btn dropdown-toggle align-text-top" data-bs-boundary="viewport" data-bs-toggle="dropdown">Actions</button>
						<div class="dropdown-menu dropdown-menu-end">
		<a class="dropdown-item" href="#">
			Action
		</a>
		<a class="dropdown-item" href="#">
			Another action
		</a>
</div>
					</span>
				</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div class="card-footer d-flex align-items-center">
		<p class="m-0 text-secondary">Showing <span>1</span> to <span>8</span> of <span>16</span> entries</p>
<ul class="pagination m-0 ms-auto">
   <li class="page-item disabled">
      <a class="page-link" href="#" tabindex="-1" aria-disabled="true">
	<!-- Download SVG icon from http://tabler.io/icons/icon/chevron-left -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M15 6l-6 6l6 6" /></svg>
 prev
      </a>
   </li>
   <li class="page-item"><a class="page-link" href="#">1</a></li>
   <li class="page-item active"><a class="page-link" href="#">2</a></li>
   <li class="page-item"><a class="page-link" href="#">3</a></li>
   <li class="page-item"><a class="page-link" href="#">4</a></li>
   <li class="page-item"><a class="page-link" href="#">5</a></li>
   <li class="page-item">
      <a class="page-link" href="#">
            next 
	<!-- Download SVG icon from http://tabler.io/icons/icon/chevron-right -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1"><path d="M9 6l6 6l-6 6" /></svg>
      </a>
   </li>
</ul>
	</div>
</div>
	</div>
</div>
			</div>
		</div>
		<footer class="footer footer-transparent d-print-none">
	<div class="container-xl">
		<div class="row text-center align-items-center flex-row-reverse">
			<div class="col-lg-auto ms-lg-auto">
				<ul class="list-inline list-inline-dots mb-0">
					<li class="list-inline-item"><a href="https://tabler.io/docs" target="_blank" class="link-secondary" rel="noopener">Documentation</a></li>
					<li class="list-inline-item"><a href="./license.html" class="link-secondary">License</a></li>
					<li class="list-inline-item"><a href="https://github.com/tabler/tabler" target="_blank" class="link-secondary" rel="noopener">Source code</a></li>
					<li class="list-inline-item">
						<a href="https://github.com/sponsors/codecalm" target="_blank" class="link-secondary" rel="noopener">
	<!-- Download SVG icon from http://tabler.io/icons/icon/heart -->
	<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-pink icon-inline icon-4"><path d="M19.5 12.572l-7.5 7.428l-7.5 -7.428a5 5 0 1 1 7.5 -6.566a5 5 0 1 1 7.5 6.572" /></svg>
							Sponsor
						</a>
					</li>
				</ul>
			</div>
			<div class="col-12 col-lg-auto mt-3 mt-lg-0">
				<ul class="list-inline list-inline-dots mb-0">
					<li class="list-inline-item">
						Copyright &copy; 2025
						<a href="." class="link-secondary">Tabler</a>.
						All rights reserved.
					</li>
					<li class="list-inline-item">
						<a href="./changelog.html" class="link-secondary" rel="noopener">
							v1.0.0
						</a>
					</li>
				</ul>
			</div>
		</div>
	</div>
</footer>
	</div>
</div>
	<!-- Libs JS -->
<script src="./static/dist/libs/apexcharts/dist/apexcharts.min.js?**********" defer></script>
			<script src="./static/dist/libs/jsvectormap/dist/jsvectormap.min.js?**********" defer></script>
			<script src="./static/dist/libs/jsvectormap/dist/maps/world.js?**********" defer></script>
			<script src="./static/dist/libs/jsvectormap/dist/maps/world-merc.js?**********" defer></script>
			<!-- Tabler Core -->
<script src="./static/dist/js/tabler.min.js?**********" defer></script>
<script src="./static/dist/js/demo.min.js?**********" defer></script>
</body>
</html>
