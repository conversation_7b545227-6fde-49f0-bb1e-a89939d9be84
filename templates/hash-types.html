{% extends "base.html" %}

{% block content %}
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <h2 class="page-title">
                    Supported Hash Types
                </h2>
                <div class="text-muted mt-1">
                    {{ hash_types|length }} hash types supported 
                    ({{ hash_types|selectattr('is_precomputed', 'eq', true)|list|length }} quick search supported)
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page-body">
    <div class="container-xl">
        <!-- Search and Filter Section - Streamlined -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-8">
                        <div class="input-icon">
                            <input type="text" id="searchInput" class="form-control" placeholder="Search hash types...">
                            <span class="input-icon-addon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                    <path d="M21 21l-6 -6"></path>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select id="filterSupport" class="form-select">
                            <option value="all">All Hash Types</option>
                            <option value="supported">Quick Search Supported</option>
                            <option value="not-supported">Not Supported</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hash Types Cards - Gọn hơn -->
        {% for category, items in hash_types|groupby('category') %}
        <div class="category-section mb-4" data-category="{{ category }}">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        {{ category }}
                        <span class="badge bg-blue ms-2">{{ items|list|length }}</span>
                    </h3>
                    <span class="text-muted small category-counter">
                        Showing {{ items|list|length }} items
                    </span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-vcenter card-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Hashcat ID</th>
                                    <th>Example</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for hash_type in items %}
                                <tr class="hash-row" data-supported="{{ 'true' if hash_type.is_precomputed else 'false' }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="hash-name">{{ hash_type.name }}</span>
                                            {% if hash_type.is_precomputed %}
                                            <span class="badge bg-green-lt ms-2">Quick Search</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-blue-lt">{{ hash_type.hashcat_id }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="text-muted text-truncate" style="max-width: 350px;">
                                                <code class="hash-example">{{ hash_type.example }}</code>
                                            </div>
                                            <button class="btn btn-sm btn-ghost-secondary ms-2 copy-hash" data-hash="{{ hash_type.example }}" title="Copy example">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-copy" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                    <path d="M8 8m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z"></path>
                                                    <path d="M16 8v-2a2 2 0 0 0 -2 -2h-8a2 2 0 0 0 -2 2v8a2 2 0 0 0 2 2h2"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const filterSupport = document.getElementById('filterSupport');
    const categorySections = document.querySelectorAll('.category-section');
    
    // Copy functionality
    document.querySelectorAll('.copy-hash').forEach(button => {
        button.addEventListener('click', function() {
            const hashText = this.dataset.hash;
            navigator.clipboard.writeText(hashText).then(() => {
                // Change button appearance temporarily
                const originalHTML = this.innerHTML;
                this.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-check" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M5 12l5 5l10 -10"></path>
                                  </svg>`;
                this.classList.add('text-success');
                
                setTimeout(() => {
                    this.innerHTML = originalHTML;
                    this.classList.remove('text-success');
                }, 1500);
            });
        });
    });

    function filterHashTypes() {
        const searchTerm = searchInput.value.toLowerCase();
        const supportFilter = filterSupport.value;

        categorySections.forEach(section => {
            let visibleCount = 0;
            const totalRows = section.querySelectorAll('.hash-row').length;
            const rows = section.querySelectorAll('.hash-row');
            
            rows.forEach(row => {
                const hashName = row.querySelector('.hash-name').textContent.toLowerCase();
                const isSupported = row.dataset.supported === 'true';
                
                let showRow = hashName.includes(searchTerm);
                
                // Apply support filter
                if (supportFilter === 'supported') {
                    showRow = showRow && isSupported;
                } else if (supportFilter === 'not-supported') {
                    showRow = showRow && !isSupported;
                }

                row.style.display = showRow ? '' : 'none';
                if (showRow) {
                    visibleCount++;
                }
            });

            // Show/hide entire category section
            section.style.display = visibleCount > 0 ? '' : 'none';
            
            // Update counter
            const counter = section.querySelector('.category-counter');
            if (counter) {
                if (visibleCount === totalRows) {
                    counter.textContent = `Showing ${totalRows} items`;
                } else {
                    counter.textContent = `Showing ${visibleCount} of ${totalRows} items`;
                }
            }
        });
    }

    // Add event listeners for search and filter
    searchInput.addEventListener('input', filterHashTypes);
    filterSupport.addEventListener('change', filterHashTypes);
    
    // Add clear button for search
    searchInput.addEventListener('keyup', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            filterHashTypes();
        }
    });
});
</script>
{% endblock %}