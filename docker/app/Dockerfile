FROM ubuntu:22.04
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    build-essential \
    git \
    pkg-config \
    libssl-dev \
    libgmp-dev \
    libpcap-dev \
    libbz2-dev \
    opencl-headers \
    ocl-icd-opencl-dev \
    && rm -rf /var/lib/apt/lists/*

# Install hashcat
RUN git clone https://github.com/hashcat/hashcat.git && \
    cd hashcat && \
    make && \
    make install && \
    cd .. && \
    rm -rf hashcat

# Install THC-Hydra
RUN git clone https://github.com/vanhauser-thc/thc-hydra.git && \
    cd thc-hydra && \
    ./configure && \
    make && \
    make install && \
    cd .. && \
    rm -rf thc-hydra

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/uploads /app/wordlists

EXPOSE 5050

# Set Python to run in unbuffered mode
ENV PYTHONUNBUFFERED=1
