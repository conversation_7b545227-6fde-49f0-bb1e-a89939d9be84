#!/bin/bash

# Cập nhật hệ thống
sudo apt-get update -y

# Cài đặt các gói cần thiết
sudo apt-get install apt-transport-https ca-certificates curl software-properties-common -y

# Thêm kho chứa Docker GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Thêm kho chứa Docker vào danh sách nguồn phần mềm
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Cập nhật lại nguồn phần mềm
sudo apt-get update -y

# Cài đặt Docker
sudo apt-get install docker-ce docker-ce-cli containerd.io -y

# Khởi động Docker và thêm vào systemd để chạy tự động khi khởi động
sudo systemctl start docker
sudo systemctl enable docker

# Kiểm tra phiên bản Docker
docker --version

# Cài đặt Docker Compose
DOCKER_COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | jq -r .tag_name)
sudo curl -L "https://github.com/docker/compose/releases/download/$DOCKER_COMPOSE_VERSION/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Cấp quyền thực thi cho docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Kiểm tra phiên bản Docker Compose
docker-compose --version

echo "Docker và Docker Compose đã được cài đặt thành công."
