#!/bin/bash
set -e

# Đợi MongoDB khởi động
echo "Waiting for MongoDB to start..."
sleep 10

# # Kiểm tra xem collection đã tồn tại chưa
# if ! mongosh --quiet --eval "db.getSiblingDB('crackmaster_db').hash_types.countDocuments({})" | grep -q "^0$"; then
#     echo "hash_types collection already has data, skipping restore"
#     exit 0
# fi
# Drop the existing hash_types collection to avoid duplicate key errors
mongosh --host localhost --port 27017 --eval "db.getSiblingDB('crackmaster_db').hash_types.drop()"

# Restore collection hash_types từ dump
# mongorestore --host localhost --db crackmaster_db --collection hash_types /tmp/mongo_dump/hash_types.bson
mongoimport --host localhost --port 27017 --db crackmaster_db --collection hash_types --file /tmp/mongo_dump/hash_types.json --jsonArray

echo "Database restore completed"
